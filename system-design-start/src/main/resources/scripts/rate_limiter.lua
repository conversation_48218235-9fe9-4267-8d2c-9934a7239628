-- Redis限流Lua脚本
-- KEYS[1]: 限流键名
-- ARGV[1]: 限流阈值
-- ARGV[2]: 时间窗口(秒)
-- 返回值: 1=允许, 0=限流

local key = KEYS[1]
local limit = tonumber(ARGV[1])
local window = tonumber(ARGV[2])

-- 获取当前计数
local current = redis.call('get', key)
if current and tonumber(current) >= limit then
    -- 超过限流阈值
    return 0
end

-- 递增计数
current = redis.call('incr', key)
if tonumber(current) == 1 then
    -- 第一次访问，设置过期时间
    redis.call('expire', key, window)
end

-- 未超过阈值，允许访问
return 1 