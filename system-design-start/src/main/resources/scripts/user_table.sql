-- 用户表，针对100亿用户场景按用户名分片设计，采用逻辑删除
CREATE TABLE IF NOT EXISTS `t_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(30) NOT NULL COMMENT '用户名',
  `username_hash` varchar(32) NOT NULL COMMENT '用户名哈希值',
  `shard_id` int(11) NOT NULL COMMENT '分片ID',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username_shard` (`username`, `shard_id`),
  UNIQUE KEY `uk_username_hash_shard` (`username_hash`, `shard_id`),
  KEY `idx_shard_id` (`shard_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 说明：
-- 1. 基于分片设计，实际生产环境中可能会基于此表结构分库分表
-- 2. 每个用户名会计算对应的分片ID，存储在shard_id字段
-- 3. 基于用户名和分片ID创建唯一索引，确保唯一性
-- 4. 同时保存用户名的哈希值，支持基于哈希值的查询 