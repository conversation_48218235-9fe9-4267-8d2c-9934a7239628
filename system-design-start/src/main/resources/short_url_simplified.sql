-- 创建短链接表
CREATE TABLE IF NOT EXISTS `t_short_url` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `short_code` varchar(10) NOT NULL COMMENT '短链接码',
  `long_url` varchar(2048) NOT NULL COMMENT '原始长链接',
  `long_url_md5` varchar(32) NOT NULL COMMENT '长链接MD5值',
  `user_id` bigint(20) DEFAULT NULL COMMENT '创建用户ID',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_short_code` (`short_code`),
  UNIQUE KEY `uk_long_url_md5` (`long_url_md5`, `is_deleted`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_long_url_md5` (`long_url_md5`) COMMENT 'MD5索引，用于快速查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短链接映射表'; 