server:
  port: 8080
  # 优化Tomcat配置以支持高并发
  tomcat:
    max-threads: 1000                 # 最大工作线程数
    max-connections: 20000            # 最大连接数
    accept-count: 1000                # 等待队列长度
    min-spare-threads: 100            # 最小空闲线程数
    connection-timeout: 5000          # 连接超时时间
    max-http-form-post-size: 2MB      # 最大表单提交大小

# RocketMQ配置
rocketmq:
  name-server: 127.0.0.1:9876
  producer:
    # 生产者组名
    group: bullet-producer-group
    # 发送消息超时时间
    send-message-timeout: 3000
    # 同步发送消息失败重试次数
    retry-times-when-send-failed: 3
    # 异步发送消息失败重试次数
    retry-times-when-send-async-failed: 3
    # 消息体最大值
    max-message-size: 4096
    # 压缩消息阈值
    compress-message-body-threshold: 4096
    # 是否开启消息轨迹
    enable-msg-trace: true
    # 消息轨迹主题
    customized-trace-topic: BULLET_MSG_TRACE
  consumer:
    # 一次最大拉取消息数量
    pull-batch-size: 1000
    # 是否开启消息轨迹
    enable-msg-trace: true
    # 消息消费失败最大重试次数
    max-reconsume-times: 3
    # 消费模式，广播或集群
    # 集群模式：同一个消费组的多个节点平均分配消息
    # 广播模式：同一个消息会被所有消费者实例消费一次
    broadcasting: false

spring:
  application:
    name: short-url-service
  
  # 允许bean覆盖
  main:
    allow-bean-definition-overriding: true
  
  # 数据库配置
  datasource:
    url: **************************************************************************************************************
    username: root
    password: xiaobiao.
    driver-class-name: com.mysql.cj.jdbc.Driver
    # 连接池配置
    hikari:
      maximum-pool-size: 100          # 最大连接数
      minimum-idle: 20                # 最小空闲连接数
      connection-timeout: 30000       # 连接超时，毫秒
      idle-timeout: 600000            # 连接空闲超时，毫秒
      max-lifetime: 1800000           # 连接最大生命周期，毫秒
  
  # Redis配置
  redis:
    host: 127.0.0.1
    port: 6379
    database: 0
    timeout: 10000
    lettuce:
      pool:
        max-active: 100               # 最大连接数
        max-idle: 50                  # 最大空闲连接数
        min-idle: 20                  # 最小空闲连接数
        max-wait: 3000                # 最大等待时间，毫秒
    password: xiaobiao.
  # Sentinel Dashboard配置
  cloud:
    sentinel:
      transport:
        dashboard: localhost:10086
        port: 10086
      eager: true  # 取消懒加载，确保应用启动时就注册到dashboard

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:/mapper/**/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    cache-enabled: false
  sql:
    print: true
  global-config:
    db-config:
      logic-delete-field: isDeleted
      logic-delete-value: true
      logic-not-delete-value: false

# 短链接配置
short-url:
  domain: http://localhost:8080

# 线程池配置
thread-pool:
  core-size: 50                       # 核心线程数
  max-size: 200                       # 最大线程数
  queue-capacity: 1000                # 队列容量
  keep-alive-seconds: 60              # 线程最大空闲时间，秒
  thread-name-prefix: short-url-async-

# Snowflake配置
snowflake:
  worker-id: -1                       # 工作节点ID，-1表示自动分配
  datacenter-id: -1                   # 数据中心ID，-1表示自动分配

# 用户名相关配置
username:
  # 分片配置
  shard:
    count: 128                        # 用户名分片数量
  
  # 布隆过滤器配置  
  bloomfilter:
    expected-insertions: 1000000000  # 预期插入元素数量 (100亿)
    fpp: 0.001                        # 误判率

  # 缓存配置
  cache:
    max-size: 10000000                # 本地缓存最大容量
    expire-after-write: 3600          # 本地缓存过期时间(秒)
    redis:
      expire-days: 7                  # Redis缓存过期时间(天)

# 分片配置
sharding:
  database:
    count: 32                         # 分库数量
    prefix: db_                       # 数据库名前缀
  table:
    count: 128                        # 分表数量
    prefix: t_                        # 表名前缀