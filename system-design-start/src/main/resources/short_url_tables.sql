-- 创建短链接表
CREATE TABLE IF NOT EXISTS `t_short_url` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `short_code` varchar(10) NOT NULL COMMENT '短链接码',
  `long_url` varchar(2048) NOT NULL COMMENT '原始长链接',
  `user_id` bigint(20) DEFAULT NULL COMMENT '创建用户ID',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_short_code` (`short_code`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短链接映射表';

-- 创建访问日志表
CREATE TABLE IF NOT EXISTS `t_access_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `short_code` varchar(10) NOT NULL COMMENT '短链接码',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `ip` varchar(64) DEFAULT NULL COMMENT '访问者IP',
  `device_type` varchar(20) DEFAULT NULL COMMENT '设备类型',
  `browser_type` varchar(50) DEFAULT NULL COMMENT '浏览器类型',
  `os_type` varchar(50) DEFAULT NULL COMMENT '操作系统类型',
  `referer` varchar(1024) DEFAULT NULL COMMENT '来源URL',
  `access_time` datetime NOT NULL COMMENT '访问时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_short_code` (`short_code`),
  KEY `idx_access_time` (`access_time`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短链接访问日志表';

-- 创建访问统计表
CREATE TABLE IF NOT EXISTS `t_access_stats` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `short_code` varchar(10) NOT NULL COMMENT '短链接码',
  `access_date` date NOT NULL COMMENT '访问日期',
  `pv` bigint(20) NOT NULL DEFAULT '0' COMMENT '访问量',
  `uv` bigint(20) NOT NULL DEFAULT '0' COMMENT '独立访问用户数',
  `ip_count` bigint(20) NOT NULL DEFAULT '0' COMMENT '独立IP数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_short_code_date` (`short_code`, `access_date`),
  KEY `idx_access_date` (`access_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短链接访问统计表';

-- 创建黑名单表
CREATE TABLE IF NOT EXISTS `t_black_list` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type` varchar(20) NOT NULL COMMENT '黑名单类型：IP/DOMAIN/URL',
  `value` varchar(255) NOT NULL COMMENT '黑名单值',
  `reason` varchar(200) DEFAULT NULL COMMENT '原因',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_type_value` (`type`, `value`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='黑名单表'; 