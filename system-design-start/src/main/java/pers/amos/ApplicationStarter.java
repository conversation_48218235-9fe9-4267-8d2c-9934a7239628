package pers.amos;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

/**
 * <AUTHOR> wong
 * @create 2024/8/15 16:47
 * @Description
 */
@SpringBootApplication
@MapperScan(basePackages = {"pers.amos.dal.mapper"})
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableScheduling
@EnableWebSocket
public class ApplicationStarter {
    public static void main(String[] args) {
        SpringApplication.run(ApplicationStarter.class, args);
    }
}
