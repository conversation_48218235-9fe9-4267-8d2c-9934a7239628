package pers.amos.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Repository;
import pers.amos.dal.dataobject.Account;
import pers.amos.dal.mapper.AccountMapper;
import pers.amos.dal.repository.AccountRepository;

/**
 * <AUTHOR>
 * @createDate 2024-08-15 17:09:06
 */
@Repository
public class AccountRepositoryImpl extends ServiceImpl<AccountMapper, Account> implements AccountRepository {

}
