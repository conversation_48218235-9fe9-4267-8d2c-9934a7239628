package pers.amos.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

/**
 * <AUTHOR> wong
 * @create 2024/10/10 16:25
 * @Description
 */
@Data
@TableName(value ="inventory")
public class Inventory {
    @TableId(type = IdType.AUTO)
    private Long id;

    private String productNo;

    /**
     * 总数量
     */
    private Long totalCount;

    /**
     * 剩余数量
     */
    private Long remainCount;
}
