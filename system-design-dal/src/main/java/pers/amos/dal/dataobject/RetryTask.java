package pers.amos.dal.dataobject;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;

/**
 * @TableName account
 */
@TableName(value = "retry_task")
@Data
public class RetryTask implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 事务id
     */
    private String xid;

    /**
     * 事务名称
     */
    private String chainName;

    /**
     *
     */
    private String nodeName;

    private String clazz;

    private String method;

    private String param;

    private Integer retryCount;

    private String status;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}