package pers.amos.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import pers.amos.dal.entity.AccessStatsDO;

import java.util.Date;
import java.util.List;

/**
 * 访问统计Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface AccessStatsMapper extends BaseMapper<AccessStatsDO> {
    
    /**
     * 增加PV计数
     * 
     * @param shortCode 短链码
     * @param accessDate 访问日期
     * @param pvCount 增加的PV数量
     * @return 影响行数
     */
    @Update("UPDATE t_access_stats SET pv = pv + #{pvCount} WHERE short_code = #{shortCode} AND access_date = #{accessDate}")
    int increasePv(@Param("shortCode") String shortCode, @Param("accessDate") String accessDate, @Param("pvCount") int pvCount);
    
    /**
     * 增加UV计数
     * 
     * @param shortCode 短链码
     * @param accessDate 访问日期
     * @param uvCount 增加的UV数量
     * @return 影响行数
     */
    @Update("UPDATE t_access_stats SET uv = uv + #{uvCount} WHERE short_code = #{shortCode} AND access_date = #{accessDate}")
    int increaseUv(@Param("shortCode") String shortCode, @Param("accessDate") String accessDate, @Param("uvCount") int uvCount);
    
    /**
     * 增加IP计数
     * 
     * @param shortCode 短链码
     * @param accessDate 访问日期
     * @param ipCount 增加的IP数量
     * @return 影响行数
     */
    @Update("UPDATE t_access_stats SET ip_count = ip_count + #{ipCount} WHERE short_code = #{shortCode} AND access_date = #{accessDate}")
    int increaseIp(@Param("shortCode") String shortCode, @Param("accessDate") String accessDate, @Param("ipCount") int ipCount);
    
    /**
     * 根据短链码和日期范围查询访问统计
     * 
     * @param shortCode 短链码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 访问统计列表
     */
    @Select("SELECT * FROM t_access_stats WHERE short_code = #{shortCode} AND access_date BETWEEN #{startDate} AND #{endDate} ORDER BY access_date DESC")
    List<AccessStatsDO> selectByShortCodeAndDateRange(
            @Param("shortCode") String shortCode,
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate);
} 