package pers.amos.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import pers.amos.dal.entity.ShortUrlDO;

/**
 * 短链接Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ShortUrlMapper extends BaseMapper<ShortUrlDO> {

    /**
     * 根据短链码查询长链接
     *
     * @param shortCode 短链码
     * @return 长链接
     */
    @Select("SELECT * FROM t_short_url WHERE short_code = #{shortCode} AND (expire_time IS NULL OR expire_time > NOW()) AND is_deleted = 0")
    ShortUrlDO findByShortCode(@Param("shortCode") String shortCode);

    /**
     * 根据长链接查询短链接
     * 该方法性能较低，仅作为备用方法
     *
     * @param longUrl 长链接
     * @return 短链接
     */
    @Select("SELECT * FROM t_short_url WHERE long_url = #{longUrl} AND (expire_time IS NULL OR expire_time > NOW()) AND is_deleted = 0 LIMIT 1")
    ShortUrlDO findByLongUrl(@Param("longUrl") String longUrl);

    /**
     * 根据长链接MD5值查询短链接
     * 性能更高，应优先使用
     *
     * @param longUrlMd5 长链接MD5值
     * @return 短链接
     */
    @Select("SELECT * FROM t_short_url WHERE long_url_md5 = #{longUrlMd5} AND (expire_time IS NULL OR expire_time > NOW()) AND is_deleted = 0 LIMIT 1")
    ShortUrlDO findByLongUrlMd5(@Param("longUrlMd5") String longUrlMd5);
} 