package pers.amos.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import pers.amos.dal.entity.AccessLogDO;

import java.util.Date;
import java.util.List;

/**
 * 访问日志Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface AccessLogMapper extends BaseMapper<AccessLogDO> {
    
    /**
     * 查询指定时间范围内的访问记录
     * 
     * @param shortCode 短链码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 访问记录列表
     */
    @Select("SELECT * FROM t_access_log WHERE short_code = #{shortCode} AND access_time BETWEEN #{startTime} AND #{endTime} ORDER BY access_time DESC")
    List<AccessLogDO> findByShortCodeAndTimeRange(
            @Param("shortCode") String shortCode,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);
    
    /**
     * 统计指定时间范围内的访问IP数
     * 
     * @param shortCode 短链码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return IP数量
     */
    @Select("SELECT COUNT(DISTINCT ip) FROM t_access_log WHERE short_code = #{shortCode} AND access_time BETWEEN #{startTime} AND #{endTime}")
    int countDistinctIpByShortCodeAndTimeRange(
            @Param("shortCode") String shortCode,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);
    
    /**
     * 统计指定时间范围内的访问用户数
     * 
     * @param shortCode 短链码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 用户数量
     */
    @Select("SELECT COUNT(DISTINCT user_id) FROM t_access_log WHERE short_code = #{shortCode} AND access_time BETWEEN #{startTime} AND #{endTime} AND user_id IS NOT NULL")
    int countDistinctUserIdByShortCodeAndTimeRange(
            @Param("shortCode") String shortCode,
            @Param("startTime") Date startTime,
            @Param("endTime") Date endTime);
} 