package pers.amos.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import pers.amos.dal.entity.UserDO;

/**
 * 用户数据访问接口
 * 
 * <AUTHOR>
 */
public interface UserMapper extends BaseMapper<UserDO> {
    
    /**
     * 根据用户名查询用户（考虑分片）
     * 
     * @param username 用户名
     * @param shardId 分片ID
     * @return 用户信息
     */
    @Select("SELECT * FROM t_user WHERE username = #{username} AND shard_id = #{shardId} AND is_deleted = 0 LIMIT 1")
    UserDO findByUsername(@Param("username") String username, @Param("shardId") Integer shardId);
    
    /**
     * 检查用户名是否存在（考虑分片）
     * 
     * @param username 用户名
     * @param shardId 分片ID
     * @return 存在返回1，不存在返回0
     */
    @Select("SELECT COUNT(1) FROM t_user WHERE username = #{username} AND shard_id = #{shardId} AND is_deleted = 0")
    int checkUsernameExists(@Param("username") String username, @Param("shardId") Integer shardId);
    
    /**
     * 根据用户名哈希查询用户（考虑分片）
     * 
     * @param usernameHash 用户名哈希
     * @param shardId 分片ID
     * @return 用户信息
     */
    @Select("SELECT * FROM t_user WHERE username_hash = #{usernameHash} AND shard_id = #{shardId} AND is_deleted = 0 LIMIT 1")
    UserDO findByUsernameHash(@Param("usernameHash") String usernameHash, @Param("shardId") Integer shardId);
} 