package pers.amos.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短链接访问统计实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("t_access_stats")
public class AccessStatsDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 短链接码
     */
    private String shortCode;

    /**
     * 访问日期
     */
    private Date accessDate;

    /**
     * 访问量
     */
    private Long pv;

    /**
     * 独立访问用户数
     */
    private Long uv;

    /**
     * 独立IP数
     */
    private Long ipCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 