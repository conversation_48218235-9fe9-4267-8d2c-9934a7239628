package pers.amos.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短链接访问日志实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("t_access_log")
public class AccessLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 短链接码
     */
    private String shortCode;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 访问者IP
     */
    private String ip;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 浏览器类型
     */
    private String browserType;

    /**
     * 操作系统类型
     */
    private String osType;

    /**
     * 来源URL
     */
    private String referer;

    /**
     * 访问时间
     */
    private Date accessTime;

    /**
     * 创建时间
     */
    private Date createTime;
} 