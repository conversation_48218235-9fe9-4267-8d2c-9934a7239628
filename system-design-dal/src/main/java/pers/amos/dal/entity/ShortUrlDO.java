package pers.amos.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短链实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("t_short_url")
public class ShortUrlDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 短链接码
     */
    private String shortCode;

    /**
     * 原始长链接
     */
    private String longUrl;

    /**
     * 长链接MD5值
     */
    private String longUrlMd5;

    /**
     * 创建用户ID
     */
    private Long userId;

    /**
     * 过期时间
     */
    private Date expireTime;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableLogic
    private Integer isDeleted;
} 