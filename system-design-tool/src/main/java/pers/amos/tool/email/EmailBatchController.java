package pers.amos.tool.email;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 邮箱批量工具控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/tool/email")
public class EmailBatchController {
    
    @Autowired
    private EmailBatchTool emailBatchTool;

    @Autowired
    private AugmentAutomationService automationService;
    
    /**
     * 批量生成邮箱
     */
    @PostMapping("/generate")
    public Map<String, Object> generateEmails(@RequestParam(defaultValue = "100") int count) {
        try {
            List<EmailBatchTool.EmailAccount> accounts = emailBatchTool.generateEmails(count);
            return Map.of(
                "success", true,
                "message", "邮箱生成成功",
                "count", accounts.size(),
                "accounts", accounts
            );
        } catch (Exception e) {
            log.error("生成邮箱失败", e);
            return Map.of(
                "success", false,
                "message", "生成邮箱失败: " + e.getMessage()
            );
        }
    }
    
    /**
     * 获取指定邮箱的验证码
     */
    @GetMapping("/codes")
    public Map<String, Object> getVerificationCodes(@RequestParam String email) {
        try {
            String code = emailBatchTool.getVerificationCode(email);
            if (code != null && !code.isEmpty()) {
                return Map.of(
                    "success", true,
                    "message", "验证码获取成功",
                    "email", email,
                    "code", code
                );
            } else {
                return Map.of(
                    "success", false,
                    "message", "未找到该邮箱的验证码",
                    "email", email
                );
            }
        } catch (Exception e) {
            log.error("获取验证码失败: {}", email, e);
            return Map.of(
                "success", false,
                "message", "获取验证码失败: " + e.getMessage(),
                "email", email
            );
        }
    }

    /**
     * 获取Augment账号管理页面
     */
    @GetMapping("/accounts")
    public String getAccountsPage() {
        return "augment-accounts";
    }

    /**
     * 获取所有Augment账号信息
     */
    @GetMapping("/accounts/list")
    @ResponseBody
    public Map<String, Object> getAccountsList() {
        try {
            // 这里可以从数据库或文件中读取账号信息
            // 暂时返回示例数据
            List<Map<String, Object>> accounts = List.of(
                Map.of(
                    "id", 1,
                    "email", "<EMAIL>",
                    "token", "aug_1234567890abcdef",
                    "quota", "100,000 tokens",
                    "used", "25,000 tokens",
                    "remaining", "75,000 tokens",
                    "createTime", "2024-01-15 10:30:00",
                    "status", "active"
                ),
                Map.of(
                    "id", 2,
                    "email", "<EMAIL>",
                    "token", "aug_abcdef1234567890",
                    "quota", "50,000 tokens",
                    "used", "10,000 tokens",
                    "remaining", "40,000 tokens",
                    "createTime", "2024-01-16 14:20:00",
                    "status", "active"
                )
            );

            return Map.of(
                "success", true,
                "message", "获取账号列表成功",
                "count", accounts.size(),
                "accounts", accounts
            );
        } catch (Exception e) {
            log.error("获取账号列表失败", e);
            return Map.of(
                "success", false,
                "message", "获取账号列表失败: " + e.getMessage()
            );
        }
    }

    /**
     * 批量注册Augment账号
     */
    @PostMapping("/augment/register")
    public Map<String, Object> batchRegisterAugment(
            @RequestParam(defaultValue = "10") int count,
            @RequestParam(defaultValue = "https://www.augmentcode.com/api/auth/register") String registerUrl) {
        try {
            log.info("开始批量注册Augment账号，数量: {}, 注册URL: {}", count, registerUrl);

            // 1. 生成邮箱账号
            List<EmailBatchTool.EmailAccount> accounts = emailBatchTool.generateEmails(count);
            if (accounts.isEmpty()) {
                return Map.of(
                    "success", false,
                    "message", "生成邮箱失败"
                );
            }

            // 2. 批量注册Augment账号
            List<Map<String, Object>> registerResults = emailBatchTool.batchRegisterAugment(accounts, registerUrl);

            // 3. 统计注册结果
            long successCount = registerResults.stream()
                    .mapToLong(result -> (Boolean) result.get("success") ? 1 : 0)
                    .sum();

            return Map.of(
                "success", true,
                "message", String.format("Augment账号批量注册完成，成功 %d/%d", successCount, count),
                "totalCount", count,
                "successCount", successCount,
                "failCount", count - successCount,
                "accounts", accounts,
                "registerResults", registerResults
            );

        } catch (Exception e) {
            log.error("批量注册Augment账号失败", e);
            return Map.of(
                "success", false,
                "message", "批量注册失败: " + e.getMessage()
            );
        }
    }

    /**
     * 自动验证Augment账号
     */
    @PostMapping("/augment/verify")
    public Map<String, Object> autoVerifyAugmentAccounts() {
        try {
            log.info("开始自动验证Augment账号...");

            // 获取当前所有邮箱账号
            List<EmailBatchTool.EmailAccount> accounts = emailBatchTool.getAllEmailAccounts();
            if (accounts.isEmpty()) {
                return Map.of(
                    "success", false,
                    "message", "没有找到邮箱账号，请先生成邮箱并注册"
                );
            }

            // 自动验证账号
            Map<String, String> verifyResults = emailBatchTool.autoVerifyAugmentAccounts(accounts);

            // 统计验证结果
            long successCount = verifyResults.values().stream()
                    .mapToLong(result -> "验证成功".equals(result) ? 1 : 0)
                    .sum();

            return Map.of(
                "success", true,
                "message", String.format("Augment账号自动验证完成，成功 %d/%d", successCount, accounts.size()),
                "totalCount", accounts.size(),
                "successCount", successCount,
                "failCount", accounts.size() - successCount,
                "verifyResults", verifyResults
            );

        } catch (Exception e) {
            log.error("自动验证Augment账号失败", e);
            return Map.of(
                "success", false,
                "message", "自动验证失败: " + e.getMessage()
            );
        }
    }

    /**
     * 一键完成Augment账号注册和验证
     */
    @PostMapping("/augment/complete")
    public Map<String, Object> completeAugmentRegistration(
            @RequestParam(defaultValue = "10") int count,
            @RequestParam(defaultValue = "https://www.augmentcode.com/api/auth/register") String registerUrl) {
        try {
            log.info("开始一键完成Augment账号注册和验证，数量: {}", count);

            // 1. 生成邮箱账号
            List<EmailBatchTool.EmailAccount> accounts = emailBatchTool.generateEmails(count);
            if (accounts.isEmpty()) {
                return Map.of(
                    "success", false,
                    "message", "生成邮箱失败"
                );
            }

            // 2. 批量注册
            List<Map<String, Object>> registerResults = emailBatchTool.batchRegisterAugment(accounts, registerUrl);
            long registerSuccessCount = registerResults.stream()
                    .mapToLong(result -> (Boolean) result.get("success") ? 1 : 0)
                    .sum();

            // 3. 自动验证（只验证注册成功的账号）
            List<EmailBatchTool.EmailAccount> successAccounts = new ArrayList<>();
            for (int i = 0; i < registerResults.size(); i++) {
                if ((Boolean) registerResults.get(i).get("success")) {
                    successAccounts.add(accounts.get(i));
                }
            }

            Map<String, String> verifyResults = new HashMap<>();
            if (!successAccounts.isEmpty()) {
                verifyResults = emailBatchTool.autoVerifyAugmentAccounts(successAccounts);
            }

            long verifySuccessCount = verifyResults.values().stream()
                    .mapToLong(result -> "验证成功".equals(result) ? 1 : 0)
                    .sum();

            return Map.of(
                "success", true,
                "message", String.format("Augment账号一键注册验证完成，注册成功 %d/%d，验证成功 %d/%d",
                        registerSuccessCount, count, verifySuccessCount, registerSuccessCount),
                "totalCount", count,
                "registerSuccessCount", registerSuccessCount,
                "verifySuccessCount", verifySuccessCount,
                "accounts", accounts,
                "registerResults", registerResults,
                "verifyResults", verifyResults
            );

        } catch (Exception e) {
            log.error("一键完成Augment账号注册验证失败", e);
            return Map.of(
                "success", false,
                "message", "一键注册验证失败: " + e.getMessage()
            );
        }
    }

    /**
     * 清空邮箱账号缓存
     */
    @PostMapping("/clear-cache")
    public Map<String, Object> clearEmailCache() {
        try {
            int count = emailBatchTool.getEmailAccountCount();
            emailBatchTool.clearEmailAccounts();

            return Map.of(
                "success", true,
                "message", String.format("已清空 %d 个邮箱账号缓存", count)
            );
        } catch (Exception e) {
            log.error("清空邮箱缓存失败", e);
            return Map.of(
                "success", false,
                "message", "清空缓存失败: " + e.getMessage()
            );
        }
    }

    /**
     * 获取当前邮箱账号状态
     */
    @GetMapping("/status")
    public Map<String, Object> getEmailAccountStatus() {
        try {
            int count = emailBatchTool.getEmailAccountCount();
            List<EmailBatchTool.EmailAccount> accounts = emailBatchTool.getAllEmailAccounts();

            return Map.of(
                "success", true,
                "message", "获取状态成功",
                "totalCount", count,
                "accounts", accounts.stream().limit(10).toList(), // 只返回前10个作为示例
                "hasMore", count > 10
            );
        } catch (Exception e) {
            log.error("获取邮箱账号状态失败", e);
            return Map.of(
                "success", false,
                "message", "获取状态失败: " + e.getMessage()
            );
        }
    }

    /**
     * Selenium自动化注册Augment账号
     */
    @PostMapping("/augment/selenium-register")
    public Map<String, Object> seleniumRegisterAugment(
            @RequestParam(defaultValue = "10") int count) {
        try {
            log.info("开始Selenium自动化注册Augment账号，数量: {}", count);

            Map<String, Object> result = automationService.batchSeleniumRegister(count);

            return result;

        } catch (Exception e) {
            log.error("Selenium自动化注册Augment账号失败", e);
            return Map.of(
                "success", false,
                "message", "Selenium自动化注册失败: " + e.getMessage()
            );
        }
    }
}
