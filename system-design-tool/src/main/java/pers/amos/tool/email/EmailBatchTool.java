package pers.amos.tool.email;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 邮箱批量注册和验证码获取工具
 */
@Slf4j
@Component
public class EmailBatchTool {
    
    private final HttpClient httpClient;
    private final ExecutorService executor;
    private final Map<String, EmailAccount> emailAccounts = new ConcurrentHashMap<>();
    
    // 临时邮箱服务配置 - 使用多个备用服务
    private static final String TEMP_MAIL_API = "https://www.1secmail.com/api/v1/";
    private static final String BACKUP_TEMP_MAIL_API = "https://api.mail.tm/"; // 备用邮件服务
    private static final String TEMP_MAIL_API_BACKUP = "https://10minutemail.com/";
    
    public EmailBatchTool() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        this.executor = Executors.newFixedThreadPool(20);
    }
    
    /**
     * 批量生成邮箱账号
     */
    public List<EmailAccount> generateEmails(int count) {
        log.info("开始生成{}个临时邮箱...", count);
        List<EmailAccount> accounts = new ArrayList<>();
        
        try {
            // 获取可用域名
            List<String> domains = getAvailableDomains();
            if (domains.isEmpty()) {
                throw new RuntimeException("无法获取可用域名");
            }
            
            for (int i = 0; i < count; i++) {
                String username = "test" + System.currentTimeMillis() + "_" + i;
                String domain = domains.get(i % domains.size());
                String email = username + "@" + domain;
                
                EmailAccount account = new EmailAccount(email, username, domain);
                accounts.add(account);
                emailAccounts.put(email, account);
            }
            
            log.info("成功生成{}个邮箱账号", accounts.size());
            saveAccountsToFile(accounts);
            
        } catch (Exception e) {
            log.error("生成邮箱失败", e);
        }
        
        return accounts;
    }
    
    /**
     * 批量注册用户
     */
    public void batchRegister(List<EmailAccount> accounts, String registerUrl) {
        log.info("开始批量注册{}个用户...", accounts.size());
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (EmailAccount account : accounts) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    registerUser(account, registerUrl);
                    Thread.sleep(100); // 避免请求过快
                } catch (Exception e) {
                    log.error("注册用户失败: {}", account.getEmail(), e);
                }
            }, executor);
            
            futures.add(future);
        }
        
        // 等待所有注册完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        log.info("批量注册完成");
    }
    
    /**
     * 获取验证码
     */
    public Map<String, String> getVerificationCodes() {
        log.info("开始获取验证码...");
        Map<String, String> codes = new ConcurrentHashMap<>();
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (EmailAccount account : emailAccounts.values()) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    String code = getVerificationCode(account);
                    if (code != null) {
                        codes.put(account.getEmail(), code);
                        log.info("获取到验证码: {} -> {}", account.getEmail(), code);
                    }
                } catch (Exception e) {
                    log.error("获取验证码失败: {}", account.getEmail(), e);
                }
            }, executor);
            
            futures.add(future);
        }
        
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        log.info("验证码获取完成，共获取{}个", codes.size());
        
        return codes;
    }
    
    private List<String> getAvailableDomains() throws Exception {
        // 直接使用已知可用的域名，避免API调用问题
        List<String> domains = Arrays.asList(
            "1secmail.com", "1secmail.org", "1secmail.net",
            "esiix.com", "wwjmp.com", "tmmbt.com"
        );
        
        log.info("使用预设域名列表: {}", domains);
        return domains;
    }
    
    private void registerUser(EmailAccount account, String registerUrl) throws Exception {
        String requestBody = String.format(
                "{\"username\":\"%s\",\"email\":\"%s\",\"password\":\"Test123456\",\"phone\":\"\"}",
                account.getUsername(), account.getEmail()
        );
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(registerUrl))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() == 200) {
            log.debug("用户注册成功: {}", account.getEmail());
        } else {
            log.warn("用户注册失败: {}, 状态码: {}", account.getEmail(), response.statusCode());
        }
    }
    
    public String getVerificationCode(EmailAccount account) throws Exception {
        int maxRetries = 5;
        int retryDelay = 3000; // 3秒
        
        for (int retry = 0; retry < maxRetries; retry++) {
            try {
                log.debug("第{}次尝试获取邮件: {}", retry + 1, account.getEmail());
                
                // 等待邮件到达
                Thread.sleep(retryDelay);
                
                // 获取邮件列表
                String messagesUrl = TEMP_MAIL_API + "?action=getMessages&login=" + 
                                   account.getUsername() + "&domain=" + account.getDomain();
                
                // 添加随机延迟避免请求过于频繁
                Thread.sleep(1000 + (long)(Math.random() * 2000)); // 1-3秒随机延迟

                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(messagesUrl))
                        .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                        .header("Accept", "application/json, text/plain, */*")
                        .header("Accept-Language", "en-US,en;q=0.9")
                        .header("Accept-Encoding", "gzip, deflate, br")
                        .header("Referer", "https://www.1secmail.com/")
                        .header("Origin", "https://www.1secmail.com")
                        .header("Connection", "keep-alive")
                        .header("Sec-Fetch-Dest", "empty")
                        .header("Sec-Fetch-Mode", "cors")
                        .header("Sec-Fetch-Site", "same-origin")
                        .header("Cache-Control", "no-cache")
                        .header("Pragma", "no-cache")
                        .timeout(Duration.ofSeconds(30))
                        .GET()
                        .build();
                
                HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
                
                log.debug("邮件列表响应: status={}, body={}", response.statusCode(), response.body());
                
                if (response.statusCode() == 200) {
                    String body = response.body();
                    if (body.contains("\"id\":")) {
                        String emailId = extractEmailId(body);
                        if (emailId != null) {
                            String code = getEmailContent(account, emailId);
                            if (code != null) {
                                return code;
                            }
                        }
                    }
                } else if (response.statusCode() == 403) {
                    log.warn("1secmail API返回403错误，可能是请求频率过高或IP被限制。邮箱: {}, 尝试次数: {}",
                        account.getEmail(), retry + 1);
                    // 403错误时增加更长的等待时间
                    Thread.sleep(5000 + (long)(Math.random() * 5000)); // 5-10秒随机延迟
                } else {
                    log.warn("邮件API请求失败: status={}, body={}, 邮箱: {}",
                        response.statusCode(), response.body(), account.getEmail());
                }
                
                // 如果没有找到邮件，增加等待时间
                retryDelay += 2000;
                
            } catch (Exception e) {
                log.warn("获取邮件失败，重试中: {}", e.getMessage());
                if (retry == maxRetries - 1) {
                    throw e;
                }
            }
        }
        
        return null;
    }
    
    private String extractEmailId(String response) {
        Pattern pattern = Pattern.compile("\"id\":(\\d+)");
        Matcher matcher = pattern.matcher(response);
        return matcher.find() ? matcher.group(1) : null;
    }
    
    private String getEmailContent(EmailAccount account, String emailId) throws Exception {
        String readUrl = TEMP_MAIL_API + "?action=readMessage&login=" + 
                        account.getUsername() + "&domain=" + account.getDomain() + "&id=" + emailId;
        
        // 添加随机延迟
        Thread.sleep(500 + (long)(Math.random() * 1000)); // 0.5-1.5秒随机延迟

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(readUrl))
                .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .header("Accept", "application/json, text/plain, */*")
                .header("Accept-Language", "en-US,en;q=0.9")
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("Referer", "https://www.1secmail.com/")
                .header("Origin", "https://www.1secmail.com")
                .header("Connection", "keep-alive")
                .header("Sec-Fetch-Dest", "empty")
                .header("Sec-Fetch-Mode", "cors")
                .header("Sec-Fetch-Site", "same-origin")
                .timeout(Duration.ofSeconds(30))
                .GET()
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        log.debug("邮件内容响应: status={}, body={}", response.statusCode(), response.body());
        
        if (response.statusCode() == 200) {
            String content = response.body();
            // 支持多种验证码格式
            Pattern[] patterns = {
                Pattern.compile("验证码[：:]?\\s*(\\d{4,6})"),
                Pattern.compile("code[：:]?\\s*(\\d{4,6})", Pattern.CASE_INSENSITIVE),
                Pattern.compile("verification[：:]?\\s*(\\d{4,6})", Pattern.CASE_INSENSITIVE),
                Pattern.compile("\\b(\\d{4,6})\\b")
            };
            
            for (Pattern pattern : patterns) {
                Matcher matcher = pattern.matcher(content);
                if (matcher.find()) {
                    return matcher.group(1);
                }
            }
        }
        
        return null;
    }

    /**
     * 根据邮箱地址直接获取验证码
     */
    public String getVerificationCode(String email) {
        try {
            log.info("开始获取邮箱 {} 的验证码", email);

            // 解析邮箱地址
            String[] parts = email.split("@");
            if (parts.length != 2) {
                log.error("邮箱地址格式错误: {}", email);
                return null;
            }

            String username = parts[0];
            String domain = parts[1];

            int maxRetries = 10;
            int retryDelay = 3000; // 3秒

            for (int retry = 0; retry < maxRetries; retry++) {
                try {
                    log.debug("第{}次尝试获取邮件: {}", retry + 1, email);

                    // 等待邮件到达
                    Thread.sleep(retryDelay);

                    // 添加随机延迟避免请求过于频繁
                    Thread.sleep(1000 + (long)(Math.random() * 2000)); // 1-3秒随机延迟

                    // 获取邮件列表
                    String messagesUrl = TEMP_MAIL_API + "?action=getMessages&login=" +
                                       username + "&domain=" + domain;

                    HttpRequest request = HttpRequest.newBuilder()
                            .uri(URI.create(messagesUrl))
                            .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                            .header("Accept", "application/json, text/plain, */*")
                            .header("Accept-Language", "en-US,en;q=0.9")
                            .header("Accept-Encoding", "gzip, deflate, br")
                            .header("Referer", "https://www.1secmail.com/")
                            .header("Origin", "https://www.1secmail.com")
                            .header("Connection", "keep-alive")
                            .header("Sec-Fetch-Dest", "empty")
                            .header("Sec-Fetch-Mode", "cors")
                            .header("Sec-Fetch-Site", "same-origin")
                            .header("Cache-Control", "no-cache")
                            .header("Pragma", "no-cache")
                            .timeout(Duration.ofSeconds(30))
                            .GET()
                            .build();

                    HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

                    log.debug("邮件列表响应: status={}, body={}", response.statusCode(), response.body());

                    if (response.statusCode() == 200) {
                        String body = response.body();
                        if (body.contains("\"id\":")) {
                            String emailId = extractEmailId(body);
                            if (emailId != null) {
                                String code = getEmailContentDirect(username, domain, emailId);
                                if (code != null) {
                                    log.info("成功获取邮箱 {} 的验证码: {}", email, code);
                                    return code;
                                }
                            }
                        }
                    } else if (response.statusCode() == 403) {
                        log.warn("1secmail API返回403错误，可能是请求频率过高或IP被限制。邮箱: {}, 尝试次数: {}",
                            email, retry + 1);
                        // 403错误时增加更长的等待时间
                        Thread.sleep(5000 + (long)(Math.random() * 5000)); // 5-10秒随机延迟
                    } else {
                        log.warn("邮件API请求失败: status={}, body={}, 邮箱: {}",
                            response.statusCode(), response.body(), email);
                    }

                    // 如果没有找到邮件，增加等待时间
                    retryDelay += 2000;

                } catch (Exception e) {
                    log.error("第{}次获取邮件失败: {}, 错误: {}", retry + 1, email, e.getMessage());
                    if (retry == maxRetries - 1) {
                        throw e;
                    }
                }
            }

            log.warn("超过最大重试次数，未能获取到验证码: {}", email);
            return null;

        } catch (Exception e) {
            log.error("获取邮箱 {} 的验证码失败", email, e);
            return null;
        }
    }

    /**
     * 直接获取邮件内容并提取验证码
     */
    private String getEmailContentDirect(String username, String domain, String emailId) throws Exception {
        // 添加随机延迟
        Thread.sleep(500 + (long)(Math.random() * 1000)); // 0.5-1.5秒随机延迟

        String readUrl = TEMP_MAIL_API + "?action=readMessage&login=" +
                        username + "&domain=" + domain + "&id=" + emailId;

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(readUrl))
                .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .header("Accept", "application/json, text/plain, */*")
                .header("Accept-Language", "en-US,en;q=0.9")
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("Referer", "https://www.1secmail.com/")
                .header("Origin", "https://www.1secmail.com")
                .header("Connection", "keep-alive")
                .header("Sec-Fetch-Dest", "empty")
                .header("Sec-Fetch-Mode", "cors")
                .header("Sec-Fetch-Site", "same-origin")
                .timeout(Duration.ofSeconds(30))
                .GET()
                .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        log.debug("邮件内容响应: status={}, body={}", response.statusCode(), response.body());

        if (response.statusCode() == 200) {
            String content = response.body();

            // 提取验证码 - 支持多种格式
            String[] patterns = {
                "verification code is:?\\s*(\\d{6})",           // verification code is: 123456
                "verification code:?\\s*(\\d{6})",             // verification code: 123456
                "code is:?\\s*(\\d{6})",                       // code is: 123456
                "code:?\\s*(\\d{6})",                          // code: 123456
                "your code:?\\s*(\\d{6})",                     // your code: 123456
                "\\b(\\d{6})\\b",                              // 任何6位数字
                "\\b(\\d{4})\\b"                               // 任何4位数字（备用）
            };

            for (String patternStr : patterns) {
                Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(content);
                if (matcher.find()) {
                    String code = matcher.group(1);
                    log.debug("使用模式 '{}' 提取到验证码: {}", patternStr, code);
                    return code;
                }
            }

            log.warn("未能从邮件内容中提取验证码，内容: {}", content.substring(0, Math.min(500, content.length())));
        } else {
            log.warn("获取邮件内容失败: status={}, body={}", response.statusCode(), response.body());
        }

        return null;
    }

    private void saveAccountsToFile(List<EmailAccount> accounts) {
        try {
            // 创建目录
            String dirPath = "data";
            File directory = new File(dirPath);
            if (!directory.exists()) {
                directory.mkdirs();
                log.info("创建目录: {}", dirPath);
            }
            
            // 生成文件名（带时间戳）
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
            String fileName = String.format("%s/email_accounts_%s.txt", dirPath, timestamp);
            
            try (PrintWriter writer = new PrintWriter(new FileWriter(fileName))) {
                writer.println("# 批量生成的邮箱账号");
                writer.println("# 生成时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
                writer.println("# 账号数量: " + accounts.size());
                writer.println("# 格式: 邮箱,用户名,域名");
                writer.println();
                
                for (EmailAccount account : accounts) {
                    writer.println(String.format("%s,%s,%s", 
                        account.getEmail(), account.getUsername(), account.getDomain()));
                }
                log.info("邮箱账号已保存到: {}", fileName);
            }
            
        } catch (IOException e) {
            log.error("保存邮箱账号失败", e);
        }
    }
    
    /**
     * 邮箱账号实体类
     */
    public static class EmailAccount {
        private final String email;
        private final String username;
        private final String domain;
        
        public EmailAccount(String email, String username, String domain) {
            this.email = email;
            this.username = username;
            this.domain = domain;
        }
        
        public String getEmail() { return email; }
        public String getUsername() { return username; }
        public String getDomain() { return domain; }
        
        @Override
        public String toString() {
            return String.format("EmailAccount{email='%s', username='%s', domain='%s'}", 
                email, username, domain);
        }
    }

    /**
     * 批量注册Augment账号
     */
    public List<Map<String, Object>> batchRegisterAugment(List<EmailAccount> accounts, String registerUrl) {
        log.info("开始批量注册{}个Augment账号...", accounts.size());
        
        List<Map<String, Object>> results = new ArrayList<>();
        List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();
        
        for (EmailAccount account : accounts) {
            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return registerAugmentUser(account, registerUrl);
                } catch (Exception e) {
                    log.error("注册Augment用户失败: {}", account.getEmail(), e);
                    return Map.of(
                        "email", account.getEmail(),
                        "success", false,
                        "error", e.getMessage()
                    );
                }
            }, executor);
            
            futures.add(future);
        }
        
        // 等待所有注册完成
        for (CompletableFuture<Map<String, Object>> future : futures) {
            try {
                results.add(future.get());
                Thread.sleep(200); // 避免请求过快
            } catch (Exception e) {
                log.error("获取注册结果失败", e);
            }
        }
        
        log.info("Augment账号批量注册完成，成功{}个", results.size());
        return results;
    }

    /**
     * 注册单个Augment用户
     */
    private Map<String, Object> registerAugmentUser(EmailAccount account, String registerUrl) throws Exception {
        // 生成随机用户名和密码
        String username = account.getUsername();
        String password = "AugmentTest123!";
        
        // 构建注册请求体（根据Augment实际API调整）
        String requestBody = String.format(
            "{\"email\":\"%s\",\"username\":\"%s\",\"password\":\"%s\",\"confirmPassword\":\"%s\"}",
            account.getEmail(), username, password, password
        );
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(registerUrl))
                .header("Content-Type", "application/json")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .header("Accept", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        log.debug("Augment注册响应: status={}, body={}", response.statusCode(), response.body());
        
        if (response.statusCode() == 200 || response.statusCode() == 201) {
            log.info("Augment用户注册成功: {}", account.getEmail());
            return Map.of(
                "email", account.getEmail(),
                "username", username,
                "password", password,
                "success", true,
                "response", response.body()
            );
        } else {
            log.warn("Augment用户注册失败: {}, 状态码: {}, 响应: {}", 
                    account.getEmail(), response.statusCode(), response.body());
            return Map.of(
                "email", account.getEmail(),
                "success", false,
                "statusCode", response.statusCode(),
                "response", response.body()
            );
        }
    }

    /**
     * 自动验证Augment账号
     */
    public Map<String, String> autoVerifyAugmentAccounts(List<EmailAccount> accounts) {
        log.info("开始自动验证{}个Augment账号...", accounts.size());
        
        Map<String, String> results = new ConcurrentHashMap<>();
        
        // 等待邮件到达
        try {
            Thread.sleep(10000); // 等待10秒让邮件到达
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        
        for (EmailAccount account : accounts) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    String verificationCode = getAugmentVerificationCode(account);
                    if (verificationCode != null) {
                        boolean verified = verifyAugmentAccount(account, verificationCode);
                        results.put(account.getEmail(), verified ? "验证成功" : "验证失败");
                        log.info("Augment账号验证: {} -> {}", account.getEmail(), 
                                verified ? "成功" : "失败");
                    } else {
                        results.put(account.getEmail(), "未收到验证码");
                        log.warn("未收到Augment验证码: {}", account.getEmail());
                    }
                } catch (Exception e) {
                    results.put(account.getEmail(), "验证异常: " + e.getMessage());
                    log.error("验证Augment账号失败: {}", account.getEmail(), e);
                }
            }, executor);
            
            futures.add(future);
        }
        
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        log.info("Augment账号自动验证完成，成功{}个", results.size());
        
        return results;
    }

    /**
     * 获取Augment验证码
     */
    private String getAugmentVerificationCode(EmailAccount account) throws Exception {
        int maxRetries = 10;
        int retryDelay = 5000; // 5秒
        
        for (int retry = 0; retry < maxRetries; retry++) {
            try {
                Thread.sleep(retryDelay);
                
                String messagesUrl = TEMP_MAIL_API + "?action=getMessages&login=" + 
                                   account.getUsername() + "&domain=" + account.getDomain();
                
                // 添加随机延迟避免请求过于频繁
                Thread.sleep(1000 + (long)(Math.random() * 2000)); // 1-3秒随机延迟

                HttpRequest request = HttpRequest.newBuilder()
                        .uri(URI.create(messagesUrl))
                        .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                        .header("Accept", "application/json, text/plain, */*")
                        .header("Accept-Language", "en-US,en;q=0.9")
                        .header("Accept-Encoding", "gzip, deflate, br")
                        .header("Referer", "https://www.1secmail.com/")
                        .header("Origin", "https://www.1secmail.com")
                        .header("Connection", "keep-alive")
                        .header("Sec-Fetch-Dest", "empty")
                        .header("Sec-Fetch-Mode", "cors")
                        .header("Sec-Fetch-Site", "same-origin")
                        .header("Cache-Control", "no-cache")
                        .header("Pragma", "no-cache")
                        .timeout(Duration.ofSeconds(30))
                        .GET()
                        .build();
                
                HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
                
                if (response.statusCode() == 200) {
                    String body = response.body();
                    if (body.contains("augment") || body.contains("verification")) {
                        String emailId = extractEmailId(body);
                        if (emailId != null) {
                            String code = getAugmentEmailContent(account, emailId);
                            if (code != null) {
                                return code;
                            }
                        }
                    }
                }
                
            } catch (Exception e) {
                log.warn("获取Augment验证码失败，重试中: {}", e.getMessage());
            }
        }
        
        return null;
    }

    /**
     * 获取Augment邮件内容并提取验证码
     */
    private String getAugmentEmailContent(EmailAccount account, String emailId) throws Exception {
        String readUrl = TEMP_MAIL_API + "?action=readMessage&login=" + 
                        account.getUsername() + "&domain=" + account.getDomain() + "&id=" + emailId;
        
        // 添加随机延迟
        Thread.sleep(500 + (long)(Math.random() * 1000)); // 0.5-1.5秒随机延迟

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(readUrl))
                .header("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
                .header("Accept", "application/json, text/plain, */*")
                .header("Accept-Language", "en-US,en;q=0.9")
                .header("Accept-Encoding", "gzip, deflate, br")
                .header("Referer", "https://www.1secmail.com/")
                .header("Origin", "https://www.1secmail.com")
                .header("Connection", "keep-alive")
                .header("Sec-Fetch-Dest", "empty")
                .header("Sec-Fetch-Mode", "cors")
                .header("Sec-Fetch-Site", "same-origin")
                .timeout(Duration.ofSeconds(30))
                .GET()
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() == 200) {
            String content = response.body();
            
            // Augment验证码格式匹配
            Pattern[] patterns = {
                Pattern.compile("verification code[：:]?\\s*([A-Z0-9]{6})", Pattern.CASE_INSENSITIVE),
                Pattern.compile("verify[：:]?\\s*([A-Z0-9]{6})", Pattern.CASE_INSENSITIVE),
                Pattern.compile("code[：:]?\\s*([A-Z0-9]{6})", Pattern.CASE_INSENSITIVE),
                Pattern.compile("\\b([A-Z0-9]{6})\\b")
            };
            
            for (Pattern pattern : patterns) {
                Matcher matcher = pattern.matcher(content);
                if (matcher.find()) {
                    return matcher.group(1);
                }
            }
        }
        
        return null;
    }

    /**
     * 验证Augment账号
     */
    private boolean verifyAugmentAccount(EmailAccount account, String verificationCode) throws Exception {
        // 构建验证请求（根据Augment实际API调整）
        String verifyUrl = "https://www.augmentcode.com/api/auth/verify";
        String requestBody = String.format(
            "{\"email\":\"%s\",\"code\":\"%s\"}",
            account.getEmail(), verificationCode
        );
        
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(verifyUrl))
                .header("Content-Type", "application/json")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .POST(HttpRequest.BodyPublishers.ofString(requestBody))
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        return response.statusCode() == 200;
    }

    /**
     * 获取所有邮箱账号
     */
    public List<EmailAccount> getAllEmailAccounts() {
        return new ArrayList<>(emailAccounts.values());
    }

    /**
     * 清空邮箱账号缓存
     */
    public void clearEmailAccounts() {
        emailAccounts.clear();
        log.info("已清空邮箱账号缓存");
    }

    /**
     * 获取邮箱账号数量
     */
    public int getEmailAccountCount() {
        return emailAccounts.size();
    }
}
