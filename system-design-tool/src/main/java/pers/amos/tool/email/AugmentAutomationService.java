package pers.amos.tool.email;

import lombok.extern.slf4j.Slf4j;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Augment账号Selenium自动化注册服务
 */
@Slf4j
@Service
public class AugmentAutomationService {

    @Autowired
    private EmailBatchTool emailBatchTool;

    private final ExecutorService executor = Executors.newFixedThreadPool(3); // 降低并发数避免被检测

    /**
     * 批量Selenium自动化注册Augment账号
     */
    public Map<String, Object> batchSeleniumRegister(int count) {
        log.info("开始使用Selenium批量自动化注册{}个Augment账号", count);

        try {
            // 1. 生成邮箱账号
            List<EmailBatchTool.EmailAccount> accounts = emailBatchTool.generateEmails(count);
            if (accounts.isEmpty()) {
                return createErrorResponse("生成邮箱失败");
            }

            // 2. 执行Selenium自动化注册
            List<Map<String, Object>> results = seleniumBatchRegister(accounts);

            // 3. 统计结果
            long successCount = results.stream()
                    .mapToLong(result -> (Boolean) result.getOrDefault("success", false) ? 1 : 0)
                    .sum();

            return Map.of(
                "success", true,
                "message", String.format("Selenium自动化注册完成，成功 %d/%d", successCount, count),
                "method", "SELENIUM_CHROME",
                "totalCount", count,
                "successCount", successCount,
                "failCount", count - successCount,
                "accounts", accounts,
                "results", results
            );

        } catch (Exception e) {
            log.error("Selenium批量自动化注册失败", e);
            return createErrorResponse("Selenium自动化注册失败: " + e.getMessage());
        }
    }

    /**
     * 方案一：Selenium WebDriver自动化注册
     */
    private List<Map<String, Object>> seleniumBatchRegister(List<EmailBatchTool.EmailAccount> accounts) {
        log.info("使用Selenium WebDriver进行自动化注册");
        
        List<Map<String, Object>> results = new ArrayList<>();
        List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();

        for (EmailBatchTool.EmailAccount account : accounts) {
            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> seleniumRegisterSingle(account), executor);
            futures.add(future);
            
            // 添加延迟避免被检测
            try {
                Thread.sleep(2000 + new Random().nextInt(3000)); // 2-5秒随机延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // 收集结果
        for (CompletableFuture<Map<String, Object>> future : futures) {
            try {
                results.add(future.get(300, TimeUnit.SECONDS)); // 5分钟超时
            } catch (Exception e) {
                log.error("获取Selenium注册结果失败", e);
                results.add(createErrorResponse("注册超时或失败"));
            }
        }

        return results;
    }

    /**
     * Selenium单个账号注册
     */
    private Map<String, Object> seleniumRegisterSingle(EmailBatchTool.EmailAccount account) {
        // 注意：这里需要添加Selenium依赖到pom.xml
        try {
            log.info("开始Selenium自动化注册: {}", account.getEmail());
            // 使用手动下载的ChromeDriver
            System.setProperty("webdriver.chrome.driver", "/Users/<USER>/Downloads/dev/chrome/chromedriver");

            ChromeOptions options = new ChromeOptions();
            // options.addArguments("--headless"); // 暂时关闭无头模式用于调试
            options.addArguments("--incognito"); // 启用无痕模式
            options.addArguments("--no-sandbox");
            options.addArguments("--disable-dev-shm-usage");
            options.addArguments("--disable-gpu");
            options.addArguments("--disable-web-security");
            options.addArguments("--disable-features=VizDisplayCompositor");
            options.addArguments("--disable-logging");
            options.addArguments("--disable-dev-tools");
            options.addArguments("--window-size=1366,768"); // 使用更常见的分辨率

            // 高级反爬虫对策
            options.addArguments("--disable-blink-features=AutomationControlled");
            options.addArguments("--disable-extensions");
            options.addArguments("--disable-plugins");
            options.addArguments("--disable-images"); // 禁用图片加载提高速度
            options.addArguments("--disable-javascript-harmony-shipping");
            options.addArguments("--disable-client-side-phishing-detection");
            options.addArguments("--disable-default-apps");
            options.addArguments("--disable-hang-monitor");
            options.addArguments("--disable-popup-blocking");
            options.addArguments("--disable-prompt-on-repost");
            options.addArguments("--disable-sync");
            options.addArguments("--disable-translate");
            options.addArguments("--disable-background-timer-throttling");
            options.addArguments("--disable-renderer-backgrounding");
            options.addArguments("--disable-backgrounding-occluded-windows");
            options.addArguments("--disable-ipc-flooding-protection");

            // 模拟真实用户的User-Agent
            String[] userAgents = {
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
            };
            String randomUserAgent = userAgents[new Random().nextInt(userAgents.length)];
            options.addArguments("--user-agent=" + randomUserAgent);

            // 设置实验性选项来隐藏自动化特征
            options.setExperimentalOption("excludeSwitches", java.util.Arrays.asList("enable-automation"));
            options.setExperimentalOption("useAutomationExtension", false);

            // 添加一些首选项来模拟真实浏览器（无痕模式优化）
            java.util.Map<String, Object> prefs = new java.util.HashMap<>();
            prefs.put("profile.default_content_setting_values.notifications", 2);
            prefs.put("profile.default_content_settings.popups", 0);
            prefs.put("profile.managed_default_content_settings.images", 1); // 允许图片加载
            prefs.put("profile.default_content_setting_values.plugins", 1);
            prefs.put("profile.content_settings.plugin_whitelist.adobe-flash-player", 1);
            prefs.put("profile.content_settings.exceptions.plugins.*,*.per_resource.adobe-flash-player", 1);
            prefs.put("profile.default_content_setting_values.cookies", 1);
            prefs.put("profile.block_third_party_cookies", false);
            prefs.put("profile.default_content_setting_values.geolocation", 1);
            // 无痕模式相关设置
            prefs.put("profile.default_content_setting_values.media_stream", 1);
            prefs.put("profile.managed_default_content_settings.media_stream", 1);
            options.setExperimentalOption("prefs", prefs);

            // 添加更多反检测参数
            options.addArguments("--disable-blink-features=AutomationControlled");
            options.addArguments("--disable-automation");
            options.addArguments("--disable-save-password-bubble");
            options.addArguments("--disable-single-click-autofill");
            options.addArguments("--disable-autofill-keyboard-accessory-view[8]");
            options.addArguments("--disable-full-form-autofill-ios");
            options.addArguments("--enable-features=NetworkService,NetworkServiceLogging");
            options.addArguments("--disable-features=TranslateUI");
            options.addArguments("--disable-ipc-flooding-protection");

            // 模拟真实浏览器的启动参数
            options.addArguments("--no-first-run");
            options.addArguments("--no-service-autorun");
            options.addArguments("--password-store=basic");
            options.addArguments("--use-mock-keychain");

            // 无痕模式增强参数
            options.addArguments("--disable-background-networking");
            options.addArguments("--disable-background-timer-throttling");
            options.addArguments("--disable-renderer-backgrounding");
            options.addArguments("--disable-backgrounding-occluded-windows");
            options.addArguments("--disable-client-side-phishing-detection");
            options.addArguments("--disable-component-update");
            options.addArguments("--disable-default-apps");
            options.addArguments("--disable-domain-reliability");
            options.addArguments("--disable-features=TranslateUI,BlinkGenPropertyTrees");
            options.addArguments("--disable-ipc-flooding-protection");
            options.addArguments("--no-default-browser-check");
            options.addArguments("--no-pings");
            options.addArguments("--disable-web-security");
            options.addArguments("--allow-running-insecure-content");

            // 设置日志级别来减少警告
            options.setLogLevel(org.openqa.selenium.chrome.ChromeDriverLogLevel.SEVERE);

            WebDriver driver = new ChromeDriver(options);
            WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(30));

            // 高级反检测JavaScript脚本
            JavascriptExecutor js = (JavascriptExecutor) driver;

            // 隐藏webdriver属性
            js.executeScript("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})");

            // 伪造plugins
            js.executeScript("""
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [
                        {name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer'},
                        {name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai'},
                        {name: 'Native Client', filename: 'internal-nacl-plugin'}
                    ]
                })
            """);

            // 伪造languages
            js.executeScript("Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']})");

            // 伪造chrome对象
            js.executeScript("window.chrome = {runtime: {}, loadTimes: function(){}, csi: function(){}};");

            // 伪造permissions
            js.executeScript("""
                const originalQuery = window.navigator.permissions.query;
                window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
            """);

            // 重写toString方法
            js.executeScript("""
                window.navigator.chrome = {
                    runtime: {},
                    loadTimes: function(){},
                    csi: function(){}
                };

                ['height', 'width'].forEach(property => {
                    const imageDescriptor = Object.getOwnPropertyDescriptor(HTMLImageElement.prototype, property);
                    Object.defineProperty(HTMLImageElement.prototype, property, {
                        ...imageDescriptor,
                        get: function() {
                            if (this.complete && this.naturalHeight == 0) {
                                return 16;
                            }
                            return imageDescriptor.get.apply(this);
                        },
                    });
                });
            """);

            // 模拟真实的屏幕指纹
            js.executeScript("""
                Object.defineProperty(screen, 'colorDepth', {get: () => 24});
                Object.defineProperty(screen, 'pixelDepth', {get: () => 24});
                Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 8});
                Object.defineProperty(navigator, 'deviceMemory', {get: () => 8});
            """);

            // Canvas指纹伪装
            js.executeScript("""
                const getContext = HTMLCanvasElement.prototype.getContext;
                HTMLCanvasElement.prototype.getContext = function(contextType, contextAttributes) {
                    if (contextType === '2d') {
                        const context = getContext.apply(this, arguments);
                        const originalFillText = context.fillText;
                        context.fillText = function(text, x, y, maxWidth) {
                            // 添加微小的随机噪声
                            const noise = Math.random() * 0.0001;
                            return originalFillText.call(this, text, x + noise, y + noise, maxWidth);
                        };
                        return context;
                    }
                    return getContext.apply(this, arguments);
                };
            """);

            // WebGL指纹伪装
            js.executeScript("""
                const getParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {
                    if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
                        return 'Intel Inc.';
                    }
                    if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL
                        return 'Intel Iris OpenGL Engine';
                    }
                    return getParameter.apply(this, arguments);
                };
            """);

            // 音频指纹伪装
            js.executeScript("""
                const audioContext = window.AudioContext || window.webkitAudioContext;
                if (audioContext) {
                    const originalCreateAnalyser = audioContext.prototype.createAnalyser;
                    audioContext.prototype.createAnalyser = function() {
                        const analyser = originalCreateAnalyser.apply(this, arguments);
                        const originalGetFloatFrequencyData = analyser.getFloatFrequencyData;
                        analyser.getFloatFrequencyData = function(array) {
                            originalGetFloatFrequencyData.apply(this, arguments);
                            // 添加微小噪声
                            for (let i = 0; i < array.length; i++) {
                                array[i] += Math.random() * 0.0001;
                            }
                        };
                        return analyser;
                    };
                }
            """);

            // 字体指纹伪装
            js.executeScript("""
                const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
                const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');

                Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
                    get: function() {
                        const width = originalOffsetWidth.get.call(this);
                        return width + Math.random() * 0.1;
                    }
                });

                Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
                    get: function() {
                        const height = originalOffsetHeight.get.call(this);
                        return height + Math.random() * 0.1;
                    }
                });
            """);

            // 时区和语言伪装
            js.executeScript("""
                Object.defineProperty(Intl.DateTimeFormat.prototype, 'resolvedOptions', {
                    value: function() {
                        return {
                            locale: 'en-US',
                            timeZone: 'America/New_York',
                            calendar: 'gregory',
                            numberingSystem: 'latn'
                        };
                    }
                });
            """);

            try {
                // 步骤1: 访问Augment官网
                log.info("步骤1: 访问Augment官网");
                driver.get("https://www.augmentcode.com/");

                // 模拟真实用户行为 - 随机等待
                int randomDelay = 3000 + new Random().nextInt(2000); // 3-5秒随机延迟
                Thread.sleep(randomDelay);

                // 模拟鼠标移动
                org.openqa.selenium.interactions.Actions actions =
                    new org.openqa.selenium.interactions.Actions(driver);
                actions.moveByOffset(100, 100).perform();
                Thread.sleep(500 + new Random().nextInt(1000));

                // 步骤2: 点击右上角的"Sign in"按钮
                log.info("步骤2: 查找并点击Sign in按钮");
                WebElement signInButton = wait.until(ExpectedConditions.elementToBeClickable(
                    By.xpath("//a[@href='https://app.augmentcode.com' and contains(text(), 'Sign in')]")));
                signInButton.click();
                Thread.sleep(5000);
                log.info("已点击Sign in按钮，等待跳转到登录页面");

                // 步骤3: 填入邮箱地址 - 模拟人类输入
                log.info("步骤3: 填入邮箱地址");
                WebElement emailInput = wait.until(ExpectedConditions.presenceOfElementLocated(
                    By.id("username")));

                // 模拟人类点击输入框
                actions.moveToElement(emailInput).click().perform();
                Thread.sleep(200 + new Random().nextInt(300));

                emailInput.clear();

                // 模拟人类逐字符输入
                String email = account.getEmail();
                for (char c : email.toCharArray()) {
                    emailInput.sendKeys(String.valueOf(c));
                    Thread.sleep(50 + new Random().nextInt(100)); // 每个字符间隔50-150ms
                }

                log.info("已填入邮箱: {}", account.getEmail());
                Thread.sleep(1000 + new Random().nextInt(1000));

                // 步骤4: 人类验证 - 手动完成
                log.info("步骤4: 等待用户手动完成人类验证");
                log.info("=".repeat(60));
                log.info("🔔 请在浏览器中手动完成 'Verify you are human' 验证");
                log.info("📍 验证完成后，请在控制台按回车键继续...");
                log.info("=".repeat(60));

                try {
                    // 等待用户手动完成验证
                    System.in.read();
                    log.info("✅ 用户确认已完成人类验证，继续执行...");

                    // 给用户一点时间确保验证完全完成
                    Thread.sleep(2000);

                } catch (Exception e) {
                    log.error("等待用户输入时发生错误: {}", e.getMessage());
                }

                // 步骤5: 点击Continue按钮
                log.info("步骤5: 点击Continue按钮");
                WebElement continueButton = wait.until(ExpectedConditions.elementToBeClickable(
                    By.xpath("//button[contains(text(), 'Continue')]")));
                continueButton.click();
                log.info("已点击Continue按钮，等待验证码页面");
                Thread.sleep(5000);

                // 步骤6: 等待并获取邮箱验证码
                log.info("步骤6: 等待邮箱验证码");
                String verificationCode = waitForVerificationCode(account);
                if (verificationCode == null) {
                    return Map.of(
                        "success", false,
                        "email", account.getEmail(),
                        "method", "selenium",
                        "error", "未收到验证码"
                    );
                }

                // 步骤7: 输入验证码
                log.info("步骤7: 输入验证码: {}", verificationCode);
                WebElement codeInput = wait.until(ExpectedConditions.presenceOfElementLocated(
                    By.id("code")));
                codeInput.clear();
                codeInput.sendKeys(verificationCode);
                log.info("已输入验证码");
                Thread.sleep(1000);

                // 步骤8: 点击Continue按钮提交验证码
                log.info("步骤8: 提交验证码");
                WebElement submitButton = wait.until(ExpectedConditions.elementToBeClickable(
                    By.xpath("//button[contains(text(), 'Continue')]")));
                submitButton.click();
                log.info("已提交验证码，等待注册完成");
                Thread.sleep(5000);

                // 步骤9: 检查注册是否成功
                String currentUrl = driver.getCurrentUrl();
                String pageSource = driver.getPageSource().toLowerCase();

                boolean isSuccess = pageSource.contains("welcome") ||
                                   pageSource.contains("dashboard") ||
                                   pageSource.contains("success") ||
                                   currentUrl.contains("app.augmentcode.com") ||
                                   currentUrl.contains("dashboard");

                if (isSuccess) {
                    log.info("✅ Selenium注册成功: {}", account.getEmail());
                    return Map.of(
                        "success", true,
                        "email", account.getEmail(),
                        "method", "selenium",
                        "verificationCode", verificationCode,
                        "finalUrl", currentUrl
                    );
                } else {
                    log.warn("⚠️ Selenium注册状态未知: {}", account.getEmail());
                    return Map.of(
                        "success", false,
                        "email", account.getEmail(),
                        "method", "selenium",
                        "error", "注册流程完成但未检测到成功标识",
                        "finalUrl", currentUrl
                    );
                }

            } finally {
                driver.quit();
            }

        } catch (Exception e) {
            log.error("Selenium注册失败: {}", account.getEmail(), e);
            return createErrorResponse("Selenium注册失败: " + e.getMessage());
        }
    }



    /**
     * 处理简单的人类验证复选框
     */
    private void handleSimpleHumanVerification(WebDriver driver, WebDriverWait wait) {
        try {
            // 查找"Verify you are human"复选框
            List<WebElement> checkboxes = driver.findElements(By.xpath("//input[@type='checkbox']"));

            for (WebElement checkbox : checkboxes) {
                if (!checkbox.isSelected()) {
                    // 使用JavaScript点击复选框
                    ((JavascriptExecutor) driver).executeScript("arguments[0].click();", checkbox);
                    log.info("已点击人类验证复选框");
                    Thread.sleep(1000);
                    break;
                }
            }
        } catch (Exception e) {
            log.warn("处理人类验证复选框时出错: {}", e.getMessage());
        }
    }



    /**
     * 等待邮箱验证码
     */
    private String waitForVerificationCode(EmailBatchTool.EmailAccount account) {
        try {
            // 等待邮件到达
            Thread.sleep(10000);
            
            // 获取验证码
            for (int i = 0; i < 10; i++) {
                String code = emailBatchTool.getVerificationCode(account);
                if (code != null && !code.isEmpty()) {
                    log.info("获取到验证码: {} -> {}", account.getEmail(), code);
                    return code;
                }
                Thread.sleep(5000); // 等待5秒后重试
            }
            
            log.warn("未能获取到验证码: {}", account.getEmail());
            return null;
            
        } catch (Exception e) {
            log.error("等待验证码时出错", e);
            return null;
        }
    }



    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        return Map.of(
            "success", false,
            "message", message
        );
    }

    /**
     * 关闭资源
     */
    public void shutdown() {
        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
