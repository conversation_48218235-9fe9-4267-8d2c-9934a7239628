<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment账号管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #233A3E 0%, #1a2a2e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.8;
            font-size: 1.1rem;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #233A3E;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }

        .controls {
            padding: 20px 30px;
            background: white;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .search-box {
            flex: 1;
            max-width: 300px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #233A3E;
            box-shadow: 0 0 0 3px rgba(35, 58, 62, 0.1);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #233A3E;
            color: white;
        }

        .btn-primary:hover {
            background: #1a2a2e;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .add-form {
            background: #f8f9fa;
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
            display: none;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-group input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .form-group input:focus {
            outline: none;
            border-color: #233A3E;
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .table-container {
            padding: 0 30px 30px;
            overflow-x: auto;
        }

        .accounts-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .accounts-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #233A3E;
            border-bottom: 2px solid #e9ecef;
        }

        .accounts-table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }

        .accounts-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .token-cell {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            position: relative;
        }

        .token-cell:hover {
            background: #f0f0f0;
        }

        .quota-info {
            font-size: 13px;
        }

        .quota-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 5px 0;
        }

        .quota-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            transition: width 0.3s ease;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #233A3E;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 50px;
            color: #666;
        }

        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .toast.show {
            transform: translateX(0);
        }

        .toast.success {
            background: #28a745;
        }

        .toast.error {
            background: #dc3545;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .search-box {
                max-width: none;
            }
            
            .accounts-table {
                font-size: 12px;
            }
            
            .accounts-table th,
            .accounts-table td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Augment账号管理</h1>
            <p>管理您的Augment AI账号和Token信息</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalAccounts">-</div>
                <div class="stat-label">总账号数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeAccounts">-</div>
                <div class="stat-label">活跃账号</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalQuota">-</div>
                <div class="stat-label">总额度</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="usedQuota">-</div>
                <div class="stat-label">已使用</div>
            </div>
        </div>

        <div class="controls">
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索账号或邮箱...">
                <span class="search-icon">🔍</span>
            </div>
            <div>
                <button class="btn btn-success" onclick="toggleAddForm()">➕ 添加账号</button>
                <button class="btn btn-primary" onclick="exportAccounts()">📥 导出数据</button>
                <button class="btn btn-primary" onclick="refreshAccounts()">🔄 刷新</button>
            </div>
        </div>

        <div class="add-form" id="addForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="emailInput">邮箱账号</label>
                    <input type="email" id="emailInput" placeholder="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="tokenInput">Token</label>
                    <input type="text" id="tokenInput" placeholder="aug_xxxxxxxxxx" required>
                </div>
                <div class="form-group">
                    <label for="quotaInput">总额度</label>
                    <input type="text" id="quotaInput" placeholder="100,000 tokens" required>
                </div>
                <div class="form-group">
                    <label for="usedInput">已使用</label>
                    <input type="text" id="usedInput" placeholder="0 tokens" value="0 tokens">
                </div>
            </div>
            <div class="form-actions">
                <button class="btn btn-primary" onclick="saveAccount()">💾 保存</button>
                <button class="btn" onclick="toggleAddForm()">❌ 取消</button>
            </div>
        </div>

        <div class="table-container">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在加载账号信息...</p>
            </div>

            <div id="emptyState" class="empty-state" style="display: none;">
                <p>📭 暂无账号信息</p>
                <p>点击"添加账号"按钮开始添加</p>
            </div>

            <table class="accounts-table" id="accountsTable" style="display: none;">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>邮箱账号</th>
                        <th>Token</th>
                        <th>总额度</th>
                        <th>已使用</th>
                        <th>创建时间</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="accountsBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        let accountsData = [];

        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', function() {
            loadAccounts();

            // 搜索功能
            document.getElementById('searchInput').addEventListener('input', function() {
                filterAccounts(this.value);
            });
        });

        // 加载账号数据
        async function loadAccounts() {
            try {
                showLoading(true);
                const response = await fetch('/api/accounts');
                const data = await response.json();

                if (data.success) {
                    accountsData = data.accounts || [];
                    renderAccounts(accountsData);
                    updateStats(accountsData);
                } else {
                    console.error('获取账号数据失败:', data.message);
                    showEmptyState();
                }
            } catch (error) {
                console.error('请求失败:', error);
                showEmptyState();
            } finally {
                showLoading(false);
            }
        }

        // 渲染账号表格
        function renderAccounts(accounts) {
            const tbody = document.getElementById('accountsBody');

            if (accounts.length === 0) {
                showEmptyState();
                return;
            }

            tbody.innerHTML = accounts.map(account => `
                <tr>
                    <td>${account.id}</td>
                    <td>${account.email}</td>
                    <td class="token-cell" title="${account.token}" onclick="copyToken('${account.token}')">
                        ${account.token}
                    </td>
                    <td>${account.quota}</td>
                    <td>${account.used}</td>
                    <td>${account.createTime}</td>
                    <td>
                        <span class="status-badge status-${account.status}">
                            ${account.status === 'active' ? '活跃' : '停用'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-primary" onclick="editAccount('${account.id}')" style="margin-right: 5px; padding: 8px 12px; font-size: 12px;">编辑</button>
                        <button class="btn btn-primary" onclick="copyToken('${account.token}')" style="margin-right: 5px; padding: 8px 12px; font-size: 12px;">复制Token</button>
                        <button class="btn btn-danger" onclick="deleteAccount('${account.id}')" style="padding: 8px 12px; font-size: 12px;">删除</button>
                    </td>
                </tr>
            `).join('');

            document.getElementById('accountsTable').style.display = 'table';
            document.getElementById('emptyState').style.display = 'none';
        }

        // 更新统计信息
        function updateStats(accounts) {
            const totalAccounts = accounts.length;
            const activeAccounts = accounts.filter(acc => acc.status === 'active').length;

            // 计算总额度和已使用额度
            let totalQuota = 0;
            let usedQuota = 0;

            accounts.forEach(acc => {
                const quota = parseInt(acc.quota.replace(/[^\d]/g, '')) || 0;
                const used = parseInt(acc.used.replace(/[^\d]/g, '')) || 0;
                totalQuota += quota;
                usedQuota += used;
            });

            document.getElementById('totalAccounts').textContent = totalAccounts;
            document.getElementById('activeAccounts').textContent = activeAccounts;
            document.getElementById('totalQuota').textContent = formatNumber(totalQuota);
            document.getElementById('usedQuota').textContent = formatNumber(usedQuota);
        }

        // 编辑账号
        function editAccount(accountId) {
            const account = accountsData.find(acc => acc.id == accountId);
            if (!account) {
                showToast('未找到指定账号', 'error');
                return;
            }

            // 填充表单数据
            document.getElementById('emailInput').value = account.email;
            document.getElementById('tokenInput').value = account.token;
            document.getElementById('quotaInput').value = account.quota;
            document.getElementById('usedInput').value = account.used;

            // 显示表单并标记为编辑模式
            const form = document.getElementById('addForm');
            form.style.display = 'block';
            form.setAttribute('data-edit-id', accountId);

            // 修改按钮文本
            const saveButton = form.querySelector('button[onclick="saveAccount()"]');
            saveButton.textContent = '💾 更新';
            saveButton.onclick = () => updateAccount(accountId);
        }

        // 格式化数字
        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        // 搜索过滤
        function filterAccounts(searchTerm) {
            const filtered = accountsData.filter(account =>
                account.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                account.token.toLowerCase().includes(searchTerm.toLowerCase())
            );
            renderAccounts(filtered);
        }

        // 显示加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            if (!show) {
                document.getElementById('accountsTable').style.display = accountsData.length > 0 ? 'table' : 'none';
            }
        }

        // 显示空状态
        function showEmptyState() {
            document.getElementById('emptyState').style.display = 'block';
            document.getElementById('accountsTable').style.display = 'none';
        }

        // 切换添加表单
        function toggleAddForm() {
            const form = document.getElementById('addForm');
            const isVisible = form.style.display === 'block';
            form.style.display = isVisible ? 'none' : 'block';

            if (!isVisible) {
                // 清空表单并重置为添加模式
                resetForm();
            }
        }

        // 重置表单
        function resetForm() {
            const form = document.getElementById('addForm');
            document.getElementById('emailInput').value = '';
            document.getElementById('tokenInput').value = '';
            document.getElementById('quotaInput').value = '';
            document.getElementById('usedInput').value = '0 tokens';

            // 重置为添加模式
            form.removeAttribute('data-edit-id');
            const saveButton = form.querySelector('button[onclick*="Account"]');
            saveButton.textContent = '💾 保存';
            saveButton.onclick = saveAccount;
        }

        // 保存账号
        async function saveAccount() {
            const email = document.getElementById('emailInput').value.trim();
            const token = document.getElementById('tokenInput').value.trim();
            const quota = document.getElementById('quotaInput').value.trim();
            const used = document.getElementById('usedInput').value.trim();

            if (!email || !token || !quota) {
                showToast('请填写所有必填字段', 'error');
                return;
            }

            const accountData = {
                email: email,
                token: token,
                quota: quota,
                used: used
            };

            try {
                const response = await fetch('/api/accounts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(accountData)
                });

                const result = await response.json();

                if (result.success) {
                    showToast('账号保存成功', 'success');
                    toggleAddForm();
                    loadAccounts(); // 重新加载数据
                } else {
                    showToast('保存失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('保存账号失败:', error);
                showToast('保存失败: ' + error.message, 'error');
            }
        }

        // 更新账号
        async function updateAccount(accountId) {
            const email = document.getElementById('emailInput').value.trim();
            const token = document.getElementById('tokenInput').value.trim();
            const quota = document.getElementById('quotaInput').value.trim();
            const used = document.getElementById('usedInput').value.trim();

            if (!email || !token || !quota) {
                showToast('请填写所有必填字段', 'error');
                return;
            }

            const accountData = {
                id: parseInt(accountId),
                email: email,
                token: token,
                quota: quota,
                used: used
            };

            try {
                const response = await fetch(`/api/accounts/${accountId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(accountData)
                });

                const result = await response.json();

                if (result.success) {
                    showToast('账号更新成功', 'success');
                    toggleAddForm();
                    loadAccounts(); // 重新加载数据
                } else {
                    showToast('更新失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('更新账号失败:', error);
                showToast('更新失败: ' + error.message, 'error');
            }
        }

        // 刷新数据
        function refreshAccounts() {
            loadAccounts();
        }

        // 复制Token
        function copyToken(token) {
            navigator.clipboard.writeText(token).then(() => {
                showToast('Token已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = token;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showToast('Token已复制到剪贴板', 'success');
            });
        }

        // 删除账号
        async function deleteAccount(accountId) {
            if (!confirm('确定要删除这个账号吗？此操作不可恢复！')) {
                return;
            }

            try {
                const response = await fetch(`/api/accounts/${accountId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showToast('账号删除成功', 'success');
                    loadAccounts(); // 重新加载数据
                } else {
                    showToast('删除失败: ' + result.message, 'error');
                }
            } catch (error) {
                console.error('删除账号失败:', error);
                showToast('删除失败: ' + error.message, 'error');
            }
        }

        // 导出数据
        function exportAccounts() {
            if (accountsData.length === 0) {
                showToast('没有数据可导出', 'error');
                return;
            }

            const csvContent = "data:text/csv;charset=utf-8,"
                + "ID,邮箱,Token,总额度,已使用,创建时间,状态\n"
                + accountsData.map(acc =>
                    `${acc.id},"${acc.email}","${acc.token}","${acc.quota}","${acc.used}","${acc.createTime}","${acc.status}"`
                ).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `augment-accounts-${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showToast('数据导出成功', 'success');
        }

        // 显示提示消息
        function showToast(message, type = 'success') {
            // 移除现有的toast
            const existingToast = document.querySelector('.toast');
            if (existingToast) {
                existingToast.remove();
            }

            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);

            // 显示toast
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // 3秒后隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
