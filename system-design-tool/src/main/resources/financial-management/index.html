<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>个人财务管理系统</title>
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
  <link rel="stylesheet" href="/financial/style.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <link rel="icon" href="/financial/favicon.svg">
</head>
<body>
  <!-- 顶部只保留退出链接 -->
  <div class="top-bar">
    <div class="logout-container">
      <a href="/financial/logout" class="logout-link" title="安全退出">
        <i class="fas fa-sign-out-alt"></i>
      </a>
    </div>
  </div>

  <!-- 顶部导航 -->
  <header class="app-header">
    <div class="header-content">
      <h1>个人财务管理系统</h1>
      <div class="header-actions">
        <div class="tab-navigation">
          <button class="tab-btn active" data-tab="expenses" id="expenses-tab">
            <i class="fas fa-wallet"></i>
            <span>支出管理</span>
          </button>
          <button class="tab-btn" data-tab="assets" id="assets-tab">
            <i class="fas fa-chart-pie"></i>
            <span>资产总结</span>
          </button>
        </div>
      </div>
    </div>
  </header>

  <!-- 主要内容区 -->
  <div class="main-container">
    <!-- 支出管理标签页 -->
    <div class="tab-content active" id="expenses-content">
      <!-- 仪表盘区域 -->
      <section class="dashboard-section">
        <div class="dashboard-cards">
          <div class="dashboard-card total-card">
            <div class="card-icon">
              <i class="fas fa-wallet"></i>
            </div>
            <div class="card-content">
              <h3>本月支出</h3>
              <div class="card-value" id="total-expense">¥ 0.00</div>
            </div>
          </div>
        </div>
      </section>

      <!-- 记录与图表区域 -->
      <div class="content-grid">
        <!-- 新增支出表单 -->
        <section class="form-section">
          <div class="section-header">
            <h2>新增支出</h2>
          </div>
          <form id="expense-form" class="expense-form">
            <div class="form-row">
              <div class="form-group">
                <label for="expense-type">
                  <i class="fas fa-tag"></i> 支出类型
                </label>
                <select id="expense-type" required>
                  <option value="">请选择类型</option>
                  <option value="icbc_credit">工行信用卡</option>
                  <option value="cmb_credit">招行信用卡</option>
                  <option value="alipay">支付宝</option>
                  <option value="wechat">微信 (包含房租和水电费)</option>
                </select>
              </div>
              <div class="form-group">
                <label for="expense-amount">
                  <i class="fas fa-yen-sign"></i> 金额 (元)
                </label>
                <input type="number" id="expense-amount" step="0.01" placeholder="输入金额" required>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label for="expense-date">
                  <i class="fas fa-calendar"></i> 日期
                </label>
                <input type="date" id="expense-date" required>
              </div>
              <div class="form-group">
                <label for="expense-note">
                  <i class="fas fa-sticky-note"></i> 备注
                </label>
                <input type="text" id="expense-note" placeholder="添加备注(可选)">
              </div>
            </div>
            <div class="form-actions">
              <button type="submit" class="btn-primary btn-large">
                <i class="fas fa-plus"></i> 添加支出
              </button>
            </div>
          </form>
        </section>

        <!-- 趋势图卡片 -->
        <section class="chart-section">
          <div class="section-header">
            <h2>月度消费趋势</h2>
            <div class="chart-controls">
              <div class="chart-control-group">
                <div class="chart-type-toggle">
                  <button class="chart-type-btn active" data-type="bar" title="柱状图">
                    <i class="fas fa-chart-bar"></i>
                  </button>
                  <button class="chart-type-btn" data-type="line" title="折线图">
                    <i class="fas fa-chart-line"></i>
                  </button>
                </div>
              </div>
              <div class="chart-control-group">
                <select id="chart-period" class="chart-period-select">
                  <option value="6">近6个月</option>
                  <option value="12" selected>近12个月</option>
                  <option value="24">近24个月</option>
                </select>
              </div>
            </div>
          </div>
          <div class="chart-container">
            <canvas id="expense-trend-chart"></canvas>
          </div>
          <div class="chart-legend" id="chart-legend"></div>
        </section>
      </div>

      <!-- 支出记录区域 -->
      <section class="expenses-section">
        <div class="section-header with-actions">
          <h2>支出记录</h2>
          <div class="section-actions">
            <div class="filter-dropdown">
              <select id="expense-filter">
                <option value="all">全部记录</option>
                <option value="current-month" selected>本月</option>
                <option value="last-month">上月</option>
              </select>
            </div>
            <button class="btn-icon btn-danger" id="clear-expense-btn" title="清空记录">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        </div>
        
        <div class="expenses-grid" id="expense-list">
          <!-- 支出记录将由JavaScript生成并平铺展示 -->
        </div>
      </section>
    </div>

    <!-- 资产总结标签页 -->
    <div class="tab-content" id="assets-content">
      <!-- 汇率信息卡片 -->
      <div class="exchange-rates-card" id="exchange-rates">
        <div class="card-header">
          <div class="card-title">
            <span class="card-icon">💱</span>
            <span>实时汇率</span>
          </div>
          <div class="last-update" id="rate-update-time">--</div>
        </div>
        <div class="rates-grid">
          <div class="rate-item">
            <div class="rate-pair">USD/CNY</div>
            <div class="rate-value" data-pair="usd">加载中...</div>
          </div>
          <div class="rate-item">
            <div class="rate-pair">HKD/CNY</div>
            <div class="rate-value" data-pair="hkd">加载中...</div>
          </div>
        </div>
      </div>

      <!-- 总资产卡片 -->
      <div class="total-asset-card">
        <div class="total-asset-icon">💰</div>
        <div class="total-asset-info">
          <div class="total-asset-label">总资产</div>
          <div class="total-asset-amount" id="total-amount">¥ 0.00</div>
        </div>
        <div class="action-buttons">
          <button class="btn-refresh" id="refresh-price-btn" title="刷新加密货币价格">
            <span class="btn-icon">₿</span>
            刷新价格
          </button>
          <button class="btn-save" id="save-btn">
            <span class="btn-icon">💾</span>
            保存
          </button>
          <button class="btn-clear" id="clear-btn">
            <span class="btn-icon">🗑️</span>
            清空
          </button>
        </div>
      </div>

      <!-- 资产网格 -->
      <div class="asset-grid" id="asset-grid">
        <!-- 加载状态 -->
        <div class="loading-state" id="asset-loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载资产数据...</div>
        </div>
      </div>

      <!-- 趋势图卡片 -->
      <div class="trend-card">
        <div class="trend-header">
          <h3>资产趋势</h3>
          <div class="trend-controls">
            <select id="trend-period" class="trend-select">
              <option value="7">最近7天</option>
              <option value="30" selected>最近30天</option>
              <option value="90">最近90天</option>
              <option value="all">全部</option>
            </select>
          </div>
        </div>
        <div class="trend-chart-container">
          <canvas id="trend-chart"></canvas>
        </div>
      </div>

      <!-- 历史记录卡片 -->
      <div class="history-card">
        <div class="history-header">
          <h3>历史记录</h3>
          <div class="history-actions">
            <span class="history-count" id="history-count">0 条记录</span>
            <button class="btn-clear-history" id="clear-history-btn" title="清空所有历史记录">
              <span>🗑️</span>
            </button>
          </div>
        </div>
        <div class="history-list" id="history-list">
          <!-- 历史记录将由JavaScript生成 -->
        </div>
      </div>
    </div>
  </div>

  <!-- 通用弹窗模板 -->
  <!-- 编辑弹窗 -->
  <div class="modal-overlay" id="edit-modal">
    <div class="modal-container">
      <div class="modal-header">
        <div class="modal-title">
          <div class="modal-icon" id="modal-icon"></div>
          <span id="modal-title-text">编辑</span>
        </div>
        <button class="close-btn" id="close-modal">&times;</button>
      </div>
      <div class="modal-body" id="modal-body">
        <!-- 编辑内容将由JavaScript生成 -->
      </div>
      <div class="modal-footer">
        <button class="btn-cancel" id="cancel-btn">取消</button>
        <button class="btn-confirm" id="confirm-btn">确认</button>
      </div>
    </div>
  </div>

  <!-- 详情弹窗 -->
  <div class="modal-overlay" id="detail-modal">
    <div class="modal-container modal-large">
      <div class="modal-header">
        <div class="modal-title">
          <span id="detail-modal-title">详情</span>
        </div>
        <button class="close-btn" id="close-detail-modal">&times;</button>
      </div>
      <div class="modal-body" id="detail-modal-body">
        <!-- 详情内容将由JavaScript生成 -->
      </div>
    </div>
  </div>

  <!-- 确认删除弹窗 -->
  <div class="modal-overlay" id="confirm-modal">
    <div class="modal-container modal-small">
      <div class="modal-header">
        <h3>确认删除</h3>
        <button class="close-btn" id="close-confirm-modal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <p id="confirm-message">确定要删除这条记录吗？</p>
      </div>
      <div class="modal-footer">
        <button class="btn-secondary" id="confirm-cancel-btn">取消</button>
        <button class="btn-danger" id="confirm-delete-btn">删除</button>
      </div>
    </div>
  </div>

  <!-- 提示消息 -->
  <div class="toast" id="toast"></div>

  <script src="/financial/script.js"></script>
</body>
</html>
