// 全局变量和配置
let expenses = [];
let currentAssets = {};
let assetHistory = [];
let cryptoPrices = {};
let exchangeRates = {};
let lastPriceUpdate = 0;
const PRICE_CACHE_DURATION = 30000; // 30秒缓存，提高响应速度
let expenseTrendChart = null;
let assetTrendChart = null;
let deleteRecordId = null;
let currentEditingExpense = null;

// 资产渠道配置
const ASSET_CHANNELS = [
  { id: 'alipay', name: '支付宝', color: '#1677FF', icon: '支', type: 'CNY' },
  { id: 'wechat', name: '微信', color: '#1AAD19', icon: '微', type: 'CNY' },
  { id: 'icbc', name: '工商银行', color: '#C41E3A', icon: '工', type: 'CNY' },
  { id: 'cmb', name: '招商银行', color: '#E60012', icon: '招', type: 'CNY' },
  { id: 'yuhang', name: '余杭农商银行', color: '#4CAF50', icon: '余', type: 'CNY' },
  { id: 'boc', name: '中国银行', color: '#B71C1C', icon: '中', type: 'CNY' },
  { 
    id: 'hsbc', 
    name: '香港汇丰银行', 
    color: '#D32F2F', 
    icon: 'H',
    type: 'HKD',
    currency: 'HKD',
    defaultAmount: 5200.94
  },
  { 
    id: 'bochk', 
    name: '中银香港', 
    color: '#8B0000', 
    icon: '港',
    type: 'HKD',
    currency: 'HKD',
    defaultAmount: 2406.65
  },
  { 
    id: 'tiger', 
    name: '老虎证券', 
    color: '#FF9800', 
    icon: '虎',
    type: 'USD',
    currency: 'USD'
  },
  { 
    id: 'longbridge', 
    name: '长桥证券', 
    color: '#9C27B0', 
    icon: '桥',
    type: 'USD',
    currency: 'USD'
  },
  { 
    id: 'futu', 
    name: '富途牛牛', 
    color: '#795548', 
    icon: '牛',
    type: 'USD',
    currency: 'USD'
  },
  { 
    id: 'binance', 
    name: '币安', 
    color: '#F3BA2F', 
    icon: '币', 
    type: 'CRYPTO',
    crypto: true,
    defaultAssets: {
      BTC: 0.03118642,
      PEPE: 167010986.47615952
    },
    editable: true
  },
  { 
    id: 'victory', 
    name: '胜利通', 
    color: '#1976D2', 
    icon: 'V',
    type: 'CRYPTO',
    crypto: true,
    defaultAssets: {
      BTC: 0.0067
    },
    editable: true
  },
  { 
    id: 'wallet', 
    name: '钱包', 
    color: '#8BC34A', 
    icon: '包',
    type: 'CRYPTO',
    crypto: true,
    defaultAssets: {
      BTC: 0.00922
    },
    editable: true
  }
];

// 支出类型配置
const EXPENSE_TYPES = {
  'icbc_credit': { name: '工行信用卡', color: '#C41E3A', icon: 'fas fa-credit-card' },
  'cmb_credit': { name: '招行信用卡', color: '#E60012', icon: 'fas fa-credit-card' },
  'alipay': { name: '支付宝', color: '#1677FF', icon: 'fab fa-alipay' },
  'wechat': { name: '微信', color: '#1AAD19', icon: 'fab fa-weixin' }
};

// 主应用类
class FinancialManagementSystem {
  constructor() {
    this.currentTab = 'expenses';
    this.init();
  }

  init() {
    this.initTabNavigation();
    this.loadData();
    this.setDefaultDate();
    this.bindEvents();
    this.updateUI();
    this.startAutoSave();
  }

  // 初始化标签页导航
  initTabNavigation() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const tabName = btn.dataset.tab;

        // 如果点击的是当前标签页，不需要重新加载
        if (this.currentTab === tabName) {
          return;
        }

        console.log(`切换到标签页: ${tabName}`);

        // 更新按钮状态
        tabBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');

        // 更新内容显示
        tabContents.forEach(content => {
          content.classList.remove('active');
        });
        document.getElementById(`${tabName}-content`).classList.add('active');

        const previousTab = this.currentTab;
        this.currentTab = tabName;

        // 只在切换到资产标签页时才更新资产UI和加载汇率
        if (tabName === 'assets' && previousTab !== 'assets') {
          this.loadExchangeRates(); // 只在资产tab时加载汇率
          this.updateAssetUI();
        } else if (tabName === 'expenses' && previousTab !== 'expenses') {
          this.updateExpenseUI();
        }
      });
    });
  }

  // 加载数据
  async loadData() {
    console.log('开始加载数据...');
    await Promise.all([
      this.loadExpenses(),
      this.loadAssets(),
      this.loadAssetHistory()
    ]);

    // 强制重新初始化默认资产，确保加密货币显示
    console.log('强制重新初始化默认资产...');
    this.initializeDefaultAssets();

    console.log('数据加载完成，当前资产状态:', currentAssets);
  }

  // 加载支出数据
  async loadExpenses() {
    try {
      const response = await fetch('/api/financial/expenses');
      if (response.ok) {
        const data = await response.json();
        expenses = Array.isArray(data) ? data : [];
        console.log('加载支出数据成功:', expenses.length, '条记录');
      } else {
        console.warn('服务器加载支出数据失败，使用本地数据');
        expenses = [];
      }
    } catch (error) {
      console.error('加载支出数据失败:', error);
      const localData = localStorage.getItem('expenses');
      try {
        const parsed = JSON.parse(localData || '[]');
        expenses = Array.isArray(parsed) ? parsed : [];
        console.log('使用本地支出数据:', expenses.length, '条记录');
      } catch (parseError) {
        console.error('解析本地支出数据失败:', parseError);
        expenses = [];
      }
    }
  }

  // 加载资产数据
  async loadAssets() {
    try {
      const response = await fetch('/api/financial/current-assets');
      if (response.ok) {
        const data = await response.json();
        currentAssets = data || {};
        console.log('加载当前资产成功:', Object.keys(currentAssets).length, '个渠道');
      } else {
        console.warn('服务器加载当前资产失败，使用本地数据');
        currentAssets = {};
      }
    } catch (error) {
      console.error('加载当前资产失败:', error);
      const localData = localStorage.getItem('currentAssets');
      try {
        const parsed = JSON.parse(localData || '{}');
        currentAssets = parsed || {};
        console.log('使用本地当前资产数据:', Object.keys(currentAssets).length, '个渠道');
      } catch (parseError) {
        console.error('解析本地当前资产数据失败:', parseError);
        currentAssets = {};
      }
    }

    // 初始化默认资产
    this.initializeDefaultAssets();
  }

  // 加载资产历史
  async loadAssetHistory() {
    try {
      const response = await fetch('/api/financial/asset-history');
      if (response.ok) {
        const data = await response.json();
        assetHistory = Array.isArray(data) ? data : [];
        console.log('加载资产历史成功:', assetHistory.length, '条记录');
      } else {
        console.warn('服务器加载资产历史失败，使用本地数据');
        assetHistory = [];
      }
    } catch (error) {
      console.error('加载资产历史失败:', error);
      const localData = localStorage.getItem('assetHistory');
      try {
        const parsed = JSON.parse(localData || '[]');
        assetHistory = Array.isArray(parsed) ? parsed : [];
        console.log('使用本地资产历史数据:', assetHistory.length, '条记录');
      } catch (parseError) {
        console.error('解析本地资产历史数据失败:', parseError);
        assetHistory = [];
      }
    }

    // 加载完成后立即渲染历史记录
    if (this.currentTab === 'assets') {
      setTimeout(() => {
        this.renderAssetHistory();
      }, 100);
    }
  }

  // 初始化默认资产
  initializeDefaultAssets() {
    ASSET_CHANNELS.forEach(channel => {
      if (!currentAssets[channel.id]) {
        if (channel.crypto && channel.defaultAssets) {
          // 确保加密货币资产使用默认值
          currentAssets[channel.id] = { ...channel.defaultAssets };
          console.log(`初始化 ${channel.name} 默认资产:`, currentAssets[channel.id]);
        } else if (channel.defaultAmount) {
          currentAssets[channel.id] = channel.defaultAmount;
        } else {
          currentAssets[channel.id] = channel.crypto ? {} : 0;
        }
      } else if (channel.crypto && channel.defaultAssets) {
        // 如果已存在但缺少某些币种，补充默认值
        Object.entries(channel.defaultAssets).forEach(([symbol, defaultAmount]) => {
          if (currentAssets[channel.id][symbol] === undefined) {
            currentAssets[channel.id][symbol] = defaultAmount;
            console.log(`补充 ${channel.name} 的 ${symbol} 默认值:`, defaultAmount);
          }
        });
      }
    });
  }

  // 设置默认日期
  setDefaultDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const dateInput = document.getElementById('expense-date');
    if (dateInput) {
      dateInput.value = `${year}-${month}-${day}`;
    }
  }

  // 绑定事件
  bindEvents() {
    this.bindExpenseEvents();
    this.bindAssetEvents();
    this.bindModalEvents();
  }

  // 绑定支出相关事件
  bindExpenseEvents() {
    const expenseForm = document.getElementById('expense-form');
    if (expenseForm) {
      expenseForm.addEventListener('submit', (e) => {
        e.preventDefault();
        this.addExpense();
      });
    }

    const clearExpenseBtn = document.getElementById('clear-expense-btn');
    if (clearExpenseBtn) {
      clearExpenseBtn.addEventListener('click', () => {
        this.clearExpenses();
      });
    }

    const expenseFilter = document.getElementById('expense-filter');
    if (expenseFilter) {
      expenseFilter.addEventListener('change', () => {
        this.renderExpenseList();
      });
    }

    const chartPeriod = document.getElementById('chart-period');
    if (chartPeriod) {
      chartPeriod.addEventListener('change', () => {
        this.renderExpenseTrendChart();
      });
    }

    // 图表类型切换
    const chartTypeBtns = document.querySelectorAll('.chart-type-btn');
    chartTypeBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        chartTypeBtns.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        this.renderExpenseTrendChart();
      });
    });
  }

  // 绑定资产相关事件
  bindAssetEvents() {
    const saveBtn = document.getElementById('save-btn');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => {
        this.saveAssets();
      });
    }

    const clearBtn = document.getElementById('clear-btn');
    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        this.clearAssets();
      });
    }

    const clearHistoryBtn = document.getElementById('clear-history-btn');
    if (clearHistoryBtn) {
      clearHistoryBtn.addEventListener('click', () => {
        this.clearAssetHistory();
      });
    }

    const refreshPriceBtn = document.getElementById('refresh-price-btn');
    if (refreshPriceBtn) {
      refreshPriceBtn.addEventListener('click', () => {
        this.refreshCryptoPrices();
      });
    }

    const trendPeriod = document.getElementById('trend-period');
    if (trendPeriod) {
      trendPeriod.addEventListener('change', () => {
        this.renderAssetTrendChart();
      });
    }
  }

  // 绑定弹窗事件
  bindModalEvents() {
    // 关闭弹窗事件
    const closeModalBtns = document.querySelectorAll('.close-btn, .btn-cancel');
    closeModalBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        this.closeModal();
      });
    });

    // 详情弹窗关闭事件
    const closeDetailBtn = document.getElementById('close-detail-modal');
    if (closeDetailBtn) {
      closeDetailBtn.addEventListener('click', () => {
        this.closeModal();
      });
    }

    // 点击遮罩关闭弹窗
    const modalOverlays = document.querySelectorAll('.modal-overlay');
    modalOverlays.forEach(overlay => {
      overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
          this.closeModal();
        }
      });
    });

    // 确认按钮
    const confirmBtn = document.getElementById('confirm-btn');
    if (confirmBtn) {
      confirmBtn.addEventListener('click', () => {
        this.handleModalConfirm();
      });
    }

    // 删除确认
    const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
    if (confirmDeleteBtn) {
      confirmDeleteBtn.addEventListener('click', () => {
        this.handleDeleteConfirm();
      });
    }

    const confirmCancelBtn = document.getElementById('confirm-cancel-btn');
    if (confirmCancelBtn) {
      confirmCancelBtn.addEventListener('click', () => {
        this.closeModal();
      });
    }
  }

  // 更新UI - 优化版本，只在初始化时调用
  updateUI() {
    console.log(`初始化UI，当前标签页: ${this.currentTab}`);

    // 初始化时只更新当前显示的标签页
    if (this.currentTab === 'expenses') {
      this.updateExpenseUI();
    } else if (this.currentTab === 'assets') {
      // 资产页面使用异步加载，避免阻塞
      setTimeout(() => {
        this.loadExchangeRates(); // 加载汇率
        this.updateAssetUI();
      }, 0);
    }
  }

  // 更新支出UI
  updateExpenseUI() {
    this.updateTotalExpense();
    this.renderExpenseList();
    this.renderExpenseTrendChart();
  }

  // 更新资产UI - 优化版本
  async updateAssetUI() {
    console.log('开始更新资产UI...');

    // 1. 立即显示骨架屏，避免空白页面
    this.showAssetSkeleton();

    // 2. 先渲染基础数据（不依赖API的部分）
    this.renderAssetGridWithoutPrices();
    this.updateTotalAssetsWithoutPrices();

    // 3. 异步加载价格数据
    this.loadPricesAsync();

    // 4. 渲染其他组件
    this.renderAssetHistory();

    // 5. 延迟渲染趋势图
    setTimeout(() => {
      this.renderAssetTrendChart();
    }, 100);

    // 6. 不再自动创建初始记录，等待用户手动保存
    console.log('初始化完成，历史记录数量:', assetHistory.length);
  }

  // 显示资产骨架屏
  showAssetSkeleton() {
    const gridEl = document.getElementById('asset-grid');
    if (!gridEl) return;

    const skeletonCards = ASSET_CHANNELS.map(channel => `
      <div class="skeleton-card">
        <div class="skeleton-header">
          <div class="skeleton-icon"></div>
          <div style="flex: 1;">
            <div class="skeleton-text medium" style="width: 80%;"></div>
            <div class="skeleton-text small" style="width: 60%;"></div>
          </div>
        </div>
        <div class="skeleton-text large" style="width: 70%;"></div>
        <div class="skeleton-text small" style="width: 50%;"></div>
      </div>
    `).join('');

    gridEl.innerHTML = skeletonCards;
  }

  // 异步加载价格数据
  async loadPricesAsync() {
    try {
      console.log('异步加载价格数据...');
      await this.updateCryptoPrices();

      // 价格加载完成后，重新渲染资产网格
      this.renderAssetGrid();
      this.updateTotalAssets();

      console.log('价格数据加载完成');
    } catch (error) {
      console.error('价格数据加载失败:', error);
      // 即使价格加载失败，也要渲染基础界面
      this.renderAssetGrid();
      this.updateTotalAssets();
    }
  }

  // 检查记录是否重复（比较资产数据和总金额）
  isRecordDuplicate(newRecord, lastRecord) {
    if (!lastRecord) return false;

    // 比较总金额（允许小数点误差）
    const totalDiff = Math.abs(newRecord.totalCNY - lastRecord.totalCNY);
    if (totalDiff > 0.01) return false;

    // 比较资产数据
    const newAssetsStr = JSON.stringify(newRecord.assets);
    const lastAssetsStr = JSON.stringify(lastRecord.assets);

    const isDuplicate = newAssetsStr === lastAssetsStr;

    if (isDuplicate) {
      console.log('检测到重复记录:', {
        newTotal: newRecord.totalCNY,
        lastTotal: lastRecord.totalCNY,
        assetsMatch: newAssetsStr === lastAssetsStr
      });
    }

    return isDuplicate;
  }

  // 创建初始资产记录（已废弃，不再自动创建）
  createInitialAssetRecord() {
    console.log('createInitialAssetRecord 已废弃，不再自动创建初始记录');
    // 不再自动创建记录，等待用户手动保存
  }

  // 开始自动保存和价格更新
  startAutoSave() {
    // 自动保存数据（只保存当前状态，不创建历史记录）
    setInterval(() => {
      this.saveExpenses();
      this.saveAssetsData(); // 只保存当前资产，不创建历史记录
    }, 30000); // 每30秒自动保存一次

    // 定期更新加密货币价格
    setInterval(() => {
      if (this.currentTab === 'assets') {
        this.updateCryptoPrices(false); // 不强制更新，遵循缓存策略
      }
    }, 60000); // 每60秒检查一次价格更新

    // 每5分钟强制更新一次价格
    setInterval(() => {
      this.updateCryptoPrices(true);
    }, 300000); // 每5分钟强制更新一次
  }

  // 工具方法
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  }

  formatDate(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  formatNumber(num, decimals = 2) {
    return parseFloat(num).toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  showToast(message, type = 'success') {
    const toast = document.getElementById('toast');
    if (!toast) {
      console.error('Toast element not found');
      alert(message); // 降级方案
      return;
    }

    toast.textContent = message;
    toast.className = `toast ${type}`;
    toast.classList.add('show');

    setTimeout(() => {
      toast.classList.remove('show');
    }, 3000);
  }

  closeModal() {
    const modals = document.querySelectorAll('.modal-overlay');
    modals.forEach(modal => {
      modal.classList.remove('show');
    });
    currentEditingExpense = null;
    deleteRecordId = null;
  }

  // 支出管理方法
  addExpense() {
    const typeEl = document.getElementById('expense-type');
    const amountEl = document.getElementById('expense-amount');
    const dateEl = document.getElementById('expense-date');
    const noteEl = document.getElementById('expense-note');

    if (!typeEl || !amountEl || !dateEl || !noteEl) {
      console.error('支出表单元素未找到');
      this.showToast('系统错误，请刷新页面重试', 'error');
      return;
    }

    const type = typeEl.value;
    const amount = parseFloat(amountEl.value);
    const date = dateEl.value;
    const note = noteEl.value;

    if (!type || !amount || !date) {
      this.showToast('请填写完整信息', 'error');
      return;
    }

    const expense = {
      id: this.generateId(),
      type,
      amount,
      date,
      note,
      timestamp: new Date().toISOString()
    };

    expenses.unshift(expense);
    this.saveExpenses();
    this.updateExpenseUI();

    // 清空表单
    const formEl = document.getElementById('expense-form');
    if (formEl) {
      formEl.reset();
    }
    this.setDefaultDate();

    this.showToast('支出记录添加成功');
  }

  async saveExpenses() {
    try {
      const response = await fetch('/api/financial/expenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(expenses)
      });

      if (!response.ok) {
        throw new Error('保存支出数据失败');
      }
      console.log('支出数据保存成功');
    } catch (error) {
      console.error('保存支出数据失败:', error);
      localStorage.setItem('expenses', JSON.stringify(expenses));
    }
  }

  clearExpenses() {
    if (confirm('确定要清空所有支出记录吗？此操作不可恢复。')) {
      expenses = [];
      this.saveExpenses();
      this.updateExpenseUI();
      this.showToast('支出记录已清空');
    }
  }

  updateTotalExpense() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    const total = expenses
      .filter(expense => {
        const expenseDate = new Date(expense.date);
        return expenseDate.getFullYear() === currentYear &&
               expenseDate.getMonth() + 1 === currentMonth;
      })
      .reduce((total, expense) => total + expense.amount, 0);

    const totalEl = document.getElementById('total-expense');
    if (totalEl) {
      totalEl.textContent = `¥ ${this.formatNumber(total)}`;
    }
  }

  renderExpenseList() {
    const listEl = document.getElementById('expense-list');
    if (!listEl) return;

    const filterEl = document.getElementById('expense-filter');
    const filterValue = filterEl ? filterEl.value : 'all';
    let filteredExpenses = [...expenses];

    // 应用筛选
    if (filterValue === 'current-month') {
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;

      filteredExpenses = expenses.filter(expense => {
        const expenseDate = new Date(expense.date);
        return expenseDate.getFullYear() === currentYear &&
               expenseDate.getMonth() + 1 === currentMonth;
      });
    } else if (filterValue === 'last-month') {
      const now = new Date();
      const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1);
      const lastYear = lastMonth.getFullYear();
      const lastMonthNum = lastMonth.getMonth() + 1;

      filteredExpenses = expenses.filter(expense => {
        const expenseDate = new Date(expense.date);
        return expenseDate.getFullYear() === lastYear &&
               expenseDate.getMonth() + 1 === lastMonthNum;
      });
    }

    if (filteredExpenses.length === 0) {
      listEl.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-receipt"></i>
          <h3>暂无支出记录</h3>
          <p>开始记录您的第一笔支出吧</p>
        </div>
      `;
      return;
    }

    // 如果是"全部记录"，按月份聚合显示
    if (filterValue === 'all') {
      this.renderMonthlyExpenseList(filteredExpenses);
    } else {
      this.renderDetailedExpenseList(filteredExpenses);
    }
  }

  // 渲染月度聚合列表
  renderMonthlyExpenseList(expenses) {
    const listEl = document.getElementById('expense-list');

    // 按月份聚合数据
    const monthlyData = {};
    expenses.forEach(expense => {
      const date = new Date(expense.date);
      const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          year: date.getFullYear(),
          month: date.getMonth() + 1,
          expenses: [],
          total: 0
        };
      }

      monthlyData[monthKey].expenses.push(expense);
      monthlyData[monthKey].total += expense.amount;
    });

    // 按时间倒序排列
    const sortedMonths = Object.keys(monthlyData).sort((a, b) => b.localeCompare(a));

    listEl.innerHTML = sortedMonths.map(monthKey => {
      const monthData = monthlyData[monthKey];
      const monthName = `${monthData.year}年${monthData.month}月`;

      return `
        <div class="month-summary-item" onclick="financialSystem.showMonthDetail('${monthKey}')">
          <div class="month-header">
            <div class="month-info">
              <div class="month-name">${monthName}</div>
              <div class="month-count">${monthData.expenses.length} 笔支出</div>
            </div>
            <div class="month-total">¥ ${this.formatNumber(monthData.total)}</div>
          </div>
          <div class="month-preview">
            ${this.getMonthPreview(monthData.expenses)}
          </div>
        </div>
      `;
    }).join('');
  }

  // 渲染详细支出列表
  renderDetailedExpenseList(filteredExpenses) {
    const listEl = document.getElementById('expense-list');

    listEl.innerHTML = filteredExpenses.map(expense => {
      const typeConfig = EXPENSE_TYPES[expense.type] || { name: expense.type, color: '#666', icon: 'fas fa-money-bill' };

      return `
        <div class="expense-item" data-id="${expense.id}" onclick="financialSystem.showExpenseDetail('${expense.id}')">
          <div class="expense-header">
            <div class="expense-type">
              <i class="${typeConfig.icon}" style="color: ${typeConfig.color}"></i>
              <span>${typeConfig.name}</span>
            </div>
            <div class="expense-actions">
              <button class="asset-btn" onclick="event.stopPropagation(); financialSystem.editExpense('${expense.id}')" title="编辑">
                <i class="fas fa-edit"></i>
              </button>
              <button class="asset-btn" onclick="event.stopPropagation(); financialSystem.deleteExpense('${expense.id}')" title="删除">
                <i class="fas fa-trash"></i>
              </button>
            </div>
          </div>
          <div class="expense-amount">¥ ${this.formatNumber(expense.amount)}</div>
          <div class="expense-date">${this.formatDate(expense.date)}</div>
          ${expense.note ? `<div class="expense-note">${expense.note}</div>` : ''}
        </div>
      `;
    }).join('');
  }

  // 获取月份预览信息
  getMonthPreview(monthExpenses) {
    const typeStats = {};
    monthExpenses.forEach(expense => {
      const typeConfig = EXPENSE_TYPES[expense.type] || { name: expense.type };
      const typeName = typeConfig.name;

      if (!typeStats[typeName]) {
        typeStats[typeName] = { count: 0, total: 0 };
      }
      typeStats[typeName].count++;
      typeStats[typeName].total += expense.amount;
    });

    const topTypes = Object.entries(typeStats)
      .sort((a, b) => b[1].total - a[1].total)
      .slice(0, 3)
      .map(([type, stats]) => `${type}: ¥${this.formatNumber(stats.total)}`)
      .join(' | ');

    return topTypes || '无支出数据';
  }

  // 显示月份明细
  showMonthDetail(monthKey) {
    const [year, month] = monthKey.split('-');
    const monthExpenses = expenses.filter(expense => {
      const date = new Date(expense.date);
      return date.getFullYear() === parseInt(year) &&
             date.getMonth() + 1 === parseInt(month);
    });

    const modal = document.getElementById('detail-modal');
    const modalTitle = document.getElementById('detail-modal-title');
    const modalBody = document.getElementById('detail-modal-body');

    if (!modal || !modalTitle || !modalBody) {
      console.error('Detail modal elements not found');
      this.showToast('系统错误，请刷新页面重试', 'error');
      return;
    }

    modalTitle.textContent = `${year}年${month}月支出明细`;

    const totalAmount = monthExpenses.reduce((sum, expense) => sum + expense.amount, 0);

    modalBody.innerHTML = `
      <div class="detail-content">
        <div class="detail-header">
          <div class="detail-icon" style="background-color: var(--primary-color)">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <div class="detail-info">
            <h3>${year}年${month}月</h3>
            <div class="detail-amount">¥ ${this.formatNumber(totalAmount)}</div>
          </div>
        </div>

        <div class="detail-fields">
          <div class="detail-field">
            <label>支出笔数</label>
            <div class="detail-value">${monthExpenses.length} 笔</div>
          </div>

          <div class="detail-field">
            <label>总金额</label>
            <div class="detail-value">¥ ${this.formatNumber(totalAmount)}</div>
          </div>

          <div class="detail-field">
            <label>平均金额</label>
            <div class="detail-value">¥ ${this.formatNumber(totalAmount / monthExpenses.length)}</div>
          </div>
        </div>

        <div class="month-detail-list">
          <h4>支出明细</h4>
          <div class="month-expenses">
            ${monthExpenses.map(expense => {
              const typeConfig = EXPENSE_TYPES[expense.type] || { name: expense.type, color: '#666', icon: 'fas fa-money-bill' };
              return `
                <div class="month-expense-item" onclick="financialSystem.closeModal(); financialSystem.showExpenseDetail('${expense.id}')">
                  <div class="expense-type">
                    <i class="${typeConfig.icon}" style="color: ${typeConfig.color}"></i>
                    <span>${typeConfig.name}</span>
                  </div>
                  <div class="expense-amount">¥ ${this.formatNumber(expense.amount)}</div>
                  <div class="expense-date">${this.formatDate(expense.date)}</div>
                  ${expense.note ? `<div class="expense-note">${expense.note}</div>` : ''}
                </div>
              `;
            }).join('')}
          </div>
        </div>
      </div>
    `;

    modal.classList.add('show');
  }

  editExpense(id) {
    const expense = expenses.find(e => e.id === id);
    if (!expense) return;

    currentEditingExpense = expense;

    const modal = document.getElementById('edit-modal');
    const modalTitle = document.getElementById('modal-title-text');
    const modalBody = document.getElementById('modal-body');

    if (!modal || !modalTitle || !modalBody) {
      console.error('Edit modal elements not found');
      this.showToast('系统错误，请刷新页面重试', 'error');
      return;
    }

    modalTitle.textContent = '编辑支出';
    modalBody.innerHTML = `
      <div class="form-group">
        <label>支出类型</label>
        <select id="edit-expense-type">
          <option value="icbc_credit" ${expense.type === 'icbc_credit' ? 'selected' : ''}>工行信用卡</option>
          <option value="cmb_credit" ${expense.type === 'cmb_credit' ? 'selected' : ''}>招行信用卡</option>
          <option value="alipay" ${expense.type === 'alipay' ? 'selected' : ''}>支付宝</option>
          <option value="wechat" ${expense.type === 'wechat' ? 'selected' : ''}>微信</option>
        </select>
      </div>
      <div class="form-group">
        <label>金额 (元)</label>
        <input type="number" id="edit-expense-amount" step="0.01" value="${expense.amount}">
      </div>
      <div class="form-group">
        <label>日期</label>
        <input type="date" id="edit-expense-date" value="${expense.date}">
      </div>
      <div class="form-group">
        <label>备注</label>
        <input type="text" id="edit-expense-note" value="${expense.note || ''}">
      </div>
    `;

    modal.classList.add('show');
  }

  deleteExpense(id) {
    deleteRecordId = id;
    const modal = document.getElementById('confirm-modal');
    const message = document.getElementById('confirm-message');
    message.textContent = '确定要删除这条支出记录吗？';
    modal.classList.add('show');
  }

  handleModalConfirm() {
    const modal = document.getElementById('edit-modal');
    if (!modal) {
      console.error('Edit modal element not found');
      this.showToast('系统错误，请刷新页面重试', 'error');
      return;
    }

    const channelId = modal.dataset.channelId;

    if (currentEditingExpense) {
      // 更新支出记录
      const typeEl = document.getElementById('edit-expense-type');
      const amountEl = document.getElementById('edit-expense-amount');
      const dateEl = document.getElementById('edit-expense-date');
      const noteEl = document.getElementById('edit-expense-note');

      if (!typeEl || !amountEl || !dateEl || !noteEl) {
        console.error('支出编辑表单元素未找到');
        this.showToast('系统错误，请刷新页面重试', 'error');
        return;
      }

      const type = typeEl.value;
      const amount = parseFloat(amountEl.value);
      const date = dateEl.value;
      const note = noteEl.value;

      if (!type || !amount || !date) {
        this.showToast('请填写完整信息', 'error');
        return;
      }

      const index = expenses.findIndex(e => e.id === currentEditingExpense.id);
      if (index !== -1) {
        expenses[index] = {
          ...currentEditingExpense,
          type,
          amount,
          date,
          note
        };

        this.saveExpenses();
        this.updateExpenseUI();
        this.closeModal();
        this.showToast('支出记录更新成功');
      }
    } else if (channelId) {
      // 更新资产记录
      const channel = ASSET_CHANNELS.find(c => c.id === channelId);
      if (!channel) return;

      if (channel.crypto) {
        // 更新加密货币资产
        const inputs = modal.querySelectorAll('.crypto-assets input[data-symbol]');
        const newAssets = {};

        if (inputs && inputs.length > 0) {
          inputs.forEach(input => {
            const symbol = input.dataset.symbol;
            const amount = parseFloat(input.value) || 0;
            if (amount > 0) {
              newAssets[symbol] = amount;
            }
          });
        }

        currentAssets[channelId] = newAssets;
        console.log(`更新 ${channel.name} 加密货币资产:`, newAssets);
      } else {
        // 更新传统资产
        const amountInput = document.getElementById('edit-asset-amount');
        if (amountInput) {
          const amount = parseFloat(amountInput.value) || 0;
          currentAssets[channelId] = amount;
          console.log(`更新 ${channel.name} 资产:`, amount);
        }
      }

      // 保存并更新UI
      this.saveCurrentAssets();
      this.renderAssetGrid();
      this.closeModal();
      this.showToast('资产更新成功');
    }
  }

  handleDeleteConfirm() {
    if (deleteRecordId) {
      const index = expenses.findIndex(e => e.id === deleteRecordId);
      if (index !== -1) {
        expenses.splice(index, 1);
        this.saveExpenses();
        this.updateExpenseUI();
        this.closeModal();
        this.showToast('支出记录删除成功');
      }
    }
  }

  // 显示支出详情
  showExpenseDetail(id) {
    const expense = expenses.find(e => e.id === id);
    if (!expense) return;

    const typeConfig = EXPENSE_TYPES[expense.type] || { name: expense.type, color: '#666', icon: 'fas fa-money-bill' };
    const modal = document.getElementById('detail-modal');
    const modalTitle = document.getElementById('detail-modal-title');
    const modalBody = document.getElementById('detail-modal-body');

    modalTitle.textContent = '支出详情';
    modalBody.innerHTML = `
      <div class="detail-content">
        <div class="detail-header">
          <div class="detail-icon" style="background-color: ${typeConfig.color}">
            <i class="${typeConfig.icon}"></i>
          </div>
          <div class="detail-info">
            <h3>${typeConfig.name}</h3>
            <div class="detail-amount">¥ ${this.formatNumber(expense.amount)}</div>
          </div>
        </div>

        <div class="detail-fields">
          <div class="detail-field">
            <label>支出类型</label>
            <div class="detail-value">${typeConfig.name}</div>
          </div>

          <div class="detail-field">
            <label>金额</label>
            <div class="detail-value">¥ ${this.formatNumber(expense.amount)}</div>
          </div>

          <div class="detail-field">
            <label>日期</label>
            <div class="detail-value">${this.formatDate(expense.date)}</div>
          </div>

          ${expense.note ? `
            <div class="detail-field">
              <label>备注</label>
              <div class="detail-value">${expense.note}</div>
            </div>
          ` : ''}

          <div class="detail-field">
            <label>创建时间</label>
            <div class="detail-value">${new Date(expense.timestamp).toLocaleString('zh-CN')}</div>
          </div>
        </div>

        <div class="detail-actions">
          <button class="btn-primary" onclick="financialSystem.closeModal(); financialSystem.editExpense('${expense.id}')">
            <i class="fas fa-edit"></i> 编辑
          </button>
          <button class="btn-danger" onclick="financialSystem.closeModal(); financialSystem.deleteExpense('${expense.id}')">
            <i class="fas fa-trash"></i> 删除
          </button>
        </div>
      </div>
    `;

    modal.classList.add('show');
  }

  // 支出趋势图表
  renderExpenseTrendChart() {
    const canvas = document.getElementById('expense-trend-chart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const period = parseInt(document.getElementById('chart-period')?.value || '12');
    const chartType = document.querySelector('.chart-type-btn.active')?.dataset.type || 'bar';

    // 销毁现有图表
    if (expenseTrendChart) {
      expenseTrendChart.destroy();
    }

    // 准备数据
    const monthlyData = this.getMonthlyExpenseData(period);

    const config = {
      type: chartType,
      data: {
        labels: monthlyData.labels,
        datasets: [{
          label: '月度支出',
          data: monthlyData.data,
          backgroundColor: chartType === 'bar' ? 'rgba(63, 81, 181, 0.8)' : 'rgba(63, 81, 181, 0.1)',
          borderColor: 'rgba(63, 81, 181, 1)',
          borderWidth: 2,
          fill: chartType === 'line'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return '¥' + value.toLocaleString();
              }
            }
          }
        }
      }
    };

    expenseTrendChart = new Chart(ctx, config);
  }

  getMonthlyExpenseData(months) {
    const now = new Date();
    const labels = [];
    const data = [];

    for (let i = months - 1; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;

      labels.push(`${year}-${month.toString().padStart(2, '0')}`);

      const monthTotal = expenses
        .filter(expense => {
          const expenseDate = new Date(expense.date);
          return expenseDate.getFullYear() === year &&
                 expenseDate.getMonth() + 1 === month;
        })
        .reduce((total, expense) => total + expense.amount, 0);

      data.push(monthTotal);
    }

    return { labels, data };
  }

  // 资产管理方法
  async updateCryptoPrices(forceUpdate = false) {
    const now = Date.now();
    if (!forceUpdate && now - lastPriceUpdate < PRICE_CACHE_DURATION && Object.keys(cryptoPrices).length > 0) {
      return;
    }

    console.log('更新加密货币价格...');

    try {
      // 获取加密货币价格
      const cryptoResponse = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin,pepe&vs_currencies=usd,cny');
      if (cryptoResponse.ok) {
        const cryptoData = await cryptoResponse.json();
        const newPrices = {
          BTC: {
            USD: cryptoData.bitcoin?.usd || 0,
            CNY: cryptoData.bitcoin?.cny || 0
          },
          PEPE: {
            USD: cryptoData.pepe?.usd || 0,
            CNY: cryptoData.pepe?.cny || 0
          }
        };

        // 检查价格是否有变化
        const priceChanged = !cryptoPrices.BTC ||
          cryptoPrices.BTC.USD !== newPrices.BTC.USD ||
          cryptoPrices.BTC.CNY !== newPrices.BTC.CNY;

        cryptoPrices = newPrices;
        lastPriceUpdate = now;

        console.log('价格更新成功:', {
          BTC_USD: cryptoPrices.BTC.USD,
          BTC_CNY: cryptoPrices.BTC.CNY,
          priceChanged
        });

        // 如果价格有变化且当前在资产标签页，则更新显示
        if (priceChanged && this.currentTab === 'assets') {
          this.renderAssetGrid();
          this.updateTotalAssets();
          this.animatePriceUpdate();
        }
      }
    } catch (error) {
      console.error('获取价格失败:', error);
      // 使用默认价格（仅在没有现有价格时）
      if (!cryptoPrices.BTC) {
        cryptoPrices = {
          BTC: { USD: 43000, CNY: 310000 },
          PEPE: { USD: 0.00000085, CNY: 0.000006 }
        };
        console.log('使用默认价格');
      }
    }
  }

  // 价格更新动画
  animatePriceUpdate() {
    const cryptoCards = document.querySelectorAll('.crypto-card .crypto-price-info');
    cryptoCards.forEach(card => {
      card.classList.add('price-updated');
      setTimeout(() => {
        card.classList.remove('price-updated');
      }, 2000);
    });
  }

  // 手动刷新加密货币价格
  async refreshCryptoPrices() {
    const refreshBtn = document.getElementById('refresh-price-btn');
    if (!refreshBtn) return;

    // 显示加载状态
    refreshBtn.classList.add('loading');
    refreshBtn.disabled = true;
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<span class="btn-icon">₿</span>刷新中...';

    try {
      console.log('手动刷新加密货币价格...');
      await this.updateCryptoPrices(true); // 强制更新

      // 更新UI
      this.renderAssetGrid();
      this.updateTotalAssets();

      this.showToast('价格更新成功', 'success');
    } catch (error) {
      console.error('手动刷新价格失败:', error);
      this.showToast('价格更新失败，请稍后重试', 'error');
    } finally {
      // 恢复按钮状态
      setTimeout(() => {
        refreshBtn.classList.remove('loading');
        refreshBtn.disabled = false;
        refreshBtn.innerHTML = originalText;
      }, 1000);
    }
  }

  async loadExchangeRates() {
    try {
      console.log('开始获取实时汇率...');

      // 显示加载状态
      const usdRateEl = document.querySelector('.rate-value[data-pair="usd"]');
      const hkdRateEl = document.querySelector('.rate-value[data-pair="hkd"]');
      const updateTimeEl = document.getElementById('rate-update-time');

      if (usdRateEl) usdRateEl.textContent = '加载中...';
      if (hkdRateEl) hkdRateEl.textContent = '加载中...';
      if (updateTimeEl) updateTimeEl.textContent = '更新中...';

      // 使用免费的汇率API - exchangerate-api.com
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // 计算汇率
      const usdToCny = data.rates.CNY || 7.2; // USD to CNY
      const hkdToCny = data.rates.CNY / data.rates.HKD || 0.92; // HKD to CNY

      exchangeRates = {
        USD_CNY: usdToCny,
        HKD_CNY: hkdToCny
      };

      console.log('汇率获取成功:', exchangeRates);

      // 更新汇率显示
      if (usdRateEl) usdRateEl.textContent = exchangeRates.USD_CNY.toFixed(4);
      if (hkdRateEl) hkdRateEl.textContent = exchangeRates.HKD_CNY.toFixed(4);

      // 更新时间显示
      const now = new Date();
      const timeStr = now.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      });
      if (updateTimeEl) updateTimeEl.textContent = `${timeStr} 更新`;

      // 汇率更新后重新计算资产总值
      this.updateAssetUI();

    } catch (error) {
      console.error('获取汇率失败:', error);

      // 使用备用汇率
      exchangeRates = {
        USD_CNY: 7.2,
        HKD_CNY: 0.92
      };

      // 更新汇率显示为备用值
      const usdRateEl = document.querySelector('.rate-value[data-pair="usd"]');
      const hkdRateEl = document.querySelector('.rate-value[data-pair="hkd"]');
      const updateTimeEl = document.getElementById('rate-update-time');

      if (usdRateEl) usdRateEl.textContent = exchangeRates.USD_CNY.toFixed(4) + ' (备用)';
      if (hkdRateEl) hkdRateEl.textContent = exchangeRates.HKD_CNY.toFixed(4) + ' (备用)';
      if (updateTimeEl) updateTimeEl.textContent = '使用备用汇率';

      this.showToast('汇率获取失败，使用备用汇率', 'warning');
    }
  }

  // 格式化大数字显示
  formatLargeNumber(num) {
    const number = parseFloat(num);

    // 如果数字太大（超过10位），使用科学计数法
    if (number >= 1e10) {
      return number.toExponential(2);
    }

    // 如果数字很大但不需要科学计数法，添加千分位分隔符
    if (number >= 1000) {
      return number.toLocaleString('en-US', {
        maximumFractionDigits: 8,
        minimumFractionDigits: 0
      });
    }

    // 小数字直接显示
    return number.toString();
  }

  // 渲染资产网格（不依赖价格数据的快速版本）
  renderAssetGridWithoutPrices() {
    const gridEl = document.getElementById('asset-grid');
    if (!gridEl) return;

    console.log('渲染资产网格（无价格版本）...');

    gridEl.innerHTML = ASSET_CHANNELS.map(channel => {
      const asset = currentAssets[channel.id];
      let displayAmount = '';
      let cnyEquivalent = 0;

      if (channel.crypto) {
        // 加密货币资产
        const assets = asset || {};
        const assetEntries = Object.entries(assets);

        // 如果没有资产但有默认资产配置，使用默认值显示
        if (assetEntries.length === 0 && channel.defaultAssets) {
          const defaultEntries = Object.entries(channel.defaultAssets);
          displayAmount = defaultEntries.map(([symbol, amount]) => {
            const formattedAmount = this.formatLargeNumber(amount);
            return `${formattedAmount} ${symbol}`;
          }).join('<br>');
        } else if (assetEntries.length === 0) {
          displayAmount = '暂无资产';
        } else {
          displayAmount = assetEntries.map(([symbol, amount]) => {
            const formattedAmount = this.formatLargeNumber(amount);
            return `${formattedAmount} ${symbol}`;
          }).join('<br>');
        }
      } else {
        // 传统资产
        const amount = asset || 0;
        if (channel.currency === 'USD') {
          displayAmount = `$${this.formatNumber(amount)}`;
          cnyEquivalent = amount * exchangeRates.USD_CNY;
        } else if (channel.currency === 'HKD') {
          displayAmount = `HK$${this.formatNumber(amount)}`;
          cnyEquivalent = amount * exchangeRates.HKD_CNY;
        } else {
          displayAmount = `¥${this.formatNumber(amount)}`;
          cnyEquivalent = amount;
        }
      }

      // 为所有资产添加额外信息区域
      let priceInfo = '';
      if (channel.crypto) {
        priceInfo = `
          <div class="crypto-price-info">
            <div class="price-header">
              <div class="price-label">₿ BTC 价格</div>
            </div>
            <div class="price-loading">
              <i class="fas fa-spinner fa-spin"></i> 获取中...
            </div>
          </div>
        `;
      } else if (channel.currency === 'USD') {
        priceInfo = `
          <div class="asset-extra-info">
            <div class="exchange-rate-info">
              <div class="rate-label">汇率 USD/CNY</div>
              <div class="rate-value">${exchangeRates.USD_CNY || '7.20'}</div>
            </div>
          </div>
        `;
      } else if (channel.currency === 'HKD') {
        priceInfo = `
          <div class="asset-extra-info">
            <div class="exchange-rate-info">
              <div class="rate-label">汇率 HKD/CNY</div>
              <div class="rate-value">${exchangeRates.HKD_CNY || '0.92'}</div>
            </div>
          </div>
        `;
      } else {
        priceInfo = `
          <div class="asset-extra-info">
            <div class="asset-status-info">
              <div class="status-label">本币资产</div>
              <div class="status-value">无汇率风险</div>
            </div>
          </div>
        `;
      }

      // 确定卡片类型和样式（快速版本）
      let cardClass = 'asset-card';
      let cardStyle = '';

      if (channel.crypto) {
        cardClass += ' crypto-card';
        cardStyle = `
          background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 50%, #ffeaa7 100%);
          border: 2px solid #f39c12;
          box-shadow: 0 4px 20px rgba(243, 156, 18, 0.15);
        `;
      } else if (channel.currency === 'USD') {
        cardClass += ' usd-card';
        cardStyle = `
          background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 50%, #c3e6cb 100%);
          border: 2px solid #28a745;
          box-shadow: 0 4px 20px rgba(40, 167, 69, 0.15);
        `;
      } else if (channel.currency === 'HKD') {
        cardClass += ' hkd-card';
        cardStyle = `
          background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 50%, #ce93d8 100%);
          border: 2px solid #9c27b0;
          box-shadow: 0 4px 20px rgba(156, 39, 176, 0.15);
        `;
      } else {
        cardClass += ' cny-card';
        cardStyle = `
          background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
          border: 2px solid #2196f3;
          box-shadow: 0 4px 20px rgba(33, 150, 243, 0.15);
        `;
      }

      // 获取货币符号和类型标识（快速版本）
      const currencySymbol = channel.crypto ? '₿' :
                            channel.currency === 'USD' ? '$' :
                            channel.currency === 'HKD' ? 'HK$' : '¥';

      const typeLabel = channel.crypto ? '加密货币' :
                       channel.currency === 'USD' ? '美元资产' :
                       channel.currency === 'HKD' ? '港币资产' : '人民币资产';

      return `
        <div class="${cardClass}" data-channel="${channel.id}" style="${cardStyle}" onclick="financialSystem.showAssetDetail('${channel.id}')">
          <!-- 卡片顶部 -->
          <div class="card-top">
            <div class="asset-header-modern">
              <div class="asset-icon-modern" style="background: ${channel.color};">
                ${channel.icon}
              </div>
              <div class="asset-title-section">
                <div class="asset-name-modern">${channel.name}</div>
                <div class="asset-type-modern">${typeLabel}</div>
              </div>
            </div>
            <div class="currency-symbol-modern">${currencySymbol}</div>
          </div>

          <!-- 资产金额 -->
          <div class="asset-amount-section">
            <div class="asset-amount-modern">${displayAmount}</div>
            <div class="asset-equivalent-modern">≈ ¥${this.formatNumber(cnyEquivalent)}</div>
          </div>

          <!-- 额外信息区域 -->
          ${priceInfo}

          <!-- 编辑按钮 -->
          <button class="asset-edit-btn-modern" onclick="event.stopPropagation(); financialSystem.editAsset('${channel.id}')" title="编辑">
            <i class="fas fa-edit"></i>
          </button>
        </div>
      `;
    }).join('');
  }

  // 完整渲染资产网格（包含价格数据）
  renderAssetGrid() {
    const gridEl = document.getElementById('asset-grid');
    if (!gridEl) return;

    console.log('渲染完整资产网格...');

    gridEl.innerHTML = ASSET_CHANNELS.map(channel => {
      const asset = currentAssets[channel.id];
      let displayAmount = '';
      let cnyEquivalent = 0;
      let priceInfo = '';

      if (channel.crypto) {
        // 加密货币资产
        const assets = asset || {};
        const assetEntries = Object.entries(assets);

        // 如果没有资产但有默认资产配置，使用默认值显示
        if (assetEntries.length === 0 && channel.defaultAssets) {
          const defaultEntries = Object.entries(channel.defaultAssets);
          displayAmount = defaultEntries.map(([symbol, amount]) => {
            const price = cryptoPrices[symbol];
            const formattedAmount = this.formatLargeNumber(amount);
            if (price) {
              const usdValue = amount * price.USD;
              cnyEquivalent += usdValue * exchangeRates.USD_CNY;
              return `${formattedAmount} ${symbol}`;
            }
            return `${formattedAmount} ${symbol}`;
          }).join('<br>');
        } else if (assetEntries.length === 0) {
          displayAmount = '暂无资产';
        } else {
          displayAmount = assetEntries.map(([symbol, amount]) => {
            const price = cryptoPrices[symbol];
            const formattedAmount = this.formatLargeNumber(amount);
            if (price) {
              const usdValue = amount * price.USD;
              cnyEquivalent += usdValue * exchangeRates.USD_CNY;
              return `${formattedAmount} ${symbol}`;
            }
            return `${formattedAmount} ${symbol}`;
          }).join('<br>');
        }

        // 为加密货币资产添加比特币价格信息
        const btcPrice = cryptoPrices.BTC;
        if (btcPrice && btcPrice.USD > 0) {
          const updateTime = lastPriceUpdate ? new Date(lastPriceUpdate) : new Date();
          const timeStr = updateTime.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
          });

          priceInfo = `
            <div class="crypto-price-info">
              <div class="price-header">
                <div class="price-label">₿ BTC 实时价格</div>
                <div class="price-time">${timeStr}</div>
              </div>
              <div class="price-values">
                <div class="price-usd">$${this.formatNumber(btcPrice.USD, 0)}</div>
                <div class="price-cny">¥${this.formatNumber(btcPrice.CNY, 0)}</div>
              </div>
            </div>
          `;
        } else {
          priceInfo = `
            <div class="crypto-price-info">
              <div class="price-header">
                <div class="price-label">₿ BTC 价格</div>
              </div>
              <div class="price-loading">
                <i class="fas fa-spinner fa-spin"></i> 获取中...
              </div>
            </div>
          `;
        }
      } else {
        // 传统资产
        const amount = asset || 0;
        if (channel.currency === 'USD') {
          displayAmount = `$${this.formatNumber(amount)}`;
          cnyEquivalent = amount * exchangeRates.USD_CNY;
          // 美元资产显示汇率信息
          priceInfo = `
            <div class="asset-extra-info">
              <div class="exchange-rate-info">
                <div class="rate-label">汇率 USD/CNY</div>
                <div class="rate-value">${exchangeRates.USD_CNY || '7.20'}</div>
              </div>
            </div>
          `;
        } else if (channel.currency === 'HKD') {
          displayAmount = `HK$${this.formatNumber(amount)}`;
          cnyEquivalent = amount * exchangeRates.HKD_CNY;
          // 港币资产显示汇率信息
          priceInfo = `
            <div class="asset-extra-info">
              <div class="exchange-rate-info">
                <div class="rate-label">汇率 HKD/CNY</div>
                <div class="rate-value">${exchangeRates.HKD_CNY || '0.92'}</div>
              </div>
            </div>
          `;
        } else {
          displayAmount = `¥${this.formatNumber(amount)}`;
          cnyEquivalent = amount;
          // 人民币资产显示状态信息
          priceInfo = `
            <div class="asset-extra-info">
              <div class="asset-status-info">
                <div class="status-label">本币资产</div>
                <div class="status-value">无汇率风险</div>
              </div>
            </div>
          `;
        }
      }

      // 确定卡片类型和样式 - 现代化设计
      let cardClass = 'asset-card';
      let cardStyle = '';

      if (channel.crypto) {
        cardClass += ' crypto-card';
        // 加密货币卡片 - 现代金色设计
        cardStyle = `
          background: linear-gradient(145deg, #ffffff 0%, #fefcf3 100%);
          border: 1px solid rgba(255, 193, 7, 0.2);
          box-shadow: 0 8px 32px rgba(255, 193, 7, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);
        `;
      } else if (channel.currency === 'USD') {
        cardClass += ' usd-card';
        // 美元资产 - 现代绿色设计
        cardStyle = `
          background: linear-gradient(145deg, #ffffff 0%, #f8fffe 100%);
          border: 1px solid rgba(34, 197, 94, 0.2);
          box-shadow: 0 8px 32px rgba(34, 197, 94, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);
        `;
      } else if (channel.currency === 'HKD') {
        cardClass += ' hkd-card';
        // 港币资产 - 现代紫色设计
        cardStyle = `
          background: linear-gradient(145deg, #ffffff 0%, #fefbff 100%);
          border: 1px solid rgba(147, 51, 234, 0.2);
          box-shadow: 0 8px 32px rgba(147, 51, 234, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);
        `;
      } else {
        cardClass += ' cny-card';
        // 人民币资产 - 现代蓝色设计
        cardStyle = `
          background: linear-gradient(145deg, #ffffff 0%, #f8faff 100%);
          border: 1px solid rgba(59, 130, 246, 0.2);
          box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1), 0 2px 8px rgba(0, 0, 0, 0.05);
        `;
      }

      // 获取货币符号和类型标识
      const currencySymbol = channel.crypto ? '₿' :
                            channel.currency === 'USD' ? '$' :
                            channel.currency === 'HKD' ? 'HK$' : '¥';

      const typeLabel = channel.crypto ? '加密货币' :
                       channel.currency === 'USD' ? '美元资产' :
                       channel.currency === 'HKD' ? '港币资产' : '人民币资产';

      return `
        <div class="${cardClass}" data-channel="${channel.id}" style="${cardStyle}" onclick="financialSystem.showAssetDetail('${channel.id}')">
          <!-- 卡片顶部 -->
          <div class="card-top">
            <div class="asset-header-modern">
              <div class="asset-icon-modern" style="background: ${channel.color};">
                ${channel.icon}
              </div>
              <div class="asset-title-section">
                <div class="asset-name-modern">${channel.name}</div>
                <div class="asset-type-modern">${typeLabel}</div>
              </div>
            </div>
            <div class="currency-symbol-modern">${currencySymbol}</div>
          </div>

          <!-- 资产金额 -->
          <div class="asset-amount-section">
            <div class="asset-amount-modern">${displayAmount}</div>
            <div class="asset-equivalent-modern">≈ ¥${this.formatNumber(cnyEquivalent)}</div>
          </div>

          <!-- 额外信息区域 -->
          ${priceInfo}

          <!-- 编辑按钮 -->
          <button class="asset-edit-btn-modern" onclick="event.stopPropagation(); financialSystem.editAsset('${channel.id}')" title="编辑">
            <i class="fas fa-edit"></i>
          </button>
        </div>
      `;
    }).join('');
  }

  // 更新总资产（不依赖价格数据的快速版本）
  updateTotalAssetsWithoutPrices() {
    let total = 0;

    ASSET_CHANNELS.forEach(channel => {
      const asset = currentAssets[channel.id];

      if (channel.crypto) {
        // 加密货币暂时不计入总资产，等价格加载完成后再计算
        return;
      } else {
        const amount = asset || 0;
        if (channel.currency === 'USD') {
          total += amount * exchangeRates.USD_CNY;
        } else if (channel.currency === 'HKD') {
          total += amount * exchangeRates.HKD_CNY;
        } else {
          total += amount;
        }
      }
    });

    const totalEl = document.getElementById('total-amount');
    if (totalEl) {
      totalEl.textContent = `¥ ${this.formatNumber(total)} (计算中...)`;
    }
  }

  // 更新总资产（完整版本）
  updateTotalAssets() {
    let total = 0;

    ASSET_CHANNELS.forEach(channel => {
      const asset = currentAssets[channel.id];

      if (channel.crypto) {
        const assets = asset || {};
        Object.entries(assets).forEach(([symbol, amount]) => {
          const price = cryptoPrices[symbol];
          if (price) {
            const usdValue = amount * price.USD;
            total += usdValue * exchangeRates.USD_CNY;
          }
        });
      } else {
        const amount = asset || 0;
        if (channel.currency === 'USD') {
          total += amount * exchangeRates.USD_CNY;
        } else if (channel.currency === 'HKD') {
          total += amount * exchangeRates.HKD_CNY;
        } else {
          total += amount;
        }
      }
    });

    const totalEl = document.getElementById('total-amount');
    if (totalEl) {
      totalEl.textContent = `¥ ${this.formatNumber(total)}`;
    }
  }

  editAsset(channelId) {
    const channel = ASSET_CHANNELS.find(c => c.id === channelId);
    if (!channel) return;

    const asset = currentAssets[channelId];
    const modal = document.getElementById('edit-modal');
    const modalTitle = document.getElementById('modal-title-text');
    const modalBody = document.getElementById('modal-body');
    const modalIcon = document.getElementById('modal-icon');

    if (!modal || !modalTitle || !modalBody || !modalIcon) {
      console.error('Edit asset modal elements not found');
      this.showToast('系统错误，请刷新页面重试', 'error');
      return;
    }

    modalTitle.textContent = `编辑 ${channel.name}`;
    modalIcon.style.backgroundColor = channel.color;
    modalIcon.textContent = channel.icon;

    if (channel.crypto) {
      // 加密货币编辑 - 使用默认资产配置确保所有币种都显示
      const assets = asset || {};
      const allSymbols = new Set([
        ...Object.keys(assets),
        ...Object.keys(channel.defaultAssets || {})
      ]);

      modalBody.innerHTML = `
        <div class="crypto-assets">
          ${Array.from(allSymbols).map(symbol => {
            const amount = assets[symbol] !== undefined ? assets[symbol] :
                          (channel.defaultAssets && channel.defaultAssets[symbol] !== undefined ? channel.defaultAssets[symbol] : 0);
            return `
              <div class="form-group">
                <label>${symbol}</label>
                <input type="number" step="0.00000001" value="${amount}" data-symbol="${symbol}">
              </div>
            `;
          }).join('')}
        </div>
      `;
    } else {
      // 传统资产编辑
      const amount = asset || 0;
      const currencySymbol = channel.currency === 'USD' ? '$' :
                           channel.currency === 'HKD' ? 'HK$' : '¥';

      modalBody.innerHTML = `
        <div class="form-group">
          <label>金额 (${currencySymbol})</label>
          <input type="number" step="0.01" value="${amount}" id="edit-asset-amount">
        </div>
      `;
    }

    modal.dataset.channelId = channelId;
    modal.classList.add('show');
  }

  // 显示资产详情
  showAssetDetail(channelId) {
    const channel = ASSET_CHANNELS.find(c => c.id === channelId);
    if (!channel) return;

    const asset = currentAssets[channelId];
    const modal = document.getElementById('detail-modal');
    const modalTitle = document.getElementById('detail-modal-title');
    const modalBody = document.getElementById('detail-modal-body');

    modalTitle.textContent = '资产详情';

    let detailContent = '';
    let totalCNY = 0;

    if (channel.crypto) {
      // 加密货币资产详情
      const assets = asset || {};
      const assetEntries = Object.entries(assets);

      detailContent = `
        <div class="detail-content">
          <div class="detail-header">
            <div class="detail-icon" style="background-color: ${channel.color}">
              ${channel.icon}
            </div>
            <div class="detail-info">
              <h3>${channel.name}</h3>
              <div class="detail-type">加密货币资产</div>
            </div>
          </div>

          <div class="crypto-assets-detail">
            ${assetEntries.length === 0 ? '<p class="no-assets">暂无资产</p>' :
              assetEntries.map(([symbol, amount]) => {
                const price = cryptoPrices[symbol];
                let usdValue = 0;
                let cnyValue = 0;

                if (price) {
                  usdValue = amount * price.USD;
                  cnyValue = usdValue * exchangeRates.USD_CNY;
                  totalCNY += cnyValue;
                }

                // 根据币种决定小数位数
                const decimals = symbol === 'PEPE' ? 8 : 2;

                return `
                  <div class="crypto-asset-item">
                    <div class="crypto-symbol">${symbol}</div>
                    <div class="crypto-amount">${amount}</div>
                    ${price ? `
                      <div class="crypto-value">
                        <div>≈ $${this.formatNumber(usdValue, decimals)}</div>
                        <div>≈ ¥${this.formatNumber(cnyValue, decimals)}</div>
                      </div>
                    ` : '<div class="crypto-value">价格获取中...</div>'}
                  </div>
                `;
              }).join('')
            }
          </div>

          <div class="detail-summary">
            <div class="summary-item">
              <label>总价值 (CNY)</label>
              <div class="summary-value">¥ ${this.formatNumber(totalCNY)}</div>
            </div>
          </div>

          <div class="detail-actions">
            <button class="btn-primary" onclick="financialSystem.closeModal(); financialSystem.editAsset('${channelId}')">
              <i class="fas fa-edit"></i> 编辑
            </button>
          </div>
        </div>
      `;
    } else {
      // 传统资产详情
      const amount = asset || 0;
      let displayAmount = '';
      let cnyEquivalent = 0;

      if (channel.currency === 'USD') {
        displayAmount = `$${this.formatNumber(amount)}`;
        cnyEquivalent = amount * exchangeRates.USD_CNY;
      } else if (channel.currency === 'HKD') {
        displayAmount = `HK$${this.formatNumber(amount)}`;
        cnyEquivalent = amount * exchangeRates.HKD_CNY;
      } else {
        displayAmount = `¥${this.formatNumber(amount)}`;
        cnyEquivalent = amount;
      }

      detailContent = `
        <div class="detail-content">
          <div class="detail-header">
            <div class="detail-icon" style="background-color: ${channel.color}">
              ${channel.icon}
            </div>
            <div class="detail-info">
              <h3>${channel.name}</h3>
              <div class="detail-amount">${displayAmount}</div>
            </div>
          </div>

          <div class="detail-fields">
            <div class="detail-field">
              <label>资产类型</label>
              <div class="detail-value">${channel.type === 'CNY' ? '人民币资产' : channel.type === 'USD' ? '美元资产' : '港币资产'}</div>
            </div>

            <div class="detail-field">
              <label>当前金额</label>
              <div class="detail-value">${displayAmount}</div>
            </div>

            ${channel.currency !== 'CNY' ? `
              <div class="detail-field">
                <label>人民币等值</label>
                <div class="detail-value">¥ ${this.formatNumber(cnyEquivalent)}</div>
              </div>

              <div class="detail-field">
                <label>汇率</label>
                <div class="detail-value">${channel.currency}/CNY: ${channel.currency === 'USD' ? exchangeRates.USD_CNY : exchangeRates.HKD_CNY}</div>
              </div>
            ` : ''}
          </div>

          <div class="detail-actions">
            <button class="btn-primary" onclick="financialSystem.closeModal(); financialSystem.editAsset('${channelId}')">
              <i class="fas fa-edit"></i> 编辑
            </button>
          </div>
        </div>
      `;
    }

    modalBody.innerHTML = detailContent;
    modal.classList.add('show');
  }

  // 显示历史记录详情
  showHistoryDetail(recordId) {
    const record = assetHistory.find(r => r.id === recordId);
    if (!record) return;

    const modal = document.getElementById('detail-modal');
    const modalTitle = document.getElementById('detail-modal-title');
    const modalBody = document.getElementById('detail-modal-body');

    modalTitle.textContent = '历史记录详情';

    const date = new Date(record.timestamp);
    const dateStr = date.toLocaleDateString('zh-CN');
    const timeStr = date.toLocaleTimeString('zh-CN');

    let assetsDetail = '';

    ASSET_CHANNELS.forEach(channel => {
      const asset = record.assets[channel.id];
      if (!asset) return;

      if (channel.crypto) {
        const cryptoAssets = Object.entries(asset);
        if (cryptoAssets.length > 0) {
          assetsDetail += `
            <div class="history-asset-group">
              <div class="history-asset-header">
                <div class="asset-icon" style="background-color: ${channel.color}">
                  ${channel.icon}
                </div>
                <span>${channel.name}</span>
              </div>
              <div class="history-crypto-assets">
                ${cryptoAssets.map(([symbol, amount]) => `
                  <div class="history-crypto-item">
                    <span class="crypto-symbol">${symbol}</span>
                    <span class="crypto-amount">${amount}</span>
                  </div>
                `).join('')}
              </div>
            </div>
          `;
        }
      } else {
        if (asset > 0) {
          const currencySymbol = channel.currency === 'USD' ? '$' :
                               channel.currency === 'HKD' ? 'HK$' : '¥';
          assetsDetail += `
            <div class="history-asset-group">
              <div class="history-asset-header">
                <div class="asset-icon" style="background-color: ${channel.color}">
                  ${channel.icon}
                </div>
                <span>${channel.name}</span>
              </div>
              <div class="history-asset-amount">
                ${currencySymbol}${this.formatNumber(asset)}
              </div>
            </div>
          `;
        }
      }
    });

    modalBody.innerHTML = `
      <div class="detail-content">
        <div class="detail-header">
          <div class="detail-icon" style="background-color: var(--primary-color)">
            <i class="fas fa-history"></i>
          </div>
          <div class="detail-info">
            <h3>资产快照</h3>
            <div class="detail-amount">¥ ${this.formatNumber(record.totalCNY)}</div>
          </div>
        </div>

        <div class="detail-fields">
          <div class="detail-field">
            <label>保存时间</label>
            <div class="detail-value">${dateStr} ${timeStr}</div>
          </div>

          <div class="detail-field">
            <label>总资产价值</label>
            <div class="detail-value">¥ ${this.formatNumber(record.totalCNY)}</div>
          </div>
        </div>

        ${this.renderHistoryRatesAndPrices(record)}

        <div class="history-assets-detail">
          <h4>资产明细</h4>
          ${assetsDetail || '<p class="no-assets">无资产数据</p>'}
        </div>
      </div>
    `;

    modal.classList.add('show');
  }

  // 渲染历史记录中的汇率和价格信息
  renderHistoryRatesAndPrices(record) {
    let ratesAndPricesHtml = '';

    // 检查是否有汇率和价格数据（兼容旧记录）
    if (record.exchangeRates || record.cryptoPrices) {
      ratesAndPricesHtml += '<div class="history-rates-prices">';
      ratesAndPricesHtml += '<h4>当时市场信息</h4>';

      // 汇率信息
      if (record.exchangeRates) {
        ratesAndPricesHtml += `
          <div class="rates-section">
            <div class="section-title">💱 汇率信息</div>
            <div class="rates-grid">
              <div class="rate-item">
                <span class="rate-label">USD/CNY</span>
                <span class="rate-value">${record.exchangeRates.USD_CNY || 'N/A'}</span>
              </div>
              <div class="rate-item">
                <span class="rate-label">HKD/CNY</span>
                <span class="rate-value">${record.exchangeRates.HKD_CNY || 'N/A'}</span>
              </div>
            </div>
          </div>
        `;
      }

      // 加密货币价格信息
      if (record.cryptoPrices) {
        ratesAndPricesHtml += `
          <div class="crypto-section">
            <div class="section-title">₿ 加密货币价格</div>
            <div class="crypto-prices-grid">
        `;

        if (record.cryptoPrices.BTC) {
          ratesAndPricesHtml += `
            <div class="crypto-price-item">
              <div class="crypto-symbol">BTC</div>
              <div class="crypto-prices">
                <div class="price-usd">$${this.formatNumber(record.cryptoPrices.BTC.USD, 0)}</div>
                <div class="price-cny">¥${this.formatNumber(record.cryptoPrices.BTC.CNY, 0)}</div>
              </div>
            </div>
          `;
        }

        if (record.cryptoPrices.PEPE) {
          const pepeUSD = record.cryptoPrices.PEPE.USD;
          const pepeCNY = record.cryptoPrices.PEPE.CNY;
          ratesAndPricesHtml += `
            <div class="crypto-price-item">
              <div class="crypto-symbol">PEPE</div>
              <div class="crypto-prices">
                <div class="price-usd">$${this.formatNumber(pepeUSD, 8)}</div>
                <div class="price-cny">¥${this.formatNumber(pepeCNY, 8)}</div>
              </div>
            </div>
          `;
        }

        ratesAndPricesHtml += '</div>';

        // 价格更新时间
        if (record.priceUpdateTime) {
          const priceUpdateDate = new Date(record.priceUpdateTime);
          const priceTimeStr = priceUpdateDate.toLocaleString('zh-CN');
          ratesAndPricesHtml += `
            <div class="price-update-time">
              <small>价格更新时间: ${priceTimeStr}</small>
            </div>
          `;
        }

        ratesAndPricesHtml += '</div>';
      }

      ratesAndPricesHtml += '</div>';
    }

    return ratesAndPricesHtml;
  }

  async saveAssets(forceCreate = false) {
    console.log('开始保存资产数据...', forceCreate ? '(强制创建)' : '');
    console.log('保存前 assetHistory 状态:', {
      type: typeof assetHistory,
      isArray: Array.isArray(assetHistory),
      length: assetHistory?.length
    });

    // 确保 assetHistory 是数组
    if (!Array.isArray(assetHistory)) {
      console.warn('assetHistory 不是数组，重置为空数组');
      assetHistory = [];
    }

    const timestamp = new Date().toISOString();
    const newRecord = {
      id: this.generateId(),
      timestamp,
      assets: JSON.parse(JSON.stringify(currentAssets)),
      totalCNY: this.calculateTotalCNY(),
      // 保存当时的汇率和价格信息
      exchangeRates: JSON.parse(JSON.stringify(exchangeRates)),
      cryptoPrices: JSON.parse(JSON.stringify(cryptoPrices)),
      priceUpdateTime: lastPriceUpdate
    };

    // 检查是否与最近的记录重复（避免重复保存）
    const isDuplicate = !forceCreate && assetHistory.length > 0 && this.isRecordDuplicate(newRecord, assetHistory[0]);

    if (isDuplicate) {
      console.log('检测到重复记录，跳过保存');
      this.showToast('资产数据无变化，无需保存');
      return;
    }

    console.log('创建资产记录:', newRecord);
    assetHistory.unshift(newRecord);
    console.log('资产记录添加成功，当前历史记录数量:', assetHistory.length);

    // 保持最近100条记录
    if (assetHistory.length > 100) {
      assetHistory = assetHistory.slice(0, 100);
    }

    // 同时保存当前资产和历史记录到服务器
    try {
      await Promise.all([
        this.saveCurrentAssets(),
        this.saveAssetHistoryToServer()
      ]);

      this.renderAssetHistory();
      this.showToast('资产数据保存成功');
    } catch (error) {
      console.error('保存资产数据失败:', error);
      this.showToast('保存失败，请重试', 'error');
    }
  }

  calculateTotalCNY() {
    let total = 0;

    ASSET_CHANNELS.forEach(channel => {
      const asset = currentAssets[channel.id];

      if (channel.crypto) {
        const assets = asset || {};
        Object.entries(assets).forEach(([symbol, amount]) => {
          const price = cryptoPrices[symbol];
          if (price) {
            const usdValue = amount * price.USD;
            total += usdValue * exchangeRates.USD_CNY;
          }
        });
      } else {
        const amount = asset || 0;
        if (channel.currency === 'USD') {
          total += amount * exchangeRates.USD_CNY;
        } else if (channel.currency === 'HKD') {
          total += amount * exchangeRates.HKD_CNY;
        } else {
          total += amount;
        }
      }
    });

    return total;
  }

  // 保存当前资产到服务器
  async saveCurrentAssets() {
    try {
      const response = await fetch('/api/financial/current-assets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(currentAssets)
      });

      if (!response.ok) {
        throw new Error('保存当前资产失败');
      }
      console.log('当前资产保存成功');
    } catch (error) {
      console.error('保存当前资产失败:', error);
      localStorage.setItem('currentAssets', JSON.stringify(currentAssets));
      throw error;
    }
  }

  // 保存资产历史到服务器
  async saveAssetHistoryToServer() {
    try {
      const response = await fetch('/api/financial/asset-history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(assetHistory)
      });

      if (!response.ok) {
        throw new Error('保存资产历史失败');
      }
      console.log('资产历史保存成功');
    } catch (error) {
      console.error('保存资产历史失败:', error);
      localStorage.setItem('assetHistory', JSON.stringify(assetHistory));
      throw error;
    }
  }

  // 兼容旧的方法名
  async saveAssetsData() {
    return this.saveCurrentAssets();
  }

  async saveAssetHistory() {
    return this.saveAssetHistoryToServer();
  }

  clearAssets() {
    if (confirm('确定要清空所有资产数据吗？此操作不可恢复。')) {
      ASSET_CHANNELS.forEach(channel => {
        if (channel.crypto) {
          currentAssets[channel.id] = {};
        } else {
          currentAssets[channel.id] = 0;
        }
      });

      this.saveAssetsData();
      this.updateAssetUI();
      this.showToast('资产数据已清空');
    }
  }

  clearAssetHistory() {
    if (confirm('确定要清空所有历史记录吗？此操作不可恢复。')) {
      assetHistory = [];

      // 清除所有缓存
      this.clearHistoryCache();

      this.saveAssetHistory();
      this.renderAssetHistory();
      this.showToast('历史记录已清空');
    }
  }

  // 清除历史记录相关缓存
  clearHistoryCache() {
    if (this.historyCache) {
      this.historyCache.clear();
    }
    if (this.summaryCache) {
      this.summaryCache.clear();
    }
    // 重置分页参数
    this.historyPage = 1;
  }

  renderAssetHistory() {
    console.time('renderAssetHistory');
    const listEl = document.getElementById('history-list');
    const countEl = document.getElementById('history-count');

    if (!listEl || !countEl) return;

    // 确保 assetHistory 是数组
    if (!Array.isArray(assetHistory)) {
      assetHistory = [];
    }

    countEl.textContent = `${assetHistory.length} 条记录`;

    if (assetHistory.length === 0) {
      listEl.innerHTML = `
        <div class="empty-state">
          <i class="fas fa-history"></i>
          <h3>暂无历史记录</h3>
          <p>保存资产数据后将显示历史记录</p>
        </div>
      `;
      return;
    }

    // 初始化分页参数
    this.historyPage = 1;
    this.historyPageSize = 10;
    this.historyCache = new Map(); // 缓存已渲染的项目

    // 渲染第一页
    this.renderHistoryPage(1, true);
    console.timeEnd('renderAssetHistory');
  }

  // 分页渲染历史记录
  renderHistoryPage(page = 1, isInitial = false) {
    const listEl = document.getElementById('history-list');
    if (!listEl) return;

    const startIndex = (page - 1) * this.historyPageSize;
    const endIndex = Math.min(startIndex + this.historyPageSize, assetHistory.length);
    const pageRecords = assetHistory.slice(startIndex, endIndex);

    // 生成当前页的HTML
    const pageHtml = pageRecords.map(record => this.renderHistoryItem(record)).join('');

    if (isInitial) {
      // 初始加载，替换全部内容
      listEl.innerHTML = pageHtml + this.renderLoadMoreButton();
    } else {
      // 追加加载，移除加载更多按钮，添加新内容，再添加按钮
      const loadMoreBtn = listEl.querySelector('.load-more-btn');
      if (loadMoreBtn) {
        loadMoreBtn.remove();
      }
      listEl.insertAdjacentHTML('beforeend', pageHtml + this.renderLoadMoreButton());
    }

    this.historyPage = page;
  }

  // 渲染单个历史记录项（带缓存）
  renderHistoryItem(record) {
    // 检查缓存
    if (this.historyCache && this.historyCache.has(record.id)) {
      return this.historyCache.get(record.id);
    }

    const date = new Date(record.timestamp);
    const dateStr = date.toLocaleDateString('zh-CN');
    const timeStr = date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });

    const html = `
      <div class="history-item" onclick="financialSystem.showHistoryDetail('${record.id}')">
        <div class="history-header">
          <div class="history-date-info">
            <div class="history-date">${dateStr}</div>
            <div class="history-time">${timeStr}</div>
          </div>
          <div class="history-actions">
            <div class="history-total">¥${this.formatNumber(record.totalCNY)}</div>
            <button class="history-delete-btn" onclick="event.stopPropagation(); financialSystem.deleteHistoryRecord('${record.id}')" title="删除记录">
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>
        <div class="history-summary">
          ${this.getOptimizedAssetSummary(record.assets)}
        </div>
      </div>
    `;

    // 缓存结果
    if (this.historyCache) {
      this.historyCache.set(record.id, html);
    }

    return html;
  }

  // 渲染加载更多按钮
  renderLoadMoreButton() {
    const totalPages = Math.ceil(assetHistory.length / this.historyPageSize);
    const currentPage = this.historyPage || 1;

    if (currentPage >= totalPages) {
      return `
        <div class="history-end">
          <div class="end-message">
            <i class="fas fa-check-circle"></i>
            <span>已显示全部 ${assetHistory.length} 条记录</span>
          </div>
        </div>
      `;
    }

    return `
      <div class="load-more-btn" onclick="financialSystem.loadMoreHistory()">
        <i class="fas fa-chevron-down"></i>
        <span>加载更多 (${currentPage}/${totalPages})</span>
      </div>
    `;
  }

  // 加载更多历史记录
  loadMoreHistory() {
    const nextPage = (this.historyPage || 1) + 1;
    const totalPages = Math.ceil(assetHistory.length / this.historyPageSize);

    if (nextPage <= totalPages) {
      this.renderHistoryPage(nextPage, false);
    }
  }

  getAssetSummary(assets) {
    const summary = [];

    ASSET_CHANNELS.forEach(channel => {
      const asset = assets[channel.id];
      if (!asset) return;

      if (channel.crypto) {
        const cryptoAssets = Object.entries(asset);
        if (cryptoAssets.length > 0) {
          const assetStr = cryptoAssets.map(([symbol, amount]) =>
            `${amount} ${symbol}`
          ).join(', ');
          summary.push(`${channel.name}: ${assetStr}`);
        }
      } else {
        if (asset > 0) {
          const currencySymbol = channel.currency === 'USD' ? '$' :
                               channel.currency === 'HKD' ? 'HK$' : '¥';
          summary.push(`${channel.name}: ${currencySymbol}${this.formatNumber(asset)}`);
        }
      }
    });

    return summary.join(' | ') || '无资产数据';
  }

  // 优化的资产摘要显示（带缓存和性能优化）
  getOptimizedAssetSummary(assets) {
    // 生成缓存键
    const cacheKey = JSON.stringify(assets);

    // 检查缓存
    if (!this.summaryCache) {
      this.summaryCache = new Map();
    }

    if (this.summaryCache.has(cacheKey)) {
      return this.summaryCache.get(cacheKey);
    }

    // 快速检查是否有资产
    const hasAssets = ASSET_CHANNELS.some(channel => {
      const asset = assets[channel.id];
      return asset && (channel.crypto ? Object.keys(asset).length > 0 : asset > 0);
    });

    if (!hasAssets) {
      const result = '<div class="no-assets-summary">无资产数据</div>';
      this.summaryCache.set(cacheKey, result);
      return result;
    }

    // 使用数组拼接而不是字符串拼接（性能更好）
    const htmlParts = [];
    const traditionalItems = [];
    const cryptoItems = [];

    // 一次遍历收集所有数据
    ASSET_CHANNELS.forEach(channel => {
      const asset = assets[channel.id];
      if (!asset) return;

      if (channel.crypto) {
        const cryptoAssets = Object.entries(asset);
        if (cryptoAssets.length > 0) {
          const assetStr = cryptoAssets.map(([symbol, amount]) => `${amount} ${symbol}`).join(', ');
          cryptoItems.push(`
            <div class="asset-summary-item crypto">
              <span class="asset-icon-small" style="background-color: ${channel.color}">${channel.icon}</span>
              <span class="asset-name-small">${channel.name}</span>
              <span class="asset-amount-small">${assetStr}</span>
            </div>
          `);
        }
      } else {
        if (asset > 0) {
          const currencySymbol = channel.currency === 'USD' ? '$' :
                               channel.currency === 'HKD' ? 'HK$' : '¥';
          traditionalItems.push(`
            <div class="asset-summary-item">
              <span class="asset-icon-small" style="background-color: ${channel.color}">${channel.icon}</span>
              <span class="asset-name-small">${channel.name}</span>
              <span class="asset-amount-small">${currencySymbol}${this.formatNumber(asset)}</span>
            </div>
          `);
        }
      }
    });

    // 构建HTML
    if (traditionalItems.length > 0) {
      htmlParts.push(
        '<div class="asset-summary-section">',
        '<div class="asset-summary-title">💰 传统资产</div>',
        '<div class="asset-summary-items">',
        ...traditionalItems,
        '</div></div>'
      );
    }

    if (cryptoItems.length > 0) {
      htmlParts.push(
        '<div class="asset-summary-section">',
        '<div class="asset-summary-title">₿ 加密货币</div>',
        '<div class="asset-summary-items">',
        ...cryptoItems,
        '</div></div>'
      );
    }

    const result = htmlParts.length > 0 ? htmlParts.join('') : '<div class="no-assets-summary">无资产数据</div>';

    // 缓存结果，限制缓存大小
    if (this.summaryCache.size > 100) {
      // 清除最旧的缓存项
      const firstKey = this.summaryCache.keys().next().value;
      this.summaryCache.delete(firstKey);
    }
    this.summaryCache.set(cacheKey, result);

    return result;
  }

  // 删除历史记录（优化版本）
  deleteHistoryRecord(recordId) {
    if (confirm('确定要删除这条历史记录吗？此操作不可恢复。')) {
      const index = assetHistory.findIndex(record => record.id === recordId);
      if (index !== -1) {
        // 从数组中移除
        assetHistory.splice(index, 1);

        // 清除相关缓存
        if (this.historyCache) {
          this.historyCache.delete(recordId);
        }
        if (this.summaryCache) {
          this.summaryCache.clear(); // 清除摘要缓存，因为可能有关联
        }

        // 保存数据
        this.saveAssetHistory();

        // 优化重新渲染：只重新渲染当前页面
        const currentPage = this.historyPage || 1;
        const totalPages = Math.ceil(assetHistory.length / this.historyPageSize);

        if (currentPage > totalPages && totalPages > 0) {
          // 如果当前页超出范围，回到最后一页
          this.renderHistoryPage(totalPages, true);
        } else {
          // 重新渲染当前页
          this.renderAssetHistory();
        }

        // 异步重新渲染趋势图，避免阻塞UI
        setTimeout(() => {
          this.renderAssetTrendChart();
        }, 0);

        this.showToast('历史记录删除成功');
      }
    }
  }

  renderAssetTrendChart() {
    const canvas = document.getElementById('trend-chart');

    console.log('渲染资产趋势图...');
    console.log('Canvas元素:', canvas);

    // 确保 assetHistory 是数组
    if (!Array.isArray(assetHistory)) {
      assetHistory = [];
    }

    console.log('资产历史数据:', assetHistory.length, '条记录');

    if (!canvas) {
      console.error('找不到趋势图canvas元素');
      return;
    }

    // 如果没有历史数据，显示空状态
    if (assetHistory.length === 0) {
      console.log('没有历史数据，显示空状态');
      this.showEmptyTrendChart();
      return;
    }

    const ctx = canvas.getContext('2d');
    const period = document.getElementById('trend-period')?.value || '30';

    // 销毁现有图表
    if (assetTrendChart) {
      assetTrendChart.destroy();
    }

    // 准备数据
    let filteredHistory = [...assetHistory];

    if (period !== 'all') {
      const days = parseInt(period);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      filteredHistory = assetHistory.filter(record =>
        new Date(record.timestamp) >= cutoffDate
      );
    }

    console.log('筛选后的历史数据:', filteredHistory.length, '条记录');

    if (filteredHistory.length === 0) {
      this.showEmptyTrendChart();
      return;
    }

    filteredHistory.reverse(); // 按时间正序排列

    const labels = filteredHistory.map(record => {
      const date = new Date(record.timestamp);
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
    });

    const data = filteredHistory.map(record => record.totalCNY);

    console.log('图表数据:', { labels, data });

    const config = {
      type: 'line',
      data: {
        labels,
        datasets: [{
          label: '总资产',
          data,
          borderColor: 'rgba(63, 81, 181, 1)',
          backgroundColor: 'rgba(63, 81, 181, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: 'rgba(63, 81, 181, 1)',
          pointBorderColor: '#fff',
          pointBorderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context) {
                return '总资产: ¥' + context.parsed.y.toLocaleString();
              }
            }
          }
        },
        scales: {
          x: {
            display: true,
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: false,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            },
            ticks: {
              callback: function(value) {
                return '¥' + value.toLocaleString();
              }
            }
          }
        },
        interaction: {
          mode: 'nearest',
          axis: 'x',
          intersect: false
        }
      }
    };

    try {
      assetTrendChart = new Chart(ctx, config);
      console.log('资产趋势图创建成功');
    } catch (error) {
      console.error('创建资产趋势图失败:', error);
    }
  }

  // 显示空趋势图状态
  showEmptyTrendChart() {
    const canvas = document.getElementById('trend-chart');
    if (!canvas) return;

    // 销毁现有图表
    if (assetTrendChart) {
      assetTrendChart.destroy();
      assetTrendChart = null;
    }

    const ctx = canvas.getContext('2d');

    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制空状态提示
    ctx.fillStyle = '#999';
    ctx.font = '16px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('暂无历史数据', canvas.width / 2, canvas.height / 2 - 10);
    ctx.font = '14px Arial';
    ctx.fillText('保存资产数据后将显示趋势图', canvas.width / 2, canvas.height / 2 + 15);
  }
}

// 初始化应用
let financialSystem;

document.addEventListener('DOMContentLoaded', () => {
  const startTime = performance.now();

  try {
    console.log('🚀 初始化财务管理系统...');
    console.log('📊 初始数据状态:', {
      expenses: Array.isArray(expenses) ? `数组(${expenses.length}项)` : typeof expenses,
      assetHistory: Array.isArray(assetHistory) ? `数组(${assetHistory.length}项)` : typeof assetHistory,
      currentAssets: typeof currentAssets
    });

    financialSystem = new FinancialManagementSystem();

    const endTime = performance.now();
    const loadTime = Math.round(endTime - startTime);

    console.log(`✅ 财务管理系统初始化完成 (${loadTime}ms)`);

    // 性能提示
    if (loadTime > 1000) {
      console.warn(`⚠️ 初始化时间较长 (${loadTime}ms)，建议检查网络连接`);
    }

  } catch (error) {
    console.error('❌ 财务管理系统初始化失败:', error);

    // 显示用户友好的错误提示
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #f8d7da;
      color: #721c24;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #f5c6cb;
      z-index: 9999;
      max-width: 400px;
      text-align: center;
    `;
    errorDiv.innerHTML = `
      <h3>系统初始化失败</h3>
      <p>请刷新页面重试，如问题持续存在请联系技术支持。</p>
      <button onclick="location.reload()" style="margin-top: 10px; padding: 8px 16px; background: #721c24; color: white; border: none; border-radius: 4px; cursor: pointer;">
        刷新页面
      </button>
    `;
    document.body.appendChild(errorDiv);
  }
});
