// 简单的语法检查脚本
const fs = require('fs');

try {
  const scriptContent = fs.readFileSync('./script.js', 'utf8');
  
  // 检查基本的语法结构
  let braceCount = 0;
  let parenCount = 0;
  let bracketCount = 0;
  
  for (let i = 0; i < scriptContent.length; i++) {
    const char = scriptContent[i];
    switch (char) {
      case '{': braceCount++; break;
      case '}': braceCount--; break;
      case '(': parenCount++; break;
      case ')': parenCount--; break;
      case '[': bracketCount++; break;
      case ']': bracketCount--; break;
    }
  }
  
  console.log('语法检查结果:');
  console.log('大括号匹配:', braceCount === 0 ? '✅' : `❌ (不匹配: ${braceCount})`);
  console.log('小括号匹配:', parenCount === 0 ? '✅' : `❌ (不匹配: ${parenCount})`);
  console.log('中括号匹配:', bracketCount === 0 ? '✅' : `❌ (不匹配: ${bracketCount})`);
  
  // 尝试解析JavaScript
  try {
    new Function(scriptContent);
    console.log('JavaScript语法:', '✅ 有效');
  } catch (error) {
    console.log('JavaScript语法:', `❌ 错误: ${error.message}`);
  }
  
} catch (error) {
  console.error('读取文件失败:', error.message);
}
