/* 全局样式 */
:root {
  --primary-color: #3f51b5;
  --primary-light: #757de8;
  --primary-dark: #002984;
  --secondary-color: #ff4081;
  --secondary-light: #ff79b0;
  --secondary-dark: #c60055;
  --text-color: #212121;
  --text-secondary: #757575;
  --background-color: #f5f7fa;
  --card-color: #ffffff;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --border-radius: 12px;
  --box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  --transition: all 0.3s ease;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: var(--text-color);
  line-height: 1.5;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* 顶部栏样式 */
.top-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: transparent;
  backdrop-filter: none;
  padding: 12px 24px;
  display: flex;
  justify-content: flex-end;
  border-bottom: none;
  z-index: 50;
  box-shadow: none;
}

.logout-container {
  display: flex;
  align-items: center;
}

.logout-link {
  color: #e74c3c;
  text-decoration: none;
  font-size: 18px;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: rgba(231, 76, 60, 0.1);
  backdrop-filter: blur(10px);
}

.logout-link:hover {
  background: rgba(231, 76, 60, 0.2);
  transform: scale(1.05);
  color: #c0392b;
}

/* 汇率信息卡片样式 */
.exchange-rates-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.exchange-rates-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
}

.exchange-rates-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
  z-index: 1;
}

.exchange-rates-card .card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.exchange-rates-card .card-icon {
  font-size: 20px;
}

.exchange-rates-card .last-update {
  font-size: 12px;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 12px;
}

.rates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
  position: relative;
  z-index: 1;
}

.exchange-rates-card .rate-item {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.exchange-rates-card .rate-item:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.exchange-rates-card .rate-pair {
  font-size: 12px;
  opacity: 0.9;
  margin-bottom: 8px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.exchange-rates-card .rate-value {
  font-size: 18px;
  font-weight: 700;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 头部区域 */
.app-header {
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-md) var(--spacing-xl);
  box-shadow: var(--box-shadow);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1280px;
  margin: 0 auto;
}

.header-content h1 {
  font-size: 1.5rem;
  font-weight: var(--font-weight-medium);
}

/* 标签页导航 */
.tab-navigation {
  display: flex;
  gap: var(--spacing-sm);
}

.tab-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--spacing-sm);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.tab-btn.active {
  background: rgba(255, 255, 255, 0.25);
  font-weight: var(--font-weight-medium);
}

.tab-btn i {
  font-size: 1rem;
}

/* 主容器 */
.main-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--spacing-xl);
}

/* 标签页内容 */
.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* 仪表盘区域 */
.dashboard-section {
  margin-bottom: var(--spacing-xl);
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.dashboard-card {
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: var(--transition);
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.total-card .card-icon {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.card-content h3 {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.card-value {
  font-size: 1.8rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

/* 通用卡片样式 */
.form-section,
.chart-section,
.expenses-section {
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.section-header h2 {
  font-size: 1.25rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.section-header.with-actions {
  margin-bottom: var(--spacing-lg);
}

/* 表单样式 */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
  color: var(--text-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.form-group input,
.form-group select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid #ddd;
  border-radius: var(--spacing-sm);
  font-size: 0.9rem;
  transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(63, 81, 181, 0.1);
}

.form-actions {
  margin-top: var(--spacing-lg);
  display: flex;
  justify-content: center;
}

/* 按钮样式 */
.btn-primary,
.btn-secondary,
.btn-danger,
.btn-save,
.btn-clear {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--spacing-sm);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #d32f2f;
}

.btn-large {
  padding: var(--spacing-md) var(--spacing-lg);
  font-size: 1rem;
}

.btn-icon {
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: 50%;
  transition: var(--transition);
  color: var(--text-secondary);
}

.btn-icon:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-color);
}

.btn-icon.btn-danger {
  color: var(--danger-color);
}

.btn-icon.btn-danger:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

/* 图表控制 */
.chart-controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.chart-control-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.chart-type-toggle {
  display: flex;
  border: 1px solid #ddd;
  border-radius: var(--spacing-sm);
  overflow: hidden;
}

.chart-type-btn {
  background: white;
  border: none;
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-secondary);
}

.chart-type-btn.active {
  background-color: var(--primary-color);
  color: white;
}

.chart-period-select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid #ddd;
  border-radius: var(--spacing-sm);
  font-size: 0.9rem;
}

.chart-container {
  margin-top: var(--spacing-md);
  height: 300px;
}

/* 支出记录网格 */
.expenses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
}

.expense-item {
  background: #f8f9fa;
  border-radius: var(--spacing-sm);
  padding: var(--spacing-md);
  border-left: 4px solid var(--primary-color);
  transition: var(--transition);
  cursor: pointer;
}

.expense-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #e9ecef;
}

.expense-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.expense-type {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.expense-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: var(--transition);
}

.expense-item:hover .expense-actions {
  opacity: 1;
}

.expense-amount {
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.expense-date {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.expense-note {
  font-size: 0.9rem;
  color: var(--text-color);
  font-style: italic;
  background: rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xs);
  border-radius: var(--spacing-xs);
}

/* 月度聚合样式 */
.month-summary-item {
  background: #f8f9fa;
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border-left: 4px solid var(--primary-color);
  cursor: pointer;
  transition: var(--transition);
}

.month-summary-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background: #e9ecef;
}

.month-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.month-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.month-name {
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
}

.month-count {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.month-total {
  font-size: 1.3rem;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.month-preview {
  font-size: 0.9rem;
  color: var(--text-secondary);
  padding-top: var(--spacing-sm);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

/* 月度明细弹窗样式 */
.month-detail-list {
  margin-top: var(--spacing-lg);
}

.month-detail-list h4 {
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.month-expenses {
  max-height: 400px;
  overflow-y: auto;
}

.month-expense-item {
  background: #f8f9fa;
  border-radius: var(--spacing-sm);
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  cursor: pointer;
  transition: var(--transition);
  border-left: 3px solid var(--primary-color);
}

.month-expense-item:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.month-expense-item:last-child {
  margin-bottom: 0;
}

.month-expense-item .expense-type {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
}

.month-expense-item .expense-amount {
  font-size: 1rem;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.month-expense-item .expense-date {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.month-expense-item .expense-note {
  font-size: 0.8rem;
  color: var(--text-color);
  font-style: italic;
  background: rgba(0, 0, 0, 0.05);
  padding: var(--spacing-xs);
  border-radius: var(--spacing-xs);
}

/* 资产相关样式 */
.total-asset-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  display: flex;
  align-items: center;
  gap: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  margin-bottom: var(--spacing-xl);
}

.total-asset-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
}

.total-asset-icon {
  font-size: 48px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.total-asset-info {
  flex: 1;
}

.total-asset-label {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.total-asset-amount {
  font-size: 36px;
  font-weight: 700;
  color: #2d3748;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn-save,
.btn-clear,
.btn-refresh {
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.btn-save {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
}

.btn-save:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.btn-clear {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
}

.btn-clear:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.btn-refresh {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.btn-refresh:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
}

.btn-refresh:active {
  transform: scale(0.98);
}

.btn-refresh.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.btn-refresh.loading .btn-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 资产网格 */
.asset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: var(--spacing-xl);
}

/* 确保所有卡片高度一致 */
.asset-grid .asset-card {
  height: 200px;
}

/* 趋势图和历史记录卡片 */
.trend-card,
.history-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.4);
  margin-bottom: var(--spacing-xl);
}

.trend-header,
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.trend-header h3,
.history-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
}

.trend-chart-container {
  height: 300px;
  margin-top: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .main-container {
    padding: var(--spacing-md);
  }
  
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .dashboard-cards {
    grid-template-columns: 1fr;
  }
  
  .header-content {
    flex-direction: column;
    gap: var(--spacing-md);
  }
  
  .tab-navigation {
    width: 100%;
    justify-content: center;
  }

  .app-header {
    top: 0;
  }
  
  .total-asset-card {
    flex-direction: column;
    text-align: center;
    padding: 24px;
  }
  
  .asset-grid {
    grid-template-columns: 1fr;
  }

  /* 汇率卡片响应式 */
  .exchange-rates-card {
    padding: 16px;
    margin-bottom: 20px;
  }

  .exchange-rates-card .card-title {
    font-size: 14px;
  }

  .exchange-rates-card .card-icon {
    font-size: 18px;
  }

  .rates-grid {
    grid-template-columns: 1fr 1fr;
    gap: 12px;
  }

  .exchange-rates-card .rate-item {
    padding: 12px;
  }

  .exchange-rates-card .rate-value {
    font-size: 16px;
  }

  /* 移动端资产卡片优化 */
  .asset-card {
    min-height: 200px;
    padding: 16px;
  }

  .asset-amount-modern {
    font-size: 14px;
    line-height: 1.5;
  }

  .crypto-card .asset-amount-modern {
    font-size: 13px;
    max-height: 4.5em;
    -webkit-line-clamp: 3;
  }

  .crypto-card .asset-amount-section {
    min-height: 65px;
  }
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.modal-overlay.show {
  display: flex;
}

.modal-container {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

.modal-container.modal-small {
  max-width: 400px;
}

.modal-container.modal-large {
  max-width: 800px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1.1rem;
  font-weight: var(--font-weight-medium);
}

.modal-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: white;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  padding: var(--spacing-xs);
  border-radius: 50%;
  transition: var(--transition);
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-color);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
}

.btn-cancel,
.btn-confirm {
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--spacing-sm);
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  transition: var(--transition);
}

.btn-cancel {
  background: #f8f9fa;
  color: var(--text-color);
  border: 1px solid #dee2e6;
}

.btn-cancel:hover {
  background: #e9ecef;
}

.btn-confirm {
  background: var(--primary-color);
  color: white;
}

.btn-confirm:hover {
  background: var(--primary-dark);
}

/* Toast 提示 */
.toast {
  position: fixed;
  top: 100px;
  right: 20px;
  background: var(--success-color);
  color: white;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--spacing-sm);
  box-shadow: var(--box-shadow);
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
  z-index: 3000;
  font-weight: var(--font-weight-medium);
}

.toast.show {
  opacity: 1;
  transform: translateX(0);
}

.toast.error {
  background: var(--danger-color);
}

.toast.warning {
  background: var(--warning-color);
}

/* 资产卡片样式 */
.asset-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.asset-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.asset-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-color);
}

.asset-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.asset-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.asset-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.asset-name {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
}

.asset-actions {
  display: flex;
  gap: 8px;
}

.asset-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.3s ease;
  color: #666;
  font-size: 14px;
}

.asset-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #333;
}

.asset-amount {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.asset-equivalent {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* 现代化额外信息区域样式 - 紧凑版本 */
.crypto-price-info,
.asset-extra-info {
  background: rgba(255, 255, 255, 0.6);
  border-radius: 10px;
  padding: 10px;
  margin-top: 10px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.price-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.price-label,
.rate-label,
.status-label {
  font-size: 11px;
  color: #6b7280;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.price-time {
  font-size: 10px;
  color: #9ca3af;
  font-weight: 500;
  background: rgba(0, 0, 0, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
}

.price-values {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-usd {
  font-size: 16px;
  font-weight: 700;
  color: #f59e0b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.price-cny {
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.price-loading {
  font-size: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  padding: 8px;
}

.price-loading i {
  color: #f59e0b;
}

/* 汇率信息样式 */
.exchange-rate-info,
.asset-status-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rate-value,
.status-value {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* 价格更新动画 */
.crypto-price-info.price-updated {
  animation: priceUpdate 2s ease-in-out;
  border-left-color: #27ae60;
}

@keyframes priceUpdate {
  0% {
    background: linear-gradient(135deg, #d5f4e6, #a8e6cf);
    transform: scale(1);
  }
  50% {
    background: linear-gradient(135deg, #d5f4e6, #a8e6cf);
    transform: scale(1.02);
  }
  100% {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    transform: scale(1);
  }
}

.crypto-price-info.price-updated .price-usd,
.crypto-price-info.price-updated .price-cny {
  animation: priceTextUpdate 2s ease-in-out;
}

@keyframes priceTextUpdate {
  0%, 100% {
    color: #d68910;
  }
  50% {
    color: #27ae60;
    font-weight: var(--font-weight-bold);
  }
}

/* 现代化资产卡片样式 - 优化版本 */
.asset-card {
  position: relative;
  border-radius: 16px;
  padding: 18px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
  will-change: transform;
  min-height: 220px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
  word-wrap: break-word;
}

.asset-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08) !important;
}

/* 卡片顶部区域 - 紧凑版本 */
.card-top {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 14px;
}

.asset-header-modern {
  display: flex;
  align-items: center;
  gap: 10px;
}

.asset-icon-modern {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: hidden;
}

.asset-icon-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
  pointer-events: none;
}

.asset-title-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.asset-name-modern {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.2;
}

.asset-type-modern {
  font-size: 11px;
  color: #6b7280;
  font-weight: 500;
}

.currency-symbol-modern {
  font-size: 20px;
  font-weight: 700;
  opacity: 0.6;
  color: #374151;
}

/* 资产金额区域 - 紧凑版本 */
.asset-amount-section {
  margin: 12px 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 60px;
}

.asset-amount-modern {
  font-size: 18px;
  font-weight: 700;
  color: #111827;
  line-height: 1.3;
  margin-bottom: 3px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  word-break: break-all;
  overflow-wrap: break-word;
  max-width: 100%;
  display: block;
}

.asset-equivalent-modern {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  word-break: break-all;
  overflow-wrap: break-word;
}

/* 加密货币长数字特殊处理 */
.crypto-card .asset-amount-modern {
  font-size: 16px;
  line-height: 1.4;
  max-height: 3.6em;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.crypto-card .asset-amount-section {
  min-height: 70px;
}

/* 超长数字的科学计数法显示 */
.asset-amount-scientific {
  font-size: 14px;
  color: #6b7280;
  font-style: italic;
  margin-top: 2px;
}

/* 编辑按钮现代化 - 优化版本 */
.asset-edit-btn-modern {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.2s ease, transform 0.15s ease;
  opacity: 0;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  font-size: 12px;
}

.asset-card:hover .asset-edit-btn-modern {
  opacity: 1;
}

.asset-edit-btn-modern:hover {
  background: rgba(255, 255, 255, 1);
  color: #374151;
  transform: scale(1.05);
}

/* 加密货币卡片特殊效果 */
.crypto-card {
  position: relative;
  overflow: hidden;
}

.crypto-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.05) 0%, rgba(255, 193, 7, 0.02) 100%);
  pointer-events: none;
}

.crypto-card .currency-symbol-modern {
  color: #fbbf24;
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 美元资产卡片 */
.usd-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.02) 100%);
  pointer-events: none;
}

.usd-card .currency-symbol-modern {
  color: #22c55e;
}

/* 港币资产卡片 */
.hkd-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.05) 0%, rgba(147, 51, 234, 0.02) 100%);
  pointer-events: none;
}

.hkd-card .currency-symbol-modern {
  color: #9333ea;
}

/* 人民币资产卡片 */
.cny-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
  pointer-events: none;
}

.cny-card .currency-symbol-modern {
  color: #3b82f6;
}

/* 历史记录样式 */
.history-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border-left: 4px solid var(--primary-color);
  transition: all 0.3s ease;
}

.history-item:hover {
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.history-item:last-child {
  margin-bottom: 0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.history-date {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
}

.history-total {
  font-size: 16px;
  font-weight: 700;
  color: var(--primary-color);
}

.history-details {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

/* 空状态 */
.empty-state {
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-secondary);
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
}

/* 加载状态 */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--spacing-xl);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 资产加载状态 */
.loading-state {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.98);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  min-height: 200px;
}

.loading-state .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.loading-text {
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
}

/* 骨架屏样式 */
.skeleton-card {
  background: rgba(255, 255, 255, 0.98);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  animation: skeleton-loading 1.5s ease-in-out infinite alternate;
}

@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.7;
  }
}

.skeleton-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.skeleton-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
}

.skeleton-text {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 4px;
  animation: skeleton-shimmer 1.5s infinite;
}

.skeleton-text.large {
  height: 24px;
  margin-bottom: var(--spacing-sm);
}

.skeleton-text.medium {
  height: 20px;
  margin-bottom: var(--spacing-xs);
}

.skeleton-text.small {
  height: 14px;
  width: 60%;
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 筛选和操作区域 */
.section-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.filter-dropdown select {
  padding: var(--spacing-xs) var(--spacing-sm);
  border: 1px solid #ddd;
  border-radius: var(--spacing-sm);
  font-size: 0.9rem;
  background: white;
}

/* 图表图例 */
.chart-legend {
  margin-top: var(--spacing-md);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* 工具提示 */
.tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  pointer-events: none;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tooltip.show {
  opacity: 1;
}

/* 详情弹窗样式 */
.detail-content {
  padding: var(--spacing-md);
}

.detail-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.detail-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 600;
}

.detail-info h3 {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.detail-amount {
  font-size: 1.8rem;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.detail-type {
  font-size: 1rem;
  color: var(--text-secondary);
}

.detail-fields {
  margin-bottom: var(--spacing-lg);
}

.detail-field {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-field:last-child {
  border-bottom: none;
}

.detail-field label {
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  flex: 0 0 120px;
}

.detail-value {
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  text-align: right;
  flex: 1;
}

.detail-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: center;
  padding-top: var(--spacing-md);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

/* 加密货币资产详情样式 */
.crypto-assets-detail {
  margin-bottom: var(--spacing-lg);
}

.crypto-asset-item {
  display: grid;
  grid-template-columns: 80px 1fr 120px;
  gap: var(--spacing-md);
  align-items: center;
  padding: var(--spacing-md);
  background: #f8f9fa;
  border-radius: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.crypto-asset-item:last-child {
  margin-bottom: 0;
}

.crypto-symbol {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-size: 0.9rem;
}

.crypto-amount {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.crypto-value {
  text-align: right;
  font-size: 0.9rem;
}

.crypto-value div:first-child {
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
}

.crypto-value div:last-child {
  color: var(--text-secondary);
  font-size: 0.8rem;
}

.no-assets {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: var(--spacing-lg);
}

.detail-summary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  padding: var(--spacing-md);
  border-radius: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-item label {
  font-weight: var(--font-weight-medium);
  opacity: 0.9;
}

.summary-value {
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* 历史记录详情样式 */
.history-assets-detail {
  margin-top: var(--spacing-lg);
}

.history-assets-detail h4 {
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.history-asset-group {
  background: #f8f9fa;
  border-radius: var(--spacing-sm);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.history-asset-group:last-child {
  margin-bottom: 0;
}

.history-asset-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.history-asset-header .asset-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.history-asset-amount {
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.history-crypto-assets {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.history-crypto-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: white;
  border-radius: var(--spacing-xs);
}

.history-crypto-item .crypto-symbol {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-size: 0.9rem;
}

.history-crypto-item .crypto-amount {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  font-size: 0.9rem;
}

/* 历史记录项点击样式 */
.history-item {
  cursor: pointer;
  transition: var(--transition);
  position: relative;
}

.history-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 优化的历史记录头部 */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
}

.history-date-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.history-date {
  font-size: 14px;
  font-weight: var(--font-weight-bold);
  color: #2d3748;
}

.history-time {
  font-size: 12px;
  color: var(--text-secondary);
}

.history-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.history-total {
  font-size: 16px;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.history-delete-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: var(--transition);
  opacity: 0;
}

.history-item:hover .history-delete-btn {
  opacity: 1;
}

.history-delete-btn:hover {
  background: rgba(244, 67, 54, 0.1);
  color: var(--danger-color);
}

/* 优化的资产摘要 */
.history-summary {
  margin-top: var(--spacing-sm);
}

.asset-summary-section {
  margin-bottom: var(--spacing-sm);
}

.asset-summary-section:last-child {
  margin-bottom: 0;
}

.asset-summary-title {
  font-size: 12px;
  font-weight: var(--font-weight-bold);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  display: flex;
  align-items: center;
  gap: 4px;
}

.asset-summary-items {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.asset-summary-item {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.05);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.asset-summary-item.crypto {
  background: rgba(243, 156, 18, 0.1);
  border: 1px solid rgba(243, 156, 18, 0.2);
}

.asset-icon-small {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 8px;
  font-weight: 600;
}

.asset-name-small {
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.asset-amount-small {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.no-assets-summary {
  font-size: 12px;
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
  padding: var(--spacing-sm);
}

/* 历史记录汇率和价格信息样式 */
.history-rates-prices {
  margin-top: var(--spacing-lg);
  background: #f8f9fa;
  border-radius: var(--spacing-sm);
  padding: var(--spacing-md);
}

.history-rates-prices h4 {
  font-size: 1.1rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.rates-section,
.crypto-section {
  margin-bottom: var(--spacing-md);
}

.rates-section:last-child,
.crypto-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: 6px;
}

.rates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
}

.rate-item {
  background: white;
  padding: var(--spacing-sm);
  border-radius: var(--spacing-xs);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.rate-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.rate-value {
  font-size: 14px;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.crypto-prices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-sm);
}

.crypto-price-item {
  background: white;
  padding: var(--spacing-sm);
  border-radius: var(--spacing-xs);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.crypto-symbol {
  font-size: 14px;
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
  text-align: center;
}

.crypto-prices {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.crypto-prices .price-usd {
  font-size: 13px;
  font-weight: var(--font-weight-bold);
  color: #f59e0b;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.crypto-prices .price-cny {
  font-size: 12px;
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

.price-update-time {
  margin-top: var(--spacing-sm);
  text-align: center;
}

.price-update-time small {
  font-size: 11px;
  color: var(--text-secondary);
  font-style: italic;
}

/* 历史记录分页加载样式 */
.load-more-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  margin: var(--spacing-md) 0;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.load-more-btn:hover {
  background: linear-gradient(135deg, #e9ecef, #dee2e6);
  border-color: rgba(0, 0, 0, 0.12);
  color: var(--text-color);
  transform: translateY(-1px);
}

.load-more-btn i {
  font-size: 12px;
  transition: transform 0.2s ease;
}

.load-more-btn:hover i {
  transform: translateY(2px);
}

.history-end {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--text-secondary);
}

.end-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  font-size: 14px;
  font-weight: var(--font-weight-medium);
}

.end-message i {
  color: var(--success-color);
}

/* 历史记录性能优化 */
.history-list {
  contain: layout style paint;
}

.history-item {
  contain: layout style paint;
  will-change: auto;
}
