<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱批量工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 3px; max-height: 300px; overflow-y: auto; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        input[type="number"], input[type="url"] { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        .email-list { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 10px; }
        .email-item { padding: 10px; background: #e9ecef; border-radius: 3px; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>邮箱批量工具</h1>
        
        <div class="section">
            <h3>1. 生成临时邮箱</h3>
            <input type="number" id="emailCount" value="100" min="1" max="1000" placeholder="邮箱数量">
            <button onclick="generateEmails()">生成邮箱</button>
            <div id="generateResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>2. 批量注册用户</h3>
            <input type="url" id="registerUrl" value="http://localhost:8080/api/v1/user/register" placeholder="注册接口URL">
            <button onclick="batchRegister()">批量注册</button>
            <div id="registerResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>3. 获取验证码</h3>
            <button onclick="getVerificationCodes()">获取验证码</button>
            <button onclick="exportCodes()">导出验证码</button>
            <div id="codesResult" class="result"></div>
        </div>
        
        <div class="section">
            <h3>4. 操作日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="logResult" class="result"></div>
        </div>

        <div class="section">
            <h3>5. Augment账号批量注册</h3>
            <div style="margin-bottom: 10px;">
                <input type="number" id="augmentCount" value="10" min="1" max="100" placeholder="注册数量" style="width: 100px;">
                <input type="url" id="augmentUrl" value="https://www.augmentcode.com/api/auth/register" placeholder="Augment注册URL" style="width: 400px;">
            </div>
            <div style="margin-bottom: 10px;">
                <button onclick="batchRegisterAugment()" style="background: #28a745;">批量注册</button>
                <button onclick="autoVerifyAugment()" style="background: #17a2b8;">自动验证</button>
                <button onclick="completeAugmentRegistration()" style="background: #dc3545;">一键注册+验证</button>
                <button onclick="seleniumRegisterAugment()" style="background: #ff6b35;">🤖 Selenium自动化</button>
                <button onclick="clearAugmentCache()" style="background: #6c757d;">清空缓存</button>
            </div>
            <div id="augmentResult" class="result"></div>
        </div>
    </div>

    <script>
        let generatedEmails = [];
        let verificationCodes = {};
        
        function log(message, type = 'info') {
            const logDiv = document.getElementById('logResult');
            const time = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logDiv.innerHTML += `<div class="${className}">[${time}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        async function generateEmails() {
            const count = document.getElementById('emailCount').value;
            const resultDiv = document.getElementById('generateResult');
            
            log(`开始生成${count}个邮箱...`);
            resultDiv.innerHTML = '<div>正在生成邮箱，请稍候...</div>';
            
            try {
                const response = await fetch(`http://localhost:8080/api/tool/email/generate?count=${count}`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    generatedEmails = data.accounts;
                    resultDiv.innerHTML = `
                        <div class="success">成功生成${data.count}个邮箱</div>
                        <div class="email-list">
                            ${data.accounts.map(acc => `<div class="email-item">${acc.email}</div>`).join('')}
                        </div>
                    `;
                    log(`成功生成${data.count}个邮箱`, 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">生成失败: ${data.message}</div>`;
                    log(`生成邮箱失败: ${data.message}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
                log(`请求失败: ${error.message}`, 'error');
            }
        }
        
        async function batchRegister() {
            const registerUrl = document.getElementById('registerUrl').value;
            const resultDiv = document.getElementById('registerResult');
            
            if (generatedEmails.length === 0) {
                resultDiv.innerHTML = '<div class="error">请先生成邮箱</div>';
                return;
            }
            
            log(`开始批量注册${generatedEmails.length}个用户...`);
            resultDiv.innerHTML = '<div>正在批量注册，请稍候...</div>';
            
            try {
                const response = await fetch(`http://localhost:8080/api/tool/email/register?registerUrl=${encodeURIComponent(registerUrl)}`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">批量注册完成，共注册${data.count}个用户</div>`;
                    log(`批量注册完成，共注册${data.count}个用户`, 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">注册失败: ${data.message}</div>`;
                    log(`批量注册失败: ${data.message}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
                log(`请求失败: ${error.message}`, 'error');
            }
        }
        
        async function getVerificationCodes() {
            const resultDiv = document.getElementById('codesResult');
            
            log('开始获取验证码...');
            resultDiv.innerHTML = '<div>正在获取验证码，请稍候...</div>';
            
            try {
                const response = await fetch('http://localhost:8080/api/tool/email/codes');
                const data = await response.json();
                
                if (data.success) {
                    verificationCodes = data.codes;
                    const codesHtml = Object.entries(data.codes)
                        .map(([email, code]) => `<div class="email-item">${email}: <strong>${code}</strong></div>`)
                        .join('');
                    
                    resultDiv.innerHTML = `
                        <div class="success">成功获取${data.count}个验证码</div>
                        <div class="email-list">${codesHtml}</div>
                    `;
                    log(`成功获取${data.count}个验证码`, 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">获取失败: ${data.message}</div>`;
                    log(`获取验证码失败: ${data.message}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
                log(`请求失败: ${error.message}`, 'error');
            }
        }
        
        function exportCodes() {
            if (Object.keys(verificationCodes).length === 0) {
                log('没有验证码可导出', 'error');
                return;
            }
            
            const csvContent = "邮箱,验证码\n" + 
                Object.entries(verificationCodes)
                    .map(([email, code]) => `${email},${code}`)
                    .join('\n');
            
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `verification_codes_${new Date().getTime()}.csv`;
            link.click();
            
            log('验证码已导出到CSV文件', 'success');
        }
        
        function clearLog() {
            document.getElementById('logResult').innerHTML = '';
        }

        // Augment批量注册相关函数
        async function batchRegisterAugment() {
            const count = document.getElementById('augmentCount').value;
            const augmentUrl = document.getElementById('augmentUrl').value;
            const resultDiv = document.getElementById('augmentResult');

            log(`开始批量注册${count}个Augment账号...`);
            resultDiv.innerHTML = '<div>正在批量注册Augment账号，请稍候...</div>';

            try {
                const response = await fetch(`http://localhost:8080/api/tool/email/augment/register?count=${count}&registerUrl=${encodeURIComponent(augmentUrl)}`, {
                    method: 'POST'
                });
                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">${data.message}</div>
                        <div>总数: ${data.totalCount}, 注册成功: ${data.successCount}, 注册失败: ${data.failCount}</div>
                        <div class="email-list">
                            ${data.accounts.map(account =>
                                `<div class="email-item">📧 ${account.email}</div>`
                            ).join('')}
                        </div>
                        <details style="margin-top: 10px;">
                            <summary>注册详情</summary>
                            <div class="email-list">
                                ${data.registerResults.map(result =>
                                    `<div class="email-item ${result.success ? 'success' : 'error'}">
                                        ${result.email}: ${result.success ? '✅ 注册成功' : '❌ ' + (result.error || '注册失败')}
                                    </div>`
                                ).join('')}
                            </div>
                        </details>
                    `;
                    log(`Augment批量注册完成: ${data.successCount}/${data.totalCount}`, 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">批量注册失败: ${data.message}</div>`;
                    log(`Augment批量注册失败: ${data.message}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
                log(`请求失败: ${error.message}`, 'error');
            }
        }

        async function autoVerifyAugment() {
            const resultDiv = document.getElementById('augmentResult');

            log('开始自动验证Augment账号...');
            resultDiv.innerHTML = '<div>正在自动验证Augment账号，请稍候...</div>';

            try {
                const response = await fetch('http://localhost:8080/api/tool/email/augment/verify', {
                    method: 'POST'
                });
                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">${data.message}</div>
                        <div>总数: ${data.totalCount}, 验证成功: ${data.successCount}, 验证失败: ${data.failCount}</div>
                        <div class="email-list">
                            ${Object.entries(data.verifyResults).map(([email, result]) =>
                                `<div class="email-item ${result === '验证成功' ? 'success' : 'error'}">
                                    ${email}: ${result === '验证成功' ? '✅' : '❌'} ${result}
                                </div>`
                            ).join('')}
                        </div>
                    `;
                    log(`Augment自动验证完成: ${data.successCount}/${data.totalCount}`, 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">自动验证失败: ${data.message}</div>`;
                    log(`Augment自动验证失败: ${data.message}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
                log(`请求失败: ${error.message}`, 'error');
            }
        }

        async function completeAugmentRegistration() {
            const count = document.getElementById('augmentCount').value;
            const augmentUrl = document.getElementById('augmentUrl').value;
            const resultDiv = document.getElementById('augmentResult');

            log(`开始一键完成${count}个Augment账号注册和验证...`);
            resultDiv.innerHTML = '<div>正在执行一键注册+验证，请稍候（可能需要1-2分钟）...</div>';

            try {
                const response = await fetch(`http://localhost:8080/api/tool/email/augment/complete?count=${count}&registerUrl=${encodeURIComponent(augmentUrl)}`, {
                    method: 'POST'
                });
                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">${data.message}</div>
                        <div>
                            <strong>注册结果:</strong> ${data.registerSuccessCount}/${data.totalCount} 成功<br>
                            <strong>验证结果:</strong> ${data.verifySuccessCount}/${data.registerSuccessCount} 成功
                        </div>
                        <details style="margin-top: 10px;">
                            <summary>完整账号信息</summary>
                            <div class="email-list">
                                ${data.accounts.map((account, index) => {
                                    const registerResult = data.registerResults[index];
                                    const verifyResult = data.verifyResults[account.email];
                                    return `<div class="email-item">
                                        📧 ${account.email}<br>
                                        👤 用户名: ${registerResult.username || account.username}<br>
                                        🔑 密码: ${registerResult.password || 'AugmentTest123!'}<br>
                                        📝 注册: ${registerResult.success ? '✅ 成功' : '❌ 失败'}<br>
                                        ✅ 验证: ${verifyResult || '未验证'}
                                    </div>`;
                                }).join('')}
                            </div>
                        </details>
                    `;
                    log(`Augment一键注册验证完成: 注册${data.registerSuccessCount}/${data.totalCount}, 验证${data.verifySuccessCount}/${data.registerSuccessCount}`, 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">一键注册验证失败: ${data.message}</div>`;
                    log(`Augment一键注册验证失败: ${data.message}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
                log(`请求失败: ${error.message}`, 'error');
            }
        }

        async function clearAugmentCache() {
            const resultDiv = document.getElementById('augmentResult');

            try {
                const response = await fetch('http://localhost:8080/api/tool/email/clear-cache', {
                    method: 'POST'
                });
                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = `<div class="success">${data.message}</div>`;
                    log(data.message, 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">清空缓存失败: ${data.message}</div>`;
                    log(`清空缓存失败: ${data.message}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
                log(`清空缓存失败: ${error.message}`, 'error');
            }
        }

        // Selenium自动化注册函数
        async function seleniumRegisterAugment() {
            const count = document.getElementById('augmentCount').value;
            const augmentUrl = document.getElementById('augmentUrl').value;
            const resultDiv = document.getElementById('augmentResult');

            log(`开始使用Selenium自动化注册${count}个Augment账号...`);
            resultDiv.innerHTML = '<div>🤖 正在执行Selenium自动化注册，请稍候（可能需要2-5分钟）...</div>';

            try {
                const response = await fetch(`http://localhost:8080/api/tool/email/augment/selenium-register?count=${count}&registerUrl=${encodeURIComponent(augmentUrl)}`, {
                    method: 'POST'
                });
                const data = await response.json();

                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">🎉 ${data.message}</div>
                        <div>
                            <strong>自动化方法:</strong> Selenium Chrome<br>
                            <strong>成功率:</strong> ${data.successCount}/${data.totalCount} (${Math.round(data.successCount/data.totalCount*100)}%)
                        </div>
                        <details style="margin-top: 10px;">
                            <summary>🤖 Selenium结果详情</summary>
                            <div class="email-list">
                                ${data.results.map(result =>
                                    `<div class="email-item ${result.success ? 'success' : 'error'}">
                                        🤖 ${result.email}<br>
                                        ${result.success ?
                                            `✅ Selenium注册成功${result.verificationCode ? ' | 验证码: ' + result.verificationCode : ''}` :
                                            `❌ ${result.error || result.message}`
                                        }
                                        ${result.finalUrl ? `<br>🔗 最终URL: ${result.finalUrl}` : ''}
                                    </div>`
                                ).join('')}
                            </div>
                        </details>
                        <div style="margin-top: 10px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 12px;">
                            💡 提示: Selenium需要Chrome浏览器和WebDriver依赖
                        </div>
                    `;
                    log(`🤖 Selenium自动化注册完成: ${data.successCount}/${data.totalCount}`, 'success');
                } else {
                    resultDiv.innerHTML = `<div class="error">🤖 Selenium自动化注册失败: ${data.message}</div>`;
                    log(`🤖 Selenium自动化注册失败: ${data.message}`, 'error');
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">🤖 请求失败: ${error.message}</div>`;
                log(`🤖 请求失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('邮箱批量工具已就绪');
        });
    </script>
</body>
</html>
