<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>资产总结系统</title>
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
  <link rel="stylesheet" href="/asset/style.css">
  <link rel="icon" href="/asset/favicon.svg">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- 汇率显示 -->
  <div class="exchange-rates" id="exchange-rates">
    <div class="rate-item">
      <span class="rate-label">USD/CNY:</span>
      <span class="rate-value">加载中...</span>
    </div>
    <div class="rate-item">
      <span class="rate-label">HKD/CNY:</span>
      <span class="rate-value">加载中...</span>
    </div>
    <div class="rate-item">
      <a href="/asset/logout" class="logout-link" title="安全退出">🔓</a>
    </div>
  </div>

  <!-- 主要内容区 -->
  <div class="main-container">
    <!-- 总资产卡片 -->
    <div class="total-asset-card">
      <div class="total-asset-icon">💰</div>
      <div class="total-asset-info">
        <div class="total-asset-label">总资产</div>
        <div class="total-asset-amount" id="total-amount">¥ 0.00</div>
      </div>
      <div class="action-buttons">
        <button class="btn-save" id="save-btn">
          <span class="btn-icon">💾</span>
          保存
        </button>
        <button class="btn-clear" id="clear-btn">
          <span class="btn-icon">🗑️</span>
          清空
        </button>
      </div>
    </div>

    <!-- 资产网格 -->
    <div class="asset-grid" id="asset-grid">
      <!-- 资产卡片将由JavaScript生成 -->
    </div>

    <!-- 趋势图卡片 -->
    <div class="trend-card">
      <div class="trend-header">
        <h3>资产趋势</h3>
        <div class="trend-controls">
          <select id="trend-period" class="trend-select">
            <option value="7">最近7天</option>
            <option value="30" selected>最近30天</option>
            <option value="90">最近90天</option>
            <option value="all">全部</option>
          </select>
        </div>
      </div>
      <div class="trend-chart-container">
        <canvas id="trend-chart"></canvas>
      </div>
    </div>

    <!-- 历史记录卡片 -->
    <div class="history-card">
      <div class="history-header">
        <h3>历史记录</h3>
        <div class="history-actions">
          <span class="history-count" id="history-count">0 条记录</span>
          <button class="btn-clear-history" id="clear-history-btn" title="清空所有历史记录">
            <span>🗑️</span>
          </button>
        </div>
      </div>
      <div class="history-list" id="history-list">
        <!-- 历史记录将由JavaScript生成 -->
      </div>
    </div>
  </div>

  <!-- 编辑弹窗 -->
  <div class="modal-overlay" id="edit-modal">
    <div class="modal-container">
      <div class="modal-header">
        <div class="modal-title">
          <div class="modal-icon" id="modal-icon"></div>
          <span id="modal-title-text">编辑资产</span>
        </div>
        <button class="close-btn" id="close-modal">&times;</button>
      </div>
      <div class="modal-body" id="modal-body">
        <!-- 编辑内容将由JavaScript生成 -->
      </div>
      <div class="modal-footer">
        <button class="btn-cancel" id="cancel-btn">取消</button>
        <button class="btn-confirm" id="confirm-btn">确认</button>
      </div>
    </div>
  </div>

  <!-- 详情弹窗 -->
  <div class="modal-overlay" id="detail-modal">
    <div class="modal-container modal-large">
      <div class="modal-header">
        <div class="modal-title">
          <span>资产详情</span>
        </div>
        <button class="close-btn" id="close-detail-modal">&times;</button>
      </div>
      <div class="modal-body" id="detail-modal-body">
        <!-- 详情内容将由JavaScript生成 -->
      </div>
    </div>
  </div>

  <!-- 确认删除弹窗 -->
  <div class="modal-overlay" id="confirm-modal">
    <div class="modal-container modal-small">
      <div class="modal-header">
        <div class="modal-title">
          <span>确认删除</span>
        </div>
        <button class="close-btn" id="close-confirm-modal">&times;</button>
      </div>
      <div class="modal-body">
        <p id="confirm-message">确定要删除这条记录吗？</p>
      </div>
      <div class="modal-footer">
        <button class="btn-cancel" id="confirm-cancel-btn">取消</button>
        <button class="btn-danger" id="confirm-delete-btn">删除</button>
      </div>
    </div>
  </div>

  <!-- 提示消息 -->
  <div class="toast" id="toast"></div>

  <script src="/asset/script.js"></script>
</body>
</html> 