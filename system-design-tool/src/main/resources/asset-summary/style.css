/* 基础重置和字体 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  padding: 20px;
}

/* 汇率显示 */
.exchange-rates {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  padding: 12px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 1000;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
}

.rate-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 500;
}

.rate-label {
  color: #666;
}

.rate-value {
  color: #2d3748;
  font-weight: 600;
}

.logout-link {
  color: #e74c3c;
  text-decoration: none;
  font-size: 16px;
  padding: 5px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: inline-block;
}

.logout-link:hover {
  background: rgba(231, 76, 60, 0.1);
  transform: scale(1.1);
}

/* 主容器 */
.main-container {
  max-width: 1200px;
  margin: 60px auto 0;
  display: grid;
  gap: 24px;
}

/* 总资产卡片 */
.total-asset-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  display: flex;
  align-items: center;
  gap: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.total-asset-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);
}

.total-asset-icon {
  font-size: 48px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.total-asset-info {
  flex: 1;
}

.total-asset-label {
  font-size: 16px;
  color: #718096;
  margin-bottom: 8px;
  font-weight: 500;
}

.total-asset-amount {
  font-size: 36px;
  font-weight: 700;
  color: #2d3748;
  background: linear-gradient(135deg, #4299e1, #3182ce);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.btn-save, .btn-clear {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-save {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
  box-shadow: 0 4px 12px rgba(72, 187, 120, 0.3);
}

.btn-save:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(72, 187, 120, 0.4);
}

.btn-clear {
  background: rgba(255, 255, 255, 0.95);
  color: #718096;
  border: 1px solid rgba(113, 128, 150, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btn-clear:hover {
  background: rgba(255, 255, 255, 1);
  color: #4a5568;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-icon {
  font-size: 16px;
}

/* 资产网格 */
.asset-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.asset-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.asset-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--card-color), var(--card-color-light));
}

.asset-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.asset-card-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.asset-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.asset-info {
  flex: 1;
}

.asset-name {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
}

.asset-type {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 6px;
  font-weight: 500;
  background: rgba(113, 128, 150, 0.15);
  color: #4a5568;
  display: inline-block;
}

.asset-amount {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.asset-value {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.asset-details {
  margin-top: 12px;
  font-size: 13px;
  color: #a0aec0;
}

/* 加密货币特殊样式 */
.crypto-card {
  background: linear-gradient(135deg, rgba(251, 191, 36, 0.15), rgba(245, 158, 11, 0.08));
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.crypto-assets {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
}

.crypto-asset {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(245, 158, 11, 0.15);
}

.crypto-asset:last-child {
  border-bottom: none;
}

.crypto-symbol {
  font-weight: 600;
  color: #92400e;
}

.crypto-amount {
  font-size: 13px;
  color: #78716c;
}

.crypto-value {
  font-weight: 600;
  color: #059669;
}

/* 外币特殊样式 */
.currency-card {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(29, 78, 216, 0.08));
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* 趋势图卡片 */
.trend-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 28px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  margin-bottom: 24px;
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(113, 128, 150, 0.15);
}

.trend-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
}

.trend-controls {
  display: flex;
  gap: 12px;
}

.trend-select {
  padding: 8px 12px;
  border: 1px solid rgba(113, 128, 150, 0.3);
  border-radius: 8px;
  background: white;
  font-size: 14px;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
}

.trend-select:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.trend-chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* 历史记录卡片 */
.history-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 28px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(113, 128, 150, 0.15);
}

.history-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
}

.history-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.history-count {
  font-size: 14px;
  color: #718096;
  background: rgba(113, 128, 150, 0.1);
  padding: 4px 12px;
  border-radius: 20px;
}

.btn-clear-history {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  color: #718096;
  transition: all 0.2s ease;
  font-size: 16px;
}

.btn-clear-history:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: rgba(247, 250, 252, 0.9);
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  group: 1;
}

.history-item:hover {
  background: rgba(237, 242, 247, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.history-item-content {
  flex: 1;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-date {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.history-amount {
  font-size: 18px;
  font-weight: 700;
  color: #059669;
}

.history-actions-btn {
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
  margin-left: 12px;
}

.history-item:hover .history-actions-btn {
  opacity: 1;
}

.btn-delete {
  background: none;
  border: none;
  padding: 6px 8px;
  border-radius: 6px;
  cursor: pointer;
  color: #718096;
  transition: all 0.2s ease;
  font-size: 14px;
}

.btn-delete:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* 弹窗样式 */
.modal-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 2000;
  backdrop-filter: blur(4px);
  opacity: 0;
  transition: opacity 0.3s ease;
  overflow-y: auto;
  padding: 20px 0;
}

.modal-overlay.show {
  opacity: 1;
}

.modal-container {
  background: white;
  margin: 20px auto;
  border-radius: 20px;
  width: 90%;
  max-width: 500px;
  max-height: calc(100vh - 80px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-large {
  max-width: 700px;
}

.modal-small {
  max-width: 400px;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  border-bottom: 1px solid #e2e8f0;
  background: #f7fafc;
  flex-shrink: 0;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
}

.modal-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  font-size: 16px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #a0aec0;
  cursor: pointer;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(160, 174, 192, 0.1);
  color: #718096;
}

.modal-body {
  padding: 28px;
  overflow-y: auto;
  flex: 1;
  max-height: calc(100vh - 200px);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 28px;
  background: #f7fafc;
  border-top: 1px solid #e2e8f0;
  flex-shrink: 0;
}

.btn-cancel, .btn-confirm, .btn-danger {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-cancel:hover {
  background: #cbd5e0;
}

.btn-confirm {
  background: linear-gradient(135deg, #4299e1, #3182ce);
  color: white;
}

.btn-confirm:hover {
  background: linear-gradient(135deg, #3182ce, #2c5282);
}

.btn-danger {
  background: linear-gradient(135deg, #f56565, #e53e3e);
  color: white;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #e53e3e, #c53030);
}

/* 表单元素 */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.crypto-inputs {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.crypto-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.crypto-input-group label {
  min-width: 60px;
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

/* 详情表格样式 */
.detail-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 16px;
}

.detail-table th,
.detail-table td {
  text-align: left;
  padding: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.detail-table th {
  background: #f7fafc;
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

.detail-table td {
  color: #2d3748;
  font-size: 14px;
}

.detail-table .amount-cell {
  font-weight: 600;
  color: #059669;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

/* 提示消息 */
.toast {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(45, 55, 72, 0.95);
  color: white;
  padding: 16px 24px;
  border-radius: 12px;
  font-weight: 500;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  z-index: 3000;
  opacity: 0;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.toast.show {
  opacity: 1;
  transform: translateX(-50%) translateY(-10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  body {
    padding: 16px;
  }

  .main-container {
    margin-top: 70px;
    gap: 20px;
  }

  .total-asset-card {
    flex-direction: column;
    text-align: center;
    padding: 24px;
  }

  .total-asset-amount {
    font-size: 28px;
  }

  .action-buttons {
    justify-content: center;
  }

  .asset-grid {
    grid-template-columns: 1fr;
  }

  .modal-overlay {
    padding: 10px 0;
  }

  .modal-container {
    width: 95%;
    margin: 10px auto;
    max-height: calc(100vh - 40px);
  }

  .modal-body {
    padding: 20px;
    max-height: calc(100vh - 140px);
  }

  .modal-header {
    padding: 20px;
  }

  .modal-footer {
    padding: 16px 20px;
  }

  .exchange-rates {
    padding: 8px 16px;
    font-size: 12px;
  }

  .trend-chart-container {
    height: 250px;
  }

  .history-actions-btn {
    opacity: 1;
  }

  .detail-table {
    font-size: 13px;
  }

  .detail-table th,
  .detail-table td {
    padding: 8px;
  }

  /* 移动设备滚动优化 */
  .modal-body {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
  }

  /* 为移动设备优化表格 */
  .detail-table {
    font-size: 12px;
    min-width: 100%;
  }

  .detail-table th,
  .detail-table td {
    padding: 6px 4px;
    word-break: break-word;
  }
} 