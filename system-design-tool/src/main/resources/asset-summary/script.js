// 资产渠道配置
const ASSET_CHANNELS = [
  { id: 'alipay', name: '支付宝', color: '#1677FF', icon: '支', type: 'CNY' },
  { id: 'wechat', name: '微信', color: '#1AAD19', icon: '微', type: 'CNY' },
  { id: 'icbc', name: '工商银行', color: '#C41E3A', icon: '工', type: 'CNY' },
  { id: 'cmb', name: '招商银行', color: '#E60012', icon: '招', type: 'CNY' },
  { id: 'yuhang', name: '余杭农商银行', color: '#4CAF50', icon: '余', type: 'CNY' },
  { id: 'boc', name: '中国银行', color: '#B71C1C', icon: '中', type: 'CNY' },
  { 
    id: 'hsbc', 
    name: '香港汇丰银行', 
    color: '#D32F2F', 
    icon: 'H',
    type: 'HKD',
    currency: 'HKD',
    defaultAmount: 5200.94
  },
  { 
    id: 'bochk', 
    name: '中银香港', 
    color: '#8B0000', 
    icon: '港',
    type: 'HKD',
    currency: 'HKD',
    defaultAmount: 2406.65
  },
  { 
    id: 'tiger', 
    name: '老虎证券', 
    color: '#FF9800', 
    icon: '虎',
    type: 'USD',
    currency: 'USD'
  },
  { 
    id: 'longbridge', 
    name: '长桥证券', 
    color: '#9C27B0', 
    icon: '桥',
    type: 'USD',
    currency: 'USD'
  },
  { 
    id: 'futu', 
    name: '富途牛牛', 
    color: '#795548', 
    icon: '牛',
    type: 'USD',
    currency: 'USD'
  },
  { 
    id: 'binance', 
    name: '币安', 
    color: '#F3BA2F', 
    icon: '币', 
    type: 'CRYPTO',
    crypto: true,
    defaultAssets: {
      BTC: 0.03118642,
      PEPE: 167010986.47615952
    },
    editable: true
  },
  { 
    id: 'victory', 
    name: '胜利通', 
    color: '#1976D2', 
    icon: 'V',
    type: 'CRYPTO',
    crypto: true,
    defaultAssets: {
      BTC: 0.0067
    },
    editable: true
  },
  { 
    id: 'wallet', 
    name: '钱包', 
    color: '#8BC34A', 
    icon: '包',
    type: 'CRYPTO',
    crypto: true,
    defaultAssets: {
      BTC: 0.00922
    },
    editable: true
  }
];

// 全局变量
let cryptoPrices = {};
let exchangeRates = {};
let lastPriceUpdate = 0;
const PRICE_CACHE_DURATION = 60000; // 1分钟缓存
let trendChart = null;
let deleteRecordId = null;

class AssetSummarySystem {
  constructor() {
    this.currentAssets = {};
    this.records = [];
    this.editingChannel = null;
  }

  async init() {
    await this.loadRecords();
    
    // 先渲染一次基本内容（使用默认值）
    this.renderAssetCards();
    this.renderHistory();
    this.renderTrendChart();
    this.bindEvents();
    this.updateTotalAsset();
    
    // 然后异步加载汇率和价格数据
    this.initExchangeRates();
    this.initCryptoPrices();
  }

  // 绑定事件
  bindEvents() {
    // 保存和清空按钮
    document.getElementById('save-btn').addEventListener('click', () => this.saveRecord());
    document.getElementById('clear-btn').addEventListener('click', () => this.clearAssets());
    
    // 弹窗事件
    document.getElementById('close-modal').addEventListener('click', () => this.closeEditModal());
    document.getElementById('close-detail-modal').addEventListener('click', () => this.closeDetailModal());
    document.getElementById('close-confirm-modal').addEventListener('click', () => this.closeConfirmModal());
    document.getElementById('cancel-btn').addEventListener('click', () => this.closeEditModal());
    document.getElementById('confirm-btn').addEventListener('click', () => this.confirmEdit());
    
    // 确认删除弹窗事件
    document.getElementById('confirm-cancel-btn').addEventListener('click', () => this.closeConfirmModal());
    document.getElementById('confirm-delete-btn').addEventListener('click', () => this.confirmDelete());
    
    // 清空历史记录
    document.getElementById('clear-history-btn').addEventListener('click', () => this.clearAllHistory());
    
    // 趋势图时间筛选
    document.getElementById('trend-period').addEventListener('change', () => this.renderTrendChart());
    
    // 点击弹窗外部关闭
    document.getElementById('edit-modal').addEventListener('click', (e) => {
      if (e.target.id === 'edit-modal') this.closeEditModal();
    });
    document.getElementById('detail-modal').addEventListener('click', (e) => {
      if (e.target.id === 'detail-modal') this.closeDetailModal();
    });
    document.getElementById('confirm-modal').addEventListener('click', (e) => {
      if (e.target.id === 'confirm-modal') this.closeConfirmModal();
    });
  }

  // 初始化汇率
  async initExchangeRates() {
    try {
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
      const data = await response.json();
      exchangeRates = {
        USD: data.rates.CNY || 7.2,
        HKD: (data.rates.CNY || 7.2) / (data.rates.HKD || 7.8)
      };
    } catch (error) {
      console.log('汇率API失败，使用默认值');
      exchangeRates = { USD: 7.2, HKD: 0.92 };
    }
    
    // 更新汇率显示
    document.querySelector('.rate-item:nth-child(1) .rate-value').textContent = 
      `¥${exchangeRates.USD.toFixed(2)}`;
    document.querySelector('.rate-item:nth-child(2) .rate-value').textContent = 
      `¥${exchangeRates.HKD.toFixed(2)}`;
      
    // 汇率更新后重新渲染资产卡片
    this.renderAssetCards();
    this.updateTotalAsset();
  }

  // 初始化加密货币价格
  async initCryptoPrices() {
    const now = Date.now();
    if (now - lastPriceUpdate < PRICE_CACHE_DURATION && Object.keys(cryptoPrices).length > 0) {
      return;
    }

    try {
      const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=bitcoin,pepe&vs_currencies=usd,cny');
      const data = await response.json();
      
      cryptoPrices = {
        BTC: {
          USD: data.bitcoin.usd,
          CNY: data.bitcoin.cny
        },
        PEPE: {
          USD: data.pepe.usd,
          CNY: data.pepe.cny
        }
      };
      lastPriceUpdate = now;
      console.log('加密货币价格已加载:', cryptoPrices);
    } catch (error) {
      console.log('加密货币价格API失败，使用备用价格');
      cryptoPrices = {
        BTC: { USD: 98000, CNY: 700000 },
        PEPE: { USD: 0.00001, CNY: 0.00007 }
      };
      lastPriceUpdate = now;
    }
    
    // 价格加载完成后重新渲染
    this.renderAssetCards();
    this.updateTotalAsset();
  }

  // 渲染资产卡片
  renderAssetCards() {
    const grid = document.getElementById('asset-grid');
    grid.innerHTML = ASSET_CHANNELS.map(channel => {
      const amount = this.getAssetAmount(channel);
      const value = this.calculateAssetValue(channel, amount);
      
      let cardClass = 'asset-card';
      if (channel.crypto) cardClass += ' crypto-card';
      if (channel.currency) cardClass += ' currency-card';

      return `
        <div class="${cardClass}" 
             data-channel-id="${channel.id}" 
             style="--card-color: ${channel.color}; --card-color-light: ${channel.color}88"
             onclick="assetSystem.openEditModal('${channel.id}')">
          <div class="asset-card-header">
            <div class="asset-icon" style="background: ${channel.color}">
              ${channel.icon}
            </div>
            <div class="asset-info">
              <div class="asset-name">${channel.name}</div>
              <div class="asset-type">${this.getAssetTypeLabel(channel)}</div>
            </div>
          </div>
          ${this.renderAssetContent(channel, amount, value)}
        </div>
      `;
    }).join('');
  }

  // 渲染资产内容
  renderAssetContent(channel, amount, value) {
    if (channel.crypto) {
      return this.renderCryptoContent(channel);
    } else {
      const displayAmount = amount || (channel.defaultAmount || 0);
      const currencySymbol = this.getCurrencySymbol(channel);
      
      return `
        <div class="asset-amount">${currencySymbol}${this.formatNumber(displayAmount)}</div>
        <div class="asset-value">≈ ¥${this.formatNumber(value)}</div>
        ${channel.currency ? `<div class="asset-details">汇率: ${currencySymbol}1 = ¥${exchangeRates[channel.currency]?.toFixed(2) || '0.00'}</div>` : ''}
      `;
    }
  }

  // 渲染加密货币内容
  renderCryptoContent(channel) {
    let totalValue = 0;
    const cryptoAssetsHtml = Object.entries(channel.defaultAssets).map(([symbol, defaultAmount]) => {
      // 确保使用用户设置的值，如果没有则使用默认值
      const userAmount = this.currentAssets[`${channel.id}_${symbol}`] !== undefined 
        ? this.currentAssets[`${channel.id}_${symbol}`] 
        : defaultAmount;
      const price = cryptoPrices[symbol];
      const value = (price && price.CNY) ? userAmount * price.CNY : 0;
      totalValue += value;
      
      return `
        <div class="crypto-asset">
          <div>
            <div class="crypto-symbol">${symbol}</div>
            <div class="crypto-amount">${this.formatNumber(userAmount, 8)}</div>
          </div>
          <div class="crypto-value">¥${this.formatNumber(value)}</div>
        </div>
      `;
    }).join('');

    return `
      <div class="asset-amount">¥${this.formatNumber(totalValue)}</div>
      <div class="crypto-assets">
        ${cryptoAssetsHtml}
      </div>
      <div class="asset-details">
        ${Object.keys(channel.defaultAssets).map(symbol => {
          const price = cryptoPrices[symbol];
          if (price && price.USD) {
            return `1 ${symbol} = $${this.formatNumber(price.USD)}`;
          } else {
            return `${symbol}: 价格加载中...`;
          }
        }).join(' | ')}
      </div>
    `;
  }

  // 渲染趋势图
  renderTrendChart() {
    const canvas = document.getElementById('trend-chart');
    const ctx = canvas.getContext('2d');
    
    // 销毁现有图表
    if (trendChart) {
      trendChart.destroy();
    }

    // 获取时间周期
    const period = document.getElementById('trend-period').value;
    const filteredRecords = this.getFilteredRecords(period);

    if (filteredRecords.length === 0) {
      // 没有数据时显示提示
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.fillStyle = '#a0aec0';
      ctx.font = '16px -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto';
      ctx.textAlign = 'center';
      ctx.fillText('暂无数据', canvas.width / 2, canvas.height / 2);
      return;
    }

    // 准备数据
    const labels = filteredRecords.map(record => {
      const date = new Date(record.timestamp);
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
    });

    const data = filteredRecords.map(record => record.total);

    // 创建图表
    trendChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: labels,
        datasets: [{
          label: '总资产',
          data: data,
          borderColor: '#4299e1',
          backgroundColor: 'rgba(66, 153, 225, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: '#4299e1',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6,
          pointHoverRadius: 8
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: false,
            ticks: {
              callback: function(value) {
                return '¥' + value.toLocaleString('zh-CN');
              }
            }
          }
        },
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                return '总资产: ¥' + context.parsed.y.toLocaleString('zh-CN');
              }
            }
          }
        },
        elements: {
          point: {
            hoverBorderWidth: 3
          }
        }
      }
    });
  }

  // 获取筛选后的记录
  getFilteredRecords(period) {
    if (period === 'all') {
      return [...this.records].reverse(); // 按时间正序
    }

    const days = parseInt(period);
    const cutoffDate = Date.now() - (days * 24 * 60 * 60 * 1000);
    
    return this.records
      .filter(record => record.timestamp > cutoffDate)
      .reverse(); // 按时间正序
  }

  // 打开编辑弹窗
  openEditModal(channelId) {
    this.editingChannel = ASSET_CHANNELS.find(c => c.id === channelId);
    if (!this.editingChannel) return;

    const modal = document.getElementById('edit-modal');
    const modalIcon = document.getElementById('modal-icon');
    const modalTitle = document.getElementById('modal-title-text');
    const modalBody = document.getElementById('modal-body');

    // 设置弹窗标题和图标
    modalIcon.style.background = this.editingChannel.color;
    modalIcon.textContent = this.editingChannel.icon;
    modalTitle.textContent = `编辑 ${this.editingChannel.name}`;

    // 生成编辑表单
    modalBody.innerHTML = this.generateEditForm(this.editingChannel);

    // 显示弹窗
    modal.style.display = 'block';
    setTimeout(() => modal.classList.add('show'), 10);
  }

  // 生成编辑表单
  generateEditForm(channel) {
    if (channel.crypto) {
      return `
        <div class="crypto-inputs">
          ${Object.entries(channel.defaultAssets).map(([symbol, defaultAmount]) => {
            const currentAmount = this.currentAssets[`${channel.id}_${symbol}`] || defaultAmount;
            return `
              <div class="crypto-input-group">
                <label for="edit-${channel.id}-${symbol}">${symbol}:</label>
                <input 
                  type="number" 
                  class="form-input" 
                  id="edit-${channel.id}-${symbol}" 
                  value="${currentAmount}"
                  step="any"
                  min="0"
                  placeholder="${defaultAmount}"
                >
              </div>
            `;
          }).join('')}
        </div>
      `;
    } else {
      const currentAmount = this.currentAssets[channel.id] || (channel.defaultAmount || 0);
      const currencySymbol = this.getCurrencySymbol(channel);
      
      return `
        <div class="form-group">
          <label class="form-label" for="edit-amount">金额 (${currencySymbol})</label>
          <input 
            type="number" 
            class="form-input" 
            id="edit-amount" 
            value="${currentAmount}"
            step="0.01"
            min="0"
            placeholder="0.00"
          >
          ${channel.currency ? `<div style="margin-top: 8px; font-size: 14px; color: #718096;">当前汇率: ${currencySymbol}1 = ¥${exchangeRates[channel.currency]?.toFixed(2) || '0.00'}</div>` : ''}
        </div>
      `;
    }
  }

  // 确认编辑
  confirmEdit() {
    if (!this.editingChannel) return;

    if (this.editingChannel.crypto) {
      // 保存加密货币数据
      Object.keys(this.editingChannel.defaultAssets).forEach(symbol => {
        const input = document.getElementById(`edit-${this.editingChannel.id}-${symbol}`);
        if (input) {
          const value = parseFloat(input.value) || 0;
          this.currentAssets[`${this.editingChannel.id}_${symbol}`] = value;
        }
      });
    } else {
      // 保存普通资产数据
      const input = document.getElementById('edit-amount');
      if (input) {
        const value = parseFloat(input.value) || 0;
        this.currentAssets[this.editingChannel.id] = value;
      }
    }

    this.saveAssets();
    this.renderAssetCards();
    this.updateTotalAsset();
    this.closeEditModal();
    this.showToast('资产已更新');
  }

  // 关闭编辑弹窗
  closeEditModal() {
    const modal = document.getElementById('edit-modal');
    modal.classList.remove('show');
    setTimeout(() => {
      modal.style.display = 'none';
      this.editingChannel = null;
    }, 300);
  }

  // 关闭详情弹窗
  closeDetailModal() {
    const modal = document.getElementById('detail-modal');
    modal.classList.remove('show');
    setTimeout(() => modal.style.display = 'none', 300);
  }

  // 关闭确认弹窗
  closeConfirmModal() {
    const modal = document.getElementById('confirm-modal');
    modal.classList.remove('show');
    setTimeout(() => {
      modal.style.display = 'none';
      deleteRecordId = null;
    }, 300);
  }

  // 获取资产金额
  getAssetAmount(channel) {
    if (channel.crypto) {
      // 加密货币返回总价值
      let total = 0;
      Object.entries(channel.defaultAssets).forEach(([symbol, defaultAmount]) => {
        const amount = this.currentAssets[`${channel.id}_${symbol}`] !== undefined 
          ? this.currentAssets[`${channel.id}_${symbol}`] 
          : defaultAmount;
        const price = cryptoPrices[symbol];
        if (price && price.CNY) {
          total += amount * price.CNY;
        }
      });
      return total;
    } else {
      return this.currentAssets[channel.id] !== undefined 
        ? this.currentAssets[channel.id] 
        : (channel.defaultAmount || 0);
    }
  }

  // 计算资产价值（人民币）
  calculateAssetValue(channel, amount) {
    if (channel.crypto) {
      return amount; // 加密货币已经是人民币价值
    } else if (channel.currency) {
      const rate = exchangeRates[channel.currency] || 1;
      return amount * rate;
    } else {
      return amount; // 人民币
    }
  }

  // 获取货币符号
  getCurrencySymbol(channel) {
    if (channel.currency === 'USD') return '$';
    if (channel.currency === 'HKD') return 'HK$';
    return '¥';
  }

  // 获取资产类型标签
  getAssetTypeLabel(channel) {
    if (channel.crypto) return '加密货币';
    if (channel.currency) return channel.currency;
    return '人民币';
  }

  // 更新总资产
  updateTotalAsset() {
    let total = 0;
    ASSET_CHANNELS.forEach(channel => {
      const amount = this.getAssetAmount(channel);
      const value = this.calculateAssetValue(channel, amount);
      total += value;
    });

    document.getElementById('total-amount').textContent = `¥ ${this.formatNumber(total)}`;
  }

  // 保存记录
  saveRecord() {
    // 构建完整的资产快照，包括默认值
    const completeAssets = {};
    
    ASSET_CHANNELS.forEach(channel => {
      if (channel.crypto) {
        Object.keys(channel.defaultAssets).forEach(symbol => {
          const key = `${channel.id}_${symbol}`;
          // 使用用户输入的值，如果没有则使用默认值
          completeAssets[key] = this.currentAssets[key] !== undefined 
            ? this.currentAssets[key] 
            : channel.defaultAssets[symbol];
        });
      } else {
        // 使用用户输入的值，如果没有则使用默认值
        completeAssets[channel.id] = this.currentAssets[channel.id] !== undefined 
          ? this.currentAssets[channel.id] 
          : (channel.defaultAmount || 0);
      }
    });

    const record = {
      id: Date.now(),
      date: new Date().toLocaleString('zh-CN'),
      timestamp: Date.now(),
      assets: completeAssets,
      total: this.calculateTotalAsset(),
      exchangeRates: { ...exchangeRates },
      cryptoPrices: { ...cryptoPrices }
    };

    this.records.unshift(record);
    if (this.records.length > 100) {
      this.records = this.records.slice(0, 100);
    }

    this.saveRecords();
    this.renderHistory();
    this.renderTrendChart();
    this.showToast('资产记录已保存');
  }

  // 清空资产
  clearAssets() {
    if (confirm('确定要清空所有资产数据吗？')) {
      this.currentAssets = {};
      this.saveAssets();
      this.renderAssetCards();
      this.updateTotalAsset();
      this.showToast('资产已清空');
    }
  }

  // 清空所有历史记录
  clearAllHistory() {
    if (confirm('确定要清空所有历史记录吗？此操作不可恢复！')) {
      this.records = [];
      this.saveRecords();
      this.renderHistory();
      this.renderTrendChart();
      this.showToast('历史记录已清空');
    }
  }

  // 删除单条记录
  deleteRecord(recordId) {
    deleteRecordId = recordId;
    const record = this.records.find(r => r.id === recordId);
    if (!record) return;

    const modal = document.getElementById('confirm-modal');
    const message = document.getElementById('confirm-message');
    message.textContent = `确定要删除 ${record.date} 的记录吗？`;

    modal.style.display = 'block';
    setTimeout(() => modal.classList.add('show'), 10);
  }

  // 确认删除
  confirmDelete() {
    if (!deleteRecordId) return;

    this.records = this.records.filter(r => r.id !== deleteRecordId);
    this.saveRecords();
    this.renderHistory();
    this.renderTrendChart();
    this.closeConfirmModal();
    this.showToast('记录已删除');
  }

  // 计算总资产
  calculateTotalAsset() {
    let total = 0;
    ASSET_CHANNELS.forEach(channel => {
      const amount = this.getAssetAmount(channel);
      const value = this.calculateAssetValue(channel, amount);
      total += value;
    });
    return total;
  }

  // 渲染历史记录
  renderHistory() {
    const historyList = document.getElementById('history-list');
    const historyCount = document.getElementById('history-count');
    
    historyCount.textContent = `${this.records.length} 条记录`;

    if (this.records.length === 0) {
      historyList.innerHTML = '<div style="text-align: center; color: #a0aec0; padding: 20px;">暂无历史记录</div>';
      return;
    }

    historyList.innerHTML = this.records.map(record => `
      <div class="history-item">
        <div class="history-item-content" onclick="assetSystem.showDetail(${record.id})">
          <div class="history-date">${record.date}</div>
          <div class="history-amount">¥${this.formatNumber(record.total)}</div>
        </div>
        <div class="history-actions-btn">
          <button class="btn-delete" onclick="assetSystem.deleteRecord(${record.id})" title="删除记录">
            🗑️
          </button>
        </div>
      </div>
    `).join('');
  }

  // 显示详情
  showDetail(recordId) {
    const record = this.records.find(r => r.id === recordId);
    if (!record) return;

    const modal = document.getElementById('detail-modal');
    const modalBody = document.getElementById('detail-modal-body');

    let detailHtml = `
      <div class="detail-section">
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
          <div>
            <div style="font-size: 14px; color: #718096; margin-bottom: 8px;">记录时间</div>
            <div style="font-size: 16px; font-weight: 600;">${record.date}</div>
          </div>
          <div>
            <div style="font-size: 14px; color: #718096; margin-bottom: 8px;">总资产</div>
            <div style="font-size: 24px; font-weight: 700; color: #059669;">¥${this.formatNumber(record.total)}</div>
          </div>
        </div>
      </div>
    `;

    // 资产明细 - 放在最前面，最重要的信息
    detailHtml += `
      <div class="detail-section">
        <h4>资产明细</h4>
        <table class="detail-table">
          <thead>
            <tr>
              <th>资产</th>
              <th>数量/金额</th>
              <th>价值(CNY)</th>
            </tr>
          </thead>
          <tbody>
    `;

    // 显示所有资产，包括0值的
    let assetRows = [];
    
    ASSET_CHANNELS.forEach(channel => {
      if (channel.crypto) {
        Object.keys(channel.defaultAssets).forEach(symbol => {
          const key = `${channel.id}_${symbol}`;
          let amount = record.assets[key];
          
          // 如果没有保存的值，使用默认值
          if (amount === undefined || amount === null) {
            amount = channel.defaultAssets[symbol];
          }
          
          // 只要金额大于0就显示
          if (amount > 0) {
            const price = record.cryptoPrices?.[symbol];
            const value = price ? amount * price.CNY : 0;
            assetRows.push(`
              <tr>
                <td>${channel.name} (${symbol})</td>
                <td>${this.formatNumber(amount, 8)}</td>
                <td class="amount-cell">¥${this.formatNumber(value)}</td>
              </tr>
            `);
          }
        });
      } else {
        let amount = record.assets[channel.id];
        
        // 如果没有保存的值，使用默认值
        if (amount === undefined || amount === null) {
          amount = channel.defaultAmount || 0;
        }
        
        // 只要金额大于0就显示
        if (amount > 0) {
          const symbol = this.getCurrencySymbol(channel);
          let value = amount;
          
          // 计算当时的价值
          if (channel.currency && record.exchangeRates) {
            const rate = record.exchangeRates[channel.currency];
            value = rate ? amount * rate : amount;
          }
          
          assetRows.push(`
            <tr>
              <td>${channel.name}</td>
              <td>${symbol}${this.formatNumber(amount)}</td>
              <td class="amount-cell">¥${this.formatNumber(value)}</td>
            </tr>
          `);
        }
      }
    });

    // 如果有资产数据，显示表格行
    if (assetRows.length > 0) {
      detailHtml += assetRows.join('');
    } else {
      detailHtml += `
        <tr>
          <td colspan="3" style="text-align: center; color: #a0aec0; padding: 20px;">
            该记录未包含资产数据
          </td>
        </tr>
      `;
    }

    detailHtml += `
          </tbody>
        </table>
      </div>
    `;

    // 汇率信息
    if (record.exchangeRates) {
      detailHtml += `
        <div class="detail-section">
          <h4>汇率信息</h4>
          <table class="detail-table">
            <thead>
              <tr>
                <th>货币对</th>
                <th>汇率</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>USD/CNY</td>
                <td>¥${record.exchangeRates.USD?.toFixed(4) || 'N/A'}</td>
              </tr>
              <tr>
                <td>HKD/CNY</td>
                <td>¥${record.exchangeRates.HKD?.toFixed(4) || 'N/A'}</td>
              </tr>
            </tbody>
          </table>
        </div>
      `;
    }

    // 加密货币价格
    if (record.cryptoPrices) {
      detailHtml += `
        <div class="detail-section">
          <h4>加密货币价格</h4>
          <table class="detail-table">
            <thead>
              <tr>
                <th>货币</th>
                <th>USD价格</th>
                <th>CNY价格</th>
              </tr>
            </thead>
            <tbody>
              ${Object.entries(record.cryptoPrices).map(([symbol, prices]) => `
                <tr>
                  <td>${symbol}</td>
                  <td>$${this.formatNumber(prices.USD)}</td>
                  <td>¥${this.formatNumber(prices.CNY)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      `;
    }

    modalBody.innerHTML = detailHtml;

    modal.style.display = 'block';
    setTimeout(() => modal.classList.add('show'), 10);
  }

  // 显示提示消息
  showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.classList.add('show');
    setTimeout(() => toast.classList.remove('show'), 3000);
  }

  // 格式化数字
  formatNumber(num, decimals = 2) {
    if (num === 0) return '0.00';
    if (!num) return '0.00';
    
    const factor = Math.pow(10, decimals);
    const rounded = Math.round(num * factor) / factor;
    
    return rounded.toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  }

  // 保存数据到服务器
  async saveAssets() {
    try {
      const response = await fetch('/api/asset/assets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(this.currentAssets)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      console.log('资产数据已保存到服务器:', result);
    } catch (error) {
      console.error('保存资产数据失败:', error);
      this.showToast('保存资产数据失败，请检查网络连接');
    }
  }

  async saveRecords() {
    try {
      const data = {
        records: this.records,
        assets: this.currentAssets
      };
      
      const response = await fetch('/api/asset/records', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const result = await response.json();
      console.log('记录数据已保存到服务器:', result);
    } catch (error) {
      console.error('保存记录数据失败:', error);
      this.showToast('保存记录数据失败，请检查网络连接');
    }
  }

  // 从服务器加载数据
  async loadAssets() {
    try {
      const response = await fetch('/api/asset/assets');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const assets = await response.json();
      this.currentAssets = assets;
      console.log('从服务器加载资产数据:', assets);
    } catch (error) {
      console.error('加载资产数据失败:', error);
      this.currentAssets = {};
    }
    
    // 确保加密货币的默认值被加载
    this.initializeDefaultAssets();
  }

  // 初始化默认资产值
  initializeDefaultAssets() {
    ASSET_CHANNELS.forEach(channel => {
      if (channel.crypto && channel.defaultAssets) {
        Object.entries(channel.defaultAssets).forEach(([symbol, defaultAmount]) => {
          const key = `${channel.id}_${symbol}`;
          // 如果没有保存的值，则设置默认值
          if (this.currentAssets[key] === undefined) {
            this.currentAssets[key] = defaultAmount;
          }
        });
      } else if (channel.defaultAmount && this.currentAssets[channel.id] === undefined) {
        // 为有默认值的普通资产设置默认值
        this.currentAssets[channel.id] = channel.defaultAmount;
      }
    });
  }

  async loadRecords() {
    try {
      const response = await fetch('/api/asset/records');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      this.records = data.records || [];
      
      // 如果服务器返回了资产数据，也加载它
      if (data.assets) {
        this.currentAssets = data.assets;
      }
      
      console.log('从服务器加载记录数据:', this.records.length, '条记录');
    } catch (error) {
      console.error('加载记录数据失败:', error);
      this.records = [];
    }
    
    await this.loadAssets();
  }
}

// 初始化系统
let assetSystem;
document.addEventListener('DOMContentLoaded', async () => {
  assetSystem = new AssetSummarySystem();
  await assetSystem.init();
}); 