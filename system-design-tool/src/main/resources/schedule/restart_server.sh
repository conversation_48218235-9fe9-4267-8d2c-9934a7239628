#!/bin/bash

echo "=== 日程管理器一键重启脚本 ==="
echo ""

# 第一步：停止现有服务
echo "1. 停止现有Flask服务..."
if [ -f "server.pid" ]; then
    PID=$(cat server.pid)
    echo "发现Flask服务进程 PID: $PID"
    
    # 检查进程是否存在
    if ps -p $PID > /dev/null; then
        echo "正在停止Flask服务..."
        kill $PID
        sleep 2
        
        # 再次检查是否已停止
        if ps -p $PID > /dev/null; then
            echo "强制停止Flask服务..."
            kill -9 $PID
        fi
        
        echo "✅ Flask服务已停止"
        rm -f server.pid
    else
        echo "⚠️  进程不存在，删除PID文件"
        rm -f server.pid
    fi
else
    echo "⚠️  未找到PID文件，检查是否有其他Flask进程..."
    # 查找并停止可能的Flask进程
    pkill -f "python3.6.*app.py" 2>/dev/null
fi

echo ""

# 第二步：检查Python环境
echo "2. 检查Python环境..."
if ! command -v /usr/bin/python3.6 &> /dev/null; then
    echo "错误: 未找到Python3.6，请先安装Python3.6"
    exit 1
fi

# 检查必要文件
if [ ! -f "app.py" ]; then
    echo "错误: 未找到app.py文件"
    exit 1
fi

echo "✅ Python环境检查通过"
echo ""

# 第三步：启动新服务
echo "3. 启动新的Flask服务..."
echo "服务地址: http://localhost:5000"
echo ""

# 后台启动Flask应用
nohup /usr/bin/python3.6 app.py > server.log 2>&1 &
SERVER_PID=$!

# 保存PID到文件
echo $SERVER_PID > server.pid

# 等待服务启动
sleep 3

# 检查服务是否成功启动
if ps -p $SERVER_PID > /dev/null; then
    echo "✅ Flask服务重启成功！"
    echo "📄 进程PID: $SERVER_PID"
    echo "📄 日志文件: server.log"
    echo "🛑 停止服务: ./stop_server.sh"
    echo ""
    echo "🌐 访问地址:"
    echo "  - 主页: http://localhost:5000/"
    echo "  - API: http://localhost:5000/api/schedules"
    echo "  - 健康检查: http://localhost:5000/api/health"
    echo ""
    echo "💡 提示:"
    echo "  - 查看日志: tail -f server.log"
    echo "  - 查看进程: ps aux | grep app.py"
    echo "  - 默认密码: xiaobiao."
else
    echo "❌ Flask服务启动失败，请检查日志文件 server.log"
    exit 1
fi

echo ""
echo "=== 重启完成 ===" 