// 日程管理器类
class ScheduleManager {
    constructor() {
        this.schedules = [];
        this.weekPlans = [];
        this.categories = this.getDefaultCategories();
        this.currentFilter = 'all';
        this.currentView = 'list';
        this.currentDate = dayjs().tz('Asia/Shanghai').toDate();
        this.summaryType = 'monthly';
        this.summaryDate = dayjs().tz('Asia/Shanghai').toDate();
        this.editingId = null;
        this.editingCategoryId = null;
        this.editingWeekPlanId = null;
        this.currentWeekDate = dayjs().tz('Asia/Shanghai').toDate();
        this.selectedWeekRange = null;
        this.API_URL = '/api';
        
        // 从磁盘加载数据
        this.loadSchedulesFromDisk();
        
        this.init();
    }



    // 初始化
    init() {
        this.bindEvents();
        // 移除重复的渲染调用，因为loadSchedulesFromDisk会调用这些方法
        // this.renderSchedules();
        // this.updateEmptyState();
        // this.updateCategoryOptions();
    }

    // 获取默认分类
    getDefaultCategories() {
        return [
            { id: 'work', name: '工作', color: 'blue', isDefault: true },
            { id: 'personal', name: '个人', color: 'pink', isDefault: true },
            { id: 'study', name: '学习', color: 'green', isDefault: true },
            { id: 'health', name: '健康', color: 'yellow', isDefault: true },
            { id: 'other', name: '其他', color: 'gray', isDefault: true }
        ];
    }

    // 绑定事件
    bindEvents() {
        // 添加日程按钮
        document.getElementById('addEventBtn').addEventListener('click', () => {
            this.openModal();
        });

        // 分类管理按钮
        document.getElementById('categoryManagerBtn').addEventListener('click', () => {
            this.openCategoryModal();
        });

        // 表单提交
        document.getElementById('eventForm').addEventListener('submit', (e) => {
            this.handleFormSubmit(e);
        });

        // 模态框关闭
        document.getElementById('closeModal').addEventListener('click', () => {
            this.closeModal();
        });

        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.closeModal();
        });

        // 确认删除
        document.getElementById('confirmDeleteBtn').addEventListener('click', () => {
            this.confirmDelete();
        });

        // 取消删除
        document.getElementById('cancelDeleteBtn').addEventListener('click', () => {
            this.closeDeleteModal();
        });

        document.getElementById('closeDeleteModal').addEventListener('click', () => {
            this.closeDeleteModal();
        });

        // 搜索
        document.getElementById('searchInput').addEventListener('input', (e) => {
            this.handleSearch(e.target.value);
        });

        // 筛选
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleFilter(e.target.dataset.filter);
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });

        // 视图切换
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchView(e.target.dataset.view);
                document.querySelectorAll('.view-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });

        // 日历导航
        document.getElementById('prevMonth').addEventListener('click', () => {
            this.navigateMonth(-1);
        });

        document.getElementById('nextMonth').addEventListener('click', () => {
            this.navigateMonth(1);
        });

        // 日期详情模态框
        document.getElementById('closeDayDetailModal').addEventListener('click', () => {
            this.closeDayDetailModal();
        });

        // 分类管理模态框
        document.getElementById('closeCategoryModal').addEventListener('click', () => {
            this.closeCategoryModal();
        });

        // 分类模态框背景点击关闭
        document.getElementById('categoryModal').addEventListener('click', (e) => {
            if (e.target.id === 'categoryModal') {
                this.closeCategoryModal();
            }
        });

        // 分类表单提交
        document.getElementById('categoryForm').addEventListener('submit', (e) => {
            this.handleCategoryFormSubmit(e);
        });

        // 取消分类编辑
        document.getElementById('cancelCategoryBtn').addEventListener('click', () => {
            this.resetCategoryForm();
        });

        // 总结类型切换
        document.querySelectorAll('.summary-nav-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchSummaryType(e.target.dataset.type);
            });
        });

        // 总结导航
        document.getElementById('prevSummaryMonth').addEventListener('click', () => {
            this.navigateSummary(-1);
        });

        document.getElementById('nextSummaryMonth').addEventListener('click', () => {
            this.navigateSummary(1);
        });

        document.getElementById('prevSummaryQuarter').addEventListener('click', () => {
            this.navigateSummary(-3);
        });

        document.getElementById('nextSummaryQuarter').addEventListener('click', () => {
            this.navigateSummary(3);
        });

        // 模态框背景点击关闭
        document.getElementById('eventModal').addEventListener('click', (e) => {
            if (e.target.id === 'eventModal') {
                this.closeModal();
            }
        });

        document.getElementById('deleteModal').addEventListener('click', (e) => {
            if (e.target.id === 'deleteModal') {
                this.closeDeleteModal();
            }
        });

        document.getElementById('dayDetailModal').addEventListener('click', (e) => {
            if (e.target.id === 'dayDetailModal') {
                this.closeDayDetailModal();
            }
        });

        // 周计划相关事件
        document.getElementById('prevWeekMonth').addEventListener('click', () => {
            this.navigateWeekMonth(-1);
        });

        document.getElementById('nextWeekMonth').addEventListener('click', () => {
            this.navigateWeekMonth(1);
        });

        // 周计划模态框事件
        document.getElementById('closeWeekPlanModal').addEventListener('click', () => {
            this.closeWeekPlanModal();
        });

        document.getElementById('cancelWeekPlanBtn').addEventListener('click', () => {
            this.closeWeekPlanModal();
        });

        document.getElementById('saveWeekPlanBtn').addEventListener('click', () => {
            this.saveWeekPlan();
        });

        document.getElementById('addWeekTaskBtn').addEventListener('click', () => {
            this.addWeekTask();
        });

        // 周计划模态框背景点击关闭
        document.getElementById('weekPlanModal').addEventListener('click', (e) => {
            if (e.target.id === 'weekPlanModal') {
                this.closeWeekPlanModal();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
                this.closeDeleteModal();
                this.closeDayDetailModal();
                this.closeCategoryModal();
                this.closeWeekPlanModal();
            }
        });
    }

    // 从磁盘加载数据
    async loadSchedulesFromDisk() {
        try {
            this.updateSaveStatus('正在加载数据...', 'loading');
            
            const response = await fetch(`${this.API_URL}/schedules`);
            if (!response.ok) {
                throw new Error(`服务器错误: ${response.status}`);
            }
            
            const data = await response.json();
            
            // 处理加载的数据
            if (data && !data.error) {
                // 新格式：包含schedules、categories和weekPlans
                if (data.schedules !== undefined && data.categories !== undefined) {
                    this.schedules = Array.isArray(data.schedules) ? data.schedules : [];
                    this.categories = Array.isArray(data.categories) ? data.categories : this.getDefaultCategories();
                    this.weekPlans = Array.isArray(data.weekPlans) ? data.weekPlans : [];
                    console.log(`✅ 成功加载新格式数据: ${this.schedules.length} 条日程, ${this.categories.length} 个分类, ${this.weekPlans.length} 个周计划`);
                }
                // 兼容旧格式：直接是数组
                else if (Array.isArray(data)) {
                    this.schedules = data;
                    this.categories = this.getDefaultCategories();
                    this.weekPlans = [];
                    console.log(`✅ 成功加载旧格式数据: ${data.length} 条日程（使用默认分类和空周计划）`);
                }
                // 其他情况，初始化空数据
                else {
                    this.schedules = [];
                    this.categories = this.getDefaultCategories();
                    this.weekPlans = [];
                    console.log('📄 初始化空日程列表和默认分类');
                }
            } else {
                // 错误或空数据
                this.schedules = [];
                this.categories = this.getDefaultCategories();
                console.log('📄 初始化空日程列表和默认分类');
                if (data && data.error) {
                    console.error('服务器返回错误:', data.error);
                }
            }
            
            this.updateSaveStatus('数据加载完成', 'success');
            
            // 渲染界面
            this.renderSchedules();
            this.updateEmptyState();
            this.updateCategoryOptions();
            
            // 确保初始视图状态正确
            this.switchView('list');
            
        } catch (error) {
            console.error('❌ 从服务器加载数据失败:', error);
            
            // 失败时使用默认数据
            this.schedules = [];
            this.categories = this.getDefaultCategories();
            
            // 渲染界面
            this.renderSchedules();
            this.updateEmptyState();
            this.updateCategoryOptions();
            
            // 确保初始视图状态正确
            this.switchView('list');
            
            this.updateSaveStatus('加载失败，请检查网络连接', 'error');
            this.showToast('数据加载失败，使用默认设置', 'error');
        }
    }

    // 保存完整的JSON数据到磁盘
    async saveSchedulesToDisk() {
        try {
            this.updateSaveStatus('正在保存数据...', 'loading');
            
            // 保存新格式：包含schedules、categories和weekPlans
            const dataToSave = {
                schedules: Array.isArray(this.schedules) ? this.schedules : [],
                categories: Array.isArray(this.categories) ? this.categories : this.getDefaultCategories(),
                weekPlans: Array.isArray(this.weekPlans) ? this.weekPlans : []
            };
            
            const response = await fetch(`${this.API_URL}/schedules`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(dataToSave)
            });
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`服务器错误 ${response.status}: ${errorText}`);
            }
            
            const result = await response.json();
            
            if (result.error) {
                throw new Error(result.error);
            }
            
            console.log(`✅ 成功保存 ${dataToSave.schedules.length} 条日程数据、${dataToSave.categories.length} 个分类和 ${dataToSave.weekPlans.length} 个周计划到磁盘`);
            this.updateSaveStatus('数据已保存', 'success');
            
        } catch (error) {
            console.error('❌ 保存数据到磁盘失败:', error);
            this.updateSaveStatus('保存失败', 'error');
            this.showToast(`数据保存失败: ${error.message}`, 'error');
            throw error; // 重新抛出错误，让调用者知道保存失败
        }
    }





    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 打开模态框
    openModal(schedule = null) {
        const modal = document.getElementById('eventModal');
        const modalTitle = document.getElementById('modalTitle');
        const form = document.getElementById('eventForm');

        if (schedule) {
            modalTitle.textContent = '编辑日程';
            this.editingId = schedule.id;
            this.fillForm(schedule);
        } else {
            modalTitle.textContent = '添加日程';
            this.editingId = null;
            form.reset();
            // 设置默认日期为今天
            document.getElementById('eventDate').value = new Date().toISOString().split('T')[0];
        }

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    // 关闭模态框
    closeModal() {
        const modal = document.getElementById('eventModal');
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
        this.editingId = null;
    }

    // 填充表单数据
    fillForm(schedule) {
        document.getElementById('eventTitle').value = schedule.title;
        document.getElementById('eventDescription').value = schedule.description || '';
        document.getElementById('eventDate').value = schedule.date;
        document.getElementById('eventTime').value = schedule.time || '';
        document.getElementById('eventPriority').value = schedule.priority;
        document.getElementById('eventCategory').value = schedule.category;
        document.getElementById('eventScore').value = schedule.score || 10;
    }

    // 处理表单提交
    async handleFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const scheduleData = {
            title: formData.get('title').trim(),
            description: formData.get('description').trim(),
            date: formData.get('date'),
            time: formData.get('time'),
            priority: formData.get('priority'),
            category: formData.get('category'),
            score: parseInt(formData.get('score')) || 10,
            completed: false,
            createdAt: new Date().toISOString()
        };

        if (!scheduleData.title || !scheduleData.date) {
            this.showToast('请填写必填字段', 'error');
            return;
        }

        let isEdit = false;
        if (this.editingId) {
            // 编辑现有日程
            const index = this.schedules.findIndex(s => s.id === this.editingId);
            if (index !== -1) {
                scheduleData.id = this.editingId;
                scheduleData.completed = this.schedules[index].completed;
                scheduleData.createdAt = this.schedules[index].createdAt;
                this.schedules[index] = scheduleData;
                isEdit = true;
            }
        } else {
            // 添加新日程
            scheduleData.id = this.generateId();
            this.schedules.push(scheduleData);
        }

        try {
            await this.saveSchedulesToDisk();
            this.renderSchedules();
            this.updateEmptyState();
            this.closeModal();
            this.showToast(isEdit ? '日程已更新' : '日程已添加');
        } catch (error) {
            // 如果保存失败，回滚操作
            if (isEdit) {
                // 重新加载数据以回滚更改
                this.loadSchedulesFromDisk();
            } else {
                // 移除刚添加的日程
                this.schedules.pop();
            }
            this.renderSchedules();
            this.updateEmptyState();
        }
    }

    // 渲染日程列表
    renderSchedules() {
        if (this.currentView === 'calendar') {
            this.renderCalendar();
        } else if (this.currentView === 'summary') {
            this.renderSummary();
        } else {
            this.renderList();
        }
    }

    // 渲染列表视图
    renderList() {
        const container = document.getElementById('scheduleList');
        const filteredSchedules = this.getFilteredSchedules();

        container.innerHTML = '';

        filteredSchedules.forEach(schedule => {
            const scheduleElement = this.createScheduleElement(schedule);
            container.appendChild(scheduleElement);
        });
    }

    // 获取过滤后的日程
    getFilteredSchedules() {
        let filtered = [...this.schedules];

        // 按筛选条件过滤
        switch (this.currentFilter) {
            case 'pending':
                filtered = filtered.filter(s => !s.completed);
                break;
            case 'completed':
                filtered = filtered.filter(s => s.completed);
                break;
            case 'today':
                const today = new Date().toISOString().split('T')[0];
                filtered = filtered.filter(s => s.date === today);
                break;
        }

        // 按搜索关键词过滤
        const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
        if (searchTerm) {
            filtered = filtered.filter(s => 
                s.title.toLowerCase().includes(searchTerm) ||
                (s.description && s.description.toLowerCase().includes(searchTerm))
            );
        }

        // 排序：未完成的在前，然后按日期排序
        filtered.sort((a, b) => {
            if (a.completed !== b.completed) {
                return a.completed ? 1 : -1;
            }
            return new Date(a.date + ' ' + (a.time || '00:00')) - new Date(b.date + ' ' + (b.time || '00:00'));
        });

        return filtered;
    }

    // 创建日程元素
    createScheduleElement(schedule) {
        const div = document.createElement('div');
        const isWeekPlanTask = !!schedule.weekPlanId;
        div.className = `schedule-item ${schedule.completed ? 'completed' : ''} ${schedule.priority}-priority ${isWeekPlanTask ? 'week-plan-task' : ''}`;

        const datetime = this.formatDateTime(schedule.date, schedule.time);
        const isOverdue = this.isOverdue(schedule);

        // 构建操作按钮，周计划任务的编辑和删除给出提示
        let actionButtons = `
            <button class="action-btn complete-btn" onclick="scheduleManager.toggleComplete('${schedule.id}')">
                <i class="fas ${schedule.completed ? 'fa-undo' : 'fa-check'}"></i>
                ${schedule.completed ? '取消完成' : '标记完成'}
            </button>
        `;

        if (isWeekPlanTask) {
            actionButtons += `
                <button class="action-btn edit-btn disabled" onclick="scheduleManager.showWeekPlanTaskTip('${schedule.weekPlanTitle}')">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="action-btn delete-btn disabled" onclick="scheduleManager.showWeekPlanTaskTip('${schedule.weekPlanTitle}')">
                    <i class="fas fa-trash"></i> 删除
                </button>
            `;
        } else {
            actionButtons += `
                <button class="action-btn edit-btn" onclick="scheduleManager.editSchedule('${schedule.id}')">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="action-btn delete-btn" onclick="scheduleManager.deleteSchedule('${schedule.id}')">
                    <i class="fas fa-trash"></i> 删除
                </button>
            `;
        }

        div.innerHTML = `
            <div class="schedule-header">
                <div>
                    <h3 class="schedule-title ${schedule.completed ? 'completed' : ''}">${this.escapeHtml(schedule.title)}</h3>
                    <div class="schedule-meta">
                        <span><i class="fas fa-calendar"></i> ${datetime}</span>
                        <span><i class="fas fa-star"></i> ${schedule.score || 10}分</span>
                        ${isOverdue ? '<span style="color: #ef4444;"><i class="fas fa-exclamation-triangle"></i> 已逾期</span>' : ''}
                        ${isWeekPlanTask ? '<span style="color: #8b5cf6;"><i class="fas fa-tasks"></i> 周计划任务</span>' : ''}
                        <span class="category-tag category-${this.getCategoryColor(schedule.category)}">${this.getCategoryName(schedule.category)}</span>
                    </div>
                </div>
            </div>
            ${schedule.description ? `<div class="schedule-description">${this.escapeHtml(schedule.description)}</div>` : ''}
            <div class="schedule-actions">
                ${actionButtons}
            </div>
        `;

        return div;
    }

    // 显示周计划任务提示
    showWeekPlanTaskTip(weekPlanTitle) {
        this.showToast(`此任务来自周计划"${weekPlanTitle}"，请到周计划中进行编辑或删除`, 'info');
    }

    // 格式化日期时间
    formatDateTime(date, time) {
        const dateObj = dayjs(date).tz('Asia/Shanghai');
        let formatted = dateObj.format('M月D日 ddd');
        
        if (time) {
            formatted += ` ${time}`;
        }
        
        return formatted;
    }

    // 检查是否逾期
    isOverdue(schedule) {
        if (schedule.completed) return false;
        
        const now = dayjs().tz('Asia/Shanghai');
        const scheduleDateTime = schedule.time ? 
            dayjs(schedule.date + ' ' + schedule.time).tz('Asia/Shanghai') : 
            dayjs(schedule.date + ' 23:59').tz('Asia/Shanghai');
        
        return scheduleDateTime.isBefore(now);
    }

    // 获取分类名称
    getCategoryName(categoryId) {
        const category = this.categories.find(c => c.id === categoryId);
        return category ? category.name : '其他';
    }

    // 获取分类颜色
    getCategoryColor(categoryId) {
        const category = this.categories.find(c => c.id === categoryId);
        return category ? category.color : 'gray';
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 切换完成状态
    async toggleComplete(id) {
        const schedule = this.schedules.find(s => s.id === id);
        if (schedule) {
            const oldCompleted = schedule.completed;
            schedule.completed = !schedule.completed;
            
            try {
                await this.saveSchedulesToDisk();
                this.renderSchedules();
                this.showToast(schedule.completed ? '日程已完成' : '日程已标记为待完成');
            } catch (error) {
                // 如果保存失败，回滚状态
                schedule.completed = oldCompleted;
                this.renderSchedules();
            }
        }
    }

    // 编辑日程
    editSchedule(id) {
        const schedule = this.schedules.find(s => s.id === id);
        if (schedule) {
            this.openModal(schedule);
        }
    }

    // 删除日程
    deleteSchedule(id) {
        this.editingId = id;
        const deleteModal = document.getElementById('deleteModal');
        deleteModal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    // 关闭删除模态框
    closeDeleteModal() {
        const deleteModal = document.getElementById('deleteModal');
        deleteModal.classList.remove('active');
        document.body.style.overflow = 'auto';
        this.editingId = null;
    }

    // 确认删除
    async confirmDelete() {
        if (this.editingId) {
            const index = this.schedules.findIndex(s => s.id === this.editingId);
            if (index !== -1) {
                const deletedSchedule = this.schedules.splice(index, 1)[0];
                
                try {
                    await this.saveSchedulesToDisk();
                    this.renderSchedules();
                    this.updateEmptyState();
                    this.showToast('日程已删除');
                } catch (error) {
                    // 如果保存失败，恢复删除的日程
                    this.schedules.splice(index, 0, deletedSchedule);
                    this.renderSchedules();
                    this.updateEmptyState();
                }
            }
        }
        this.closeDeleteModal();
    }

    // 处理搜索
    handleSearch(searchTerm) {
        this.renderSchedules();
        this.updateEmptyState();
    }

    // 处理筛选
    handleFilter(filter) {
        this.currentFilter = filter;
        
        this.renderSchedules();
        this.updateEmptyState();
    }

    // 更新空状态显示
    updateEmptyState() {
        const scheduleList = document.getElementById('scheduleList');
        const calendarView = document.getElementById('calendarView');
        const emptyState = document.getElementById('emptyState');
        
        // 只在列表视图时显示空状态
        if (this.currentView === 'list') {
            const filteredSchedules = this.getFilteredSchedules();

            if (filteredSchedules.length === 0) {
                scheduleList.style.display = 'none';
                emptyState.style.display = 'block';
                
                // 根据当前筛选条件更新空状态文本
                const emptyMessages = {
                    all: '暂无日程',
                    pending: '暂无待完成的日程',
                    completed: '暂无已完成的日程',
                    today: '今天暂无日程'
                };
                
                const searchTerm = document.getElementById('searchInput').value.trim();
                if (searchTerm) {
                    emptyState.querySelector('h3').textContent = '未找到匹配的日程';
                    emptyState.querySelector('p').textContent = `没有找到包含"${searchTerm}"的日程`;
                } else {
                    emptyState.querySelector('h3').textContent = emptyMessages[this.currentFilter];
                    emptyState.querySelector('p').textContent = '点击"添加日程"按钮创建您的第一个日程';
                }
            } else {
                scheduleList.style.display = 'grid';
                emptyState.style.display = 'none';
            }
        } else {
            // 日历视图时隐藏空状态
            emptyState.style.display = 'none';
        }
    }

    // 更新保存状态
    updateSaveStatus(message, type = 'default') {
        const saveStatus = document.getElementById('saveStatus');
        const saveStatusText = document.getElementById('saveStatusText');
        
        if (saveStatus && saveStatusText) {
            saveStatusText.textContent = message;
            
            // 移除所有状态类
            saveStatus.classList.remove('loading', 'success', 'error');
            
            // 添加当前状态类
            if (type !== 'default') {
                saveStatus.classList.add(type);
            }
            
            // 3秒后恢复默认状态
            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    saveStatusText.textContent = '就绪';
                    saveStatus.classList.remove('loading', 'success', 'error');
                }, 3000);
            }
        }
    }

    // 显示Toast通知
    showToast(message, type = 'success') {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');
        
        toastMessage.textContent = message;
        toast.className = `toast show ${type}`;
        
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    // 视图切换
    switchView(view) {
        this.currentView = view;
        
        // 隐藏所有视图
        document.getElementById('scheduleList').style.display = 'none';
        document.getElementById('calendarView').style.display = 'none';
        document.getElementById('summaryView').style.display = 'none';
        document.getElementById('weekPlanView').style.display = 'none';
        document.getElementById('emptyState').style.display = 'none';
        
        // 控制搜索框和筛选按钮的显示
        const searchBox = document.querySelector('.search-box');
        const filterButtons = document.querySelector('.filter-buttons');
        
        if (view === 'list') {
            // 列表视图：显示搜索框和筛选按钮
            if (searchBox) searchBox.style.display = 'block';
            if (filterButtons) filterButtons.style.display = 'flex';
        } else {
            // 其他视图：隐藏搜索框和筛选按钮
            if (searchBox) searchBox.style.display = 'none';
            if (filterButtons) filterButtons.style.display = 'none';
        }
        
        // 显示对应视图
        if (view === 'calendar') {
            document.getElementById('calendarView').style.display = 'block';
            this.renderCalendar();
        } else if (view === 'summary') {
            document.getElementById('summaryView').style.display = 'block';
            this.renderSummary();
        } else if (view === 'weekPlan') {
            document.getElementById('weekPlanView').style.display = 'block';
            this.renderWeekPlanCalendar();
        } else {
            // 默认列表视图
            document.getElementById('scheduleList').style.display = 'grid';
            this.renderList();
            this.updateEmptyState();
        }
    }

    // 渲染日历视图
    renderCalendar() {
        const calendarGrid = document.getElementById('calendarGrid');
        const currentMonth = document.getElementById('currentMonth');
        
        // 设置月份标题
        const currentDate = dayjs(this.currentDate).tz('Asia/Shanghai');
        currentMonth.textContent = currentDate.format('YYYY年M月');
        
        // 清空日历网格
        calendarGrid.innerHTML = '';
        
        // 添加星期标题
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        weekdays.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.className = 'calendar-day-header';
            dayHeader.textContent = day;
            calendarGrid.appendChild(dayHeader);
        });
        
        // 获取当月第一天和最后一天
        const firstDay = currentDate.startOf('month');
        const lastDay = currentDate.endOf('month');
        const today = dayjs().tz('Asia/Shanghai');
        
        // 获取第一周开始日期（显示上月末尾几天）
        const startDate = firstDay.startOf('week');
        
        // 生成6周的日期
        const totalDays = 42;
        for (let i = 0; i < totalDays; i++) {
            const currentCalendarDate = startDate.add(i, 'day');
            
            const dayElement = this.createCalendarDay(currentCalendarDate, today, firstDay, lastDay);
            calendarGrid.appendChild(dayElement);
        }
    }

    // 创建日历日期元素
    createCalendarDay(date, today, firstDay, lastDay) {
        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day';
        
        const dayNumber = date.date();
        const dateString = date.format('YYYY-MM-DD');
        const isCurrentMonth = date.isSameOrAfter(firstDay, 'day') && date.isSameOrBefore(lastDay, 'day');
        const isToday = date.isSame(today, 'day');
        
        // 设置基本样式
        if (!isCurrentMonth) {
            dayElement.classList.add('other-month');
        }
        if (isToday) {
            dayElement.classList.add('today');
        }
        
        // 获取当天的任务和分数
        const dayTasks = this.schedules.filter(schedule => schedule.date === dateString);
        const dayScore = this.calculateDayScore(dayTasks);
        
        if (dayTasks.length > 0) {
            dayElement.classList.add('has-tasks');
            
            // 根据分数设置颜色
            if (dayScore >= 80) {
                dayElement.classList.add('high-score');
            } else if (dayScore >= 60) {
                dayElement.classList.add('medium-score');
            } else {
                dayElement.classList.add('low-score');
            }
        }
        
        // 添加日期内容
        dayElement.innerHTML = `
            <div class="calendar-day-number">${dayNumber}</div>
            ${dayTasks.length > 0 ? `<div class="calendar-day-score">${dayScore}分</div>` : ''}
        `;
        
        // 添加点击事件（只对当月日期有效）
        if (isCurrentMonth) {
            dayElement.addEventListener('click', () => {
                this.showDayDetail(dateString, dayTasks);
            });
        }
        
        return dayElement;
    }

    // 计算当天分数
    calculateDayScore(dayTasks) {
        if (dayTasks.length === 0) return 0;
        
        const completedTasks = dayTasks.filter(task => task.completed);
        const totalScore = completedTasks.reduce((sum, task) => sum + (task.score || 10), 0);
        
        return Math.round(totalScore);
    }

    // 显示日期详情
    showDayDetail(dateString, dayTasks) {
        const modal = document.getElementById('dayDetailModal');
        const title = document.getElementById('dayDetailTitle');
        const totalScore = document.getElementById('dayTotalScore');
        const completionRate = document.getElementById('dayCompletionRate');
        const tasksList = document.getElementById('dayTasksList');
        
        // 设置标题
        const date = dayjs(dateString).tz('Asia/Shanghai');
        title.textContent = date.format('YYYY年M月D日 dddd');
        
        // 计算统计信息
        const completedTasks = dayTasks.filter(task => task.completed);
        const dayScore = this.calculateDayScore(dayTasks);
        const completion = dayTasks.length > 0 ? Math.round((completedTasks.length / dayTasks.length) * 100) : 0;
        
        totalScore.textContent = `${dayScore}分`;
        completionRate.textContent = `${completion}%`;
        
        // 渲染任务列表
        tasksList.innerHTML = '';
        if (dayTasks.length === 0) {
            tasksList.innerHTML = '<p style="text-align: center; color: #64748b; padding: 20px;">这一天没有安排任务</p>';
        } else {
            dayTasks.forEach(task => {
                const taskElement = this.createDayTaskElement(task);
                tasksList.appendChild(taskElement);
            });
        }
        
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }

    // 创建日期详情中的任务元素
    createDayTaskElement(task) {
        const taskElement = document.createElement('div');
        const isWeekPlanTask = !!task.weekPlanId;
        taskElement.className = `day-task-item ${task.completed ? 'completed' : ''} ${task.priority}-priority ${isWeekPlanTask ? 'week-plan-task' : ''}`;
        
        const time = task.time ? task.time : '全天';
        
        taskElement.innerHTML = `
            <div class="day-task-content">
                <div class="day-task-title ${task.completed ? 'completed' : ''}">${this.escapeHtml(task.title)}</div>
                <div class="day-task-meta">
                    <span><i class="fas fa-clock"></i> ${time}</span>
                    <span class="category-tag category-${this.getCategoryColor(task.category)}">${this.getCategoryName(task.category)}</span>
                    ${isWeekPlanTask ? '<span style="color: #8b5cf6;"><i class="fas fa-tasks"></i> 周计划</span>' : ''}
                    ${task.completed ? '<span style="color: #22c55e;"><i class="fas fa-check"></i> 已完成</span>' : '<span style="color: #f59e0b;"><i class="fas fa-clock"></i> 待完成</span>'}
                </div>
            </div>
            <div class="day-task-score">${task.score || 10}分</div>
        `;
        
        return taskElement;
    }

    // 关闭日期详情模态框
    closeDayDetailModal() {
        const modal = document.getElementById('dayDetailModal');
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
    }

    // 导航月份
    navigateMonth(direction) {
        this.currentDate = dayjs(this.currentDate).tz('Asia/Shanghai').add(direction, 'month').toDate();
        this.renderCalendar();
    }

    // 导航总结时间
    navigateSummary(direction) {
        if (this.summaryType === 'monthly') {
            // 月度总结：按月导航
            this.summaryDate = dayjs(this.summaryDate).tz('Asia/Shanghai').add(direction, 'month').toDate();
        } else {
            // 季度总结：按季度导航（3个月）
            this.summaryDate = dayjs(this.summaryDate).tz('Asia/Shanghai').add(direction, 'month').toDate();
        }
        this.renderSummary();
    }

    // 格式化日期为YYYY-MM-DD格式
    formatDate(date) {
        return dayjs(date).tz('Asia/Shanghai').format('YYYY-MM-DD');
    }

    // 判断两个日期是否为同一天
    isSameDay(date1, date2) {
        return dayjs(date1).tz('Asia/Shanghai').isSame(dayjs(date2).tz('Asia/Shanghai'), 'day');
    }

    // 渲染总结视图
    renderSummary() {
        if (this.summaryType === 'monthly') {
            this.renderMonthlySummary();
        } else {
            this.renderQuarterlySummary();
        }
    }

    // 切换总结类型
    switchSummaryType(type) {
        this.summaryType = type;
        
        // 更新按钮状态
        document.querySelectorAll('.summary-nav-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.type === type) {
                btn.classList.add('active');
            }
        });
        
        // 显示/隐藏对应的总结视图
        if (type === 'monthly') {
            document.getElementById('monthlySummary').style.display = 'block';
            document.getElementById('quarterlySummary').style.display = 'none';
        } else {
            document.getElementById('monthlySummary').style.display = 'none';
            document.getElementById('quarterlySummary').style.display = 'block';
        }
        
        this.renderSummary();
    }

    // 渲染月度总结
    renderMonthlySummary() {
        const year = this.summaryDate.getFullYear();
        const month = this.summaryDate.getMonth();
        
        // 设置标题
        document.getElementById('summaryMonthTitle').textContent = 
            this.summaryDate.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' });
        
        // 获取当月数据
        const monthData = this.getMonthData(year, month);
        
        // 更新统计卡片
        this.updateMonthlySummaryCards(monthData);
        
        // 渲染图表和分析
        this.renderMonthScoreChart(monthData);
        this.renderMonthlyCategoryStats(monthData);
        this.renderMonthlyPerformance(monthData);
    }

    // 获取月度数据
    getMonthData(year, month) {
        const monthTasks = this.schedules.filter(schedule => {
            const taskDate = dayjs(schedule.date).tz('Asia/Shanghai');
            return taskDate.year() === year && taskDate.month() === month;
        });

        const completedTasks = monthTasks.filter(task => task.completed);
        const totalTasks = monthTasks.length;
        const completionRate = totalTasks > 0 ? Math.round((completedTasks.length / totalTasks) * 100) : 0;
        const totalScore = completedTasks.reduce((sum, task) => sum + (task.score || 10), 0);
        
        // 计算每日分数
        const dailyScores = {};
        const daysInMonth = dayjs().year(year).month(month).endOf('month').date();
        
        for (let day = 1; day <= daysInMonth; day++) {
            const dateString = dayjs().year(year).month(month).date(day).format('YYYY-MM-DD');
            const dayTasks = completedTasks.filter(task => task.date === dateString);
            dailyScores[day] = dayTasks.reduce((sum, task) => sum + (task.score || 10), 0);
        }

        const avgScore = totalScore > 0 ? Math.round(totalScore / Object.values(dailyScores).filter(score => score > 0).length) : 0;
        const bestDay = Object.entries(dailyScores).reduce((best, [day, score]) => 
            score > best.score ? { day: parseInt(day), score } : best, { day: 0, score: 0 });

        return {
            totalTasks,
            completedTasks: completedTasks.length,
            completionRate,
            totalScore,
            avgScore,
            bestDay,
            dailyScores,
            monthTasks,
            completedTasksList: completedTasks
        };
    }

    // 更新月度统计卡片
    updateMonthlySummaryCards(data) {
        document.getElementById('monthTotalTasks').textContent = data.totalTasks;
        document.getElementById('monthCompletedTasks').textContent = data.completedTasks;
        document.getElementById('monthCompletionRate').textContent = `${data.completionRate}%`;
        document.getElementById('monthTotalScore').textContent = data.totalScore;
        document.getElementById('monthAvgScore').textContent = data.avgScore;
        document.getElementById('monthBestDay').textContent = data.bestDay.score > 0 ? 
            `${data.bestDay.day}日 (${data.bestDay.score}分)` : '-';
    }

    // 渲染月度分数趋势图
    renderMonthScoreChart(data) {
        const chartContainer = document.getElementById('monthScoreChart');
        chartContainer.innerHTML = '';

        const maxScore = Math.max(...Object.values(data.dailyScores));
        const chartHeight = 100;

        Object.entries(data.dailyScores).forEach(([day, score]) => {
            const bar = document.createElement('div');
            bar.className = 'score-bar';
            
            if (score >= 80) bar.classList.add('high');
            else if (score >= 60) bar.classList.add('medium');
            else if (score > 0) bar.classList.add('low');

            const height = maxScore > 0 ? (score / maxScore) * chartHeight : 5;
            bar.style.height = `${height}px`;

            const tooltip = document.createElement('div');
            tooltip.className = 'score-bar-tooltip';
            tooltip.textContent = `${day}日: ${score}分`;
            bar.appendChild(tooltip);

            chartContainer.appendChild(bar);
        });
    }

    // 渲染月度分类统计
    renderMonthlyCategoryStats(data) {
        const statsContainer = document.getElementById('monthCategoryStats');
        statsContainer.innerHTML = '';

        const categoryData = {};

        // 使用动态分类
        this.categories.forEach(category => {
            const categoryTasks = data.completedTasksList.filter(task => task.category === category.id);
            const categoryScore = categoryTasks.reduce((sum, task) => sum + (task.score || 10), 0);
            const percentage = data.totalScore > 0 ? Math.round((categoryScore / data.totalScore) * 100) : 0;

            categoryData[category.id] = {
                count: categoryTasks.length,
                score: categoryScore,
                percentage,
                color: category.color,
                name: category.name
            };
        });

        this.categories.forEach(category => {
            if (categoryData[category.id].count > 0) {
                const item = document.createElement('div');
                item.className = `category-stat-item ${category.color}`;
                item.innerHTML = `
                    <div class="category-stat-name">${category.name}</div>
                    <div class="category-stat-value">${categoryData[category.id].score}</div>
                    <div class="category-stat-percentage">${categoryData[category.id].percentage}% (${categoryData[category.id].count}个任务)</div>
                `;
                statsContainer.appendChild(item);
            }
        });
    }

    // 渲染月度表现分析
    renderMonthlyPerformance(data) {
        const performanceContainer = document.getElementById('monthPerformance');
        
        const activeDays = Object.values(data.dailyScores).filter(score => score > 0).length;
        const highScoreDays = Object.values(data.dailyScores).filter(score => score >= 80).length;
        const mediumScoreDays = Object.values(data.dailyScores).filter(score => score >= 60 && score < 80).length;
        const lowScoreDays = Object.values(data.dailyScores).filter(score => score > 0 && score < 60).length;

        const performanceLevel = data.avgScore >= 80 ? 'excellent' : 
                               data.avgScore >= 60 ? 'good' : 'average';
        const performanceText = {
            excellent: '优秀',
            good: '良好',
            average: '待改进'
        };

        performanceContainer.innerHTML = `
            <div class="performance-item">
                <span class="performance-label">活跃天数</span>
                <span class="performance-value">${activeDays} 天</span>
            </div>
            <div class="performance-item">
                <span class="performance-label">优秀天数 (≥80分)</span>
                <span class="performance-value">${highScoreDays} 天</span>
            </div>
            <div class="performance-item">
                <span class="performance-label">良好天数 (60-79分)</span>
                <span class="performance-value">${mediumScoreDays} 天</span>
            </div>
            <div class="performance-item">
                <span class="performance-label">待改进天数 (<60分)</span>
                <span class="performance-value">${lowScoreDays} 天</span>
            </div>
            <div class="performance-item">
                <span class="performance-label">整体表现</span>
                <span class="performance-badge ${performanceLevel}">${performanceText[performanceLevel]}</span>
            </div>
        `;
    }

    // 渲染季度总结
    renderQuarterlySummary() {
        const year = this.summaryDate.getFullYear();
        const quarter = Math.floor(this.summaryDate.getMonth() / 3);
        
        // 设置标题
        document.getElementById('summaryQuarterTitle').textContent = 
            `${year}年第${quarter + 1}季度`;
        
        // 获取季度数据
        const quarterData = this.getQuarterData(year, quarter);
        
        // 更新统计卡片
        this.updateQuarterlySummaryCards(quarterData);
        
        // 渲染分析
        this.renderQuarterComparison(quarterData);
        this.renderQuarterlyCategoryStats(quarterData);
        this.renderQuarterGoals(quarterData);
    }

    // 获取季度数据
    getQuarterData(year, quarter) {
        const startMonth = quarter * 3;
        const endMonth = startMonth + 3;
        
        const quarterTasks = this.schedules.filter(schedule => {
            const taskDate = dayjs(schedule.date).tz('Asia/Shanghai');
            return taskDate.year() === year && 
                   taskDate.month() >= startMonth && 
                   taskDate.month() < endMonth;
        });

        const completedTasks = quarterTasks.filter(task => task.completed);
        const totalTasks = quarterTasks.length;
        const completionRate = totalTasks > 0 ? Math.round((completedTasks.length / totalTasks) * 100) : 0;
        const totalScore = completedTasks.reduce((sum, task) => sum + (task.score || 10), 0);
        
        // 计算三个月的数据
        const monthsData = [];
        for (let i = 0; i < 3; i++) {
            const monthData = this.getMonthData(year, startMonth + i);
            monthsData.push({
                month: startMonth + i,
                ...monthData
            });
        }

        const avgScore = totalScore > 0 ? Math.round(totalScore / completedTasks.length) : 0;
        
        // 计算趋势
        const scores = monthsData.map(m => m.totalScore);
        let trend = 'stable';
        if (scores[2] > scores[0]) trend = 'up';
        else if (scores[2] < scores[0]) trend = 'down';

        return {
            totalTasks,
            completedTasks: completedTasks.length,
            completionRate,
            totalScore,
            avgScore,
            trend,
            monthsData,
            quarterTasks,
            completedTasksList: completedTasks
        };
    }

    // 更新季度统计卡片
    updateQuarterlySummaryCards(data) {
        document.getElementById('quarterTotalTasks').textContent = data.totalTasks;
        document.getElementById('quarterCompletedTasks').textContent = data.completedTasks;
        document.getElementById('quarterCompletionRate').textContent = `${data.completionRate}%`;
        document.getElementById('quarterTotalScore').textContent = data.totalScore;
        document.getElementById('quarterAvgScore').textContent = data.avgScore;
        
        const trendIcons = { up: '📈', down: '📉', stable: '➡️' };
        const trendTexts = { up: '上升', down: '下降', stable: '稳定' };
        document.getElementById('quarterTrend').textContent = 
            `${trendIcons[data.trend]} ${trendTexts[data.trend]}`;
    }

    // 渲染季度月份对比
    renderQuarterComparison(data) {
        const comparisonContainer = document.getElementById('quarterComparison');
        comparisonContainer.innerHTML = '';

        const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];

        data.monthsData.forEach((monthData, index) => {
            const item = document.createElement('div');
            item.className = 'month-comparison-item';
            
            let trendClass = 'stable';
            let trendText = '持平';
            if (index > 0) {
                const prevScore = data.monthsData[index - 1].totalScore;
                if (monthData.totalScore > prevScore) {
                    trendClass = 'up';
                    trendText = `+${monthData.totalScore - prevScore}`;
                } else if (monthData.totalScore < prevScore) {
                    trendClass = 'down';
                    trendText = `${monthData.totalScore - prevScore}`;
                }
            }

            item.innerHTML = `
                <div class="month-name">${monthNames[monthData.month]}</div>
                <div class="month-score">${monthData.totalScore}</div>
                <div class="month-trend ${trendClass}">${trendText}</div>
            `;
            
            comparisonContainer.appendChild(item);
        });
    }

    // 渲染季度分类统计
    renderQuarterlyCategoryStats(data) {
        const statsContainer = document.getElementById('quarterCategoryStats');
        statsContainer.innerHTML = '';

        const categoryData = {};

        // 使用动态分类
        this.categories.forEach(category => {
            const categoryTasks = data.completedTasksList.filter(task => task.category === category.id);
            const categoryScore = categoryTasks.reduce((sum, task) => sum + (task.score || 10), 0);
            const percentage = data.totalScore > 0 ? Math.round((categoryScore / data.totalScore) * 100) : 0;

            categoryData[category.id] = {
                count: categoryTasks.length,
                score: categoryScore,
                percentage,
                color: category.color,
                name: category.name
            };
        });

        this.categories.forEach(category => {
            if (categoryData[category.id].count > 0) {
                const item = document.createElement('div');
                item.className = `category-stat-item ${category.color}`;
                item.innerHTML = `
                    <div class="category-stat-name">${category.name}</div>
                    <div class="category-stat-value">${categoryData[category.id].score}</div>
                    <div class="category-stat-percentage">${categoryData[category.id].percentage}% (${categoryData[category.id].count}个任务)</div>
                `;
                statsContainer.appendChild(item);
            }
        });
    }

    // 渲染季度目标
    renderQuarterGoals(data) {
        const goalsContainer = document.getElementById('quarterGoals');
        
        const goals = [
            {
                description: '任务完成率目标 (≥80%)',
                target: 80,
                actual: data.completionRate
            },
            {
                description: '平均分数目标 (≥70分)',
                target: 70,
                actual: data.avgScore
            },
            {
                description: '总分数目标 (≥1800分)',
                target: 1800,
                actual: data.totalScore
            }
        ];

        goalsContainer.innerHTML = '';
        
        goals.forEach(goal => {
            const progress = Math.min((goal.actual / goal.target) * 100, 100);
            const item = document.createElement('div');
            item.className = 'goal-item';
            
            item.innerHTML = `
                <div class="goal-description">${goal.description}</div>
                <div class="goal-progress">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                    <span class="progress-text">${Math.round(progress)}%</span>
                </div>
            `;
            
            goalsContainer.appendChild(item);
        });
    }

    // 分类管理方法
    openCategoryModal() {
        const modal = document.getElementById('categoryModal');
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
        this.renderCategoryList();
        this.resetCategoryForm();
    }

    closeCategoryModal() {
        const modal = document.getElementById('categoryModal');
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
        this.resetCategoryForm();
    }

    // 渲染分类列表
    renderCategoryList() {
        const container = document.getElementById('categoryListContent');
        container.innerHTML = '';

        if (this.categories.length === 0) {
            container.innerHTML = '<div class="category-empty">暂无分类</div>';
            return;
        }

        this.categories.forEach(category => {
            const categoryElement = this.createCategoryElement(category);
            container.appendChild(categoryElement);
        });
    }

    // 创建分类元素
    createCategoryElement(category) {
        const div = document.createElement('div');
        div.className = 'category-item';
        div.innerHTML = `
            <div class="category-item-info">
                <div class="category-color-indicator ${category.color}"></div>
                <div class="category-item-name">${this.escapeHtml(category.name)}</div>
            </div>
            <div class="category-item-actions">
                <button class="category-item-btn edit" onclick="scheduleManager.editCategory('${category.id}')">
                    <i class="fas fa-edit"></i> 编辑
                </button>
                <button class="category-item-btn delete" onclick="scheduleManager.deleteCategory('${category.id}')" 
                        ${category.isDefault ? 'disabled' : ''}>
                    <i class="fas fa-trash"></i> 删除
                </button>
            </div>
        `;
        return div;
    }

    // 处理分类表单提交
    async handleCategoryFormSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const categoryData = {
            name: formData.get('name').trim(),
            color: formData.get('color'),
            isDefault: false
        };

        if (!categoryData.name) {
            this.showToast('请输入分类名称', 'error');
            return;
        }

        // 检查分类名称是否已存在
        const existingCategory = this.categories.find(c => 
            c.name === categoryData.name && c.id !== this.editingCategoryId
        );
        
        if (existingCategory) {
            this.showToast('分类名称已存在', 'error');
            return;
        }

        try {
            if (this.editingCategoryId) {
                // 编辑现有分类
                const index = this.categories.findIndex(c => c.id === this.editingCategoryId);
                if (index !== -1) {
                    this.categories[index] = {
                        ...this.categories[index],
                        ...categoryData
                    };
                    this.showToast('分类已更新');
                }
            } else {
                // 添加新分类
                categoryData.id = this.generateId();
                this.categories.push(categoryData);
                this.showToast('分类已添加');
            }

            // 保存到磁盘
            await this.saveSchedulesToDisk();
            
            this.renderCategoryList();
            this.updateCategoryOptions();
            this.resetCategoryForm();
            this.renderSchedules(); // 重新渲染以更新分类显示
            
        } catch (error) {
            this.showToast('保存失败: ' + error.message, 'error');
        }
    }

    // 编辑分类
    editCategory(categoryId) {
        const category = this.categories.find(c => c.id === categoryId);
        if (!category) return;

        this.editingCategoryId = categoryId;
        document.getElementById('categoryName').value = category.name;
        document.getElementById('categoryColor').value = category.color;
        
        // 更新表单标题和按钮
        document.querySelector('.category-form h3').textContent = '编辑分类';
        document.querySelector('#categoryForm button[type="submit"]').textContent = '更新';
    }

    // 删除分类
    async deleteCategory(categoryId) {
        const category = this.categories.find(c => c.id === categoryId);
        if (!category) return;

        if (category.isDefault) {
            this.showToast('无法删除默认分类', 'error');
            return;
        }

        // 检查是否有日程使用此分类
        const usedBySchedules = this.schedules.some(s => s.category === categoryId);
        if (usedBySchedules) {
            const confirmed = confirm(`分类"${category.name}"正在被使用，删除后相关日程将被移动到"其他"分类。确定要删除吗？`);
            if (!confirmed) return;

            // 将使用此分类的日程移动到"其他"分类
            this.schedules.forEach(schedule => {
                if (schedule.category === categoryId) {
                    schedule.category = 'other';
                }
            });
        }

        try {
            // 删除分类
            this.categories = this.categories.filter(c => c.id !== categoryId);
            
            // 保存到磁盘
            await this.saveSchedulesToDisk();
            
            this.renderCategoryList();
            this.updateCategoryOptions();
            this.renderSchedules(); // 重新渲染以更新分类显示
            this.showToast('分类已删除');
            
        } catch (error) {
            this.showToast('删除失败: ' + error.message, 'error');
        }
    }

    // 重置分类表单
    resetCategoryForm() {
        this.editingCategoryId = null;
        document.getElementById('categoryForm').reset();
        document.querySelector('.category-form h3').textContent = '添加新分类';
        document.querySelector('#categoryForm button[type="submit"]').textContent = '保存';
    }

    // 更新分类选项
    updateCategoryOptions() {
        const categorySelect = document.getElementById('eventCategory');
        if (!categorySelect) return;

        categorySelect.innerHTML = '';
        this.categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            categorySelect.appendChild(option);
        });
    }
    
    // ===== 周计划功能 =====
    
    // 获取周范围（周一到周日）
    getWeekRange(date) {
        const day = dayjs(date).tz('Asia/Shanghai');
        
        // 计算周一（因为已经设置weekStart为1，所以startOf('week')就是周一）
        const monday = day.startOf('week');
        
        // 计算周日
        const sunday = monday.add(6, 'day');
        
        return {
            start: monday.format('YYYY-MM-DD'),
            end: sunday.format('YYYY-MM-DD'),
            weekNumber: monday.week(),
            year: monday.year()
        };
    }
    
    // 获取周数
    getWeekNumber(date) {
        return dayjs(date).tz('Asia/Shanghai').week();
    }
    
    // 格式化周范围显示
    formatWeekRange(weekRange) {
        const startDate = dayjs(weekRange.start).tz('Asia/Shanghai');
        const endDate = dayjs(weekRange.end).tz('Asia/Shanghai');
        return `${weekRange.year}年第${weekRange.weekNumber}周 (${startDate.format('MM-DD')} - ${endDate.format('MM-DD')})`;
    }
    
    // 判断两个日期是否在同一周
    isSameWeek(date1, date2) {
        return dayjs(date1).tz('Asia/Shanghai').isSame(dayjs(date2).tz('Asia/Shanghai'), 'week');
    }
    
    // 获取周计划ID
    getWeekId(date) {
        const day = dayjs(date).tz('Asia/Shanghai');
        return `${day.year()}-W${day.week()}`;
    }
    
    // 渲染周计划日历
    renderWeekPlanCalendar() {
        const calendarGrid = document.getElementById('weekCalendarGrid');
        const currentMonth = document.getElementById('currentWeekMonth');
        
        // 设置月份标题
        const currentDate = dayjs(this.currentWeekDate).tz('Asia/Shanghai');
        currentMonth.textContent = currentDate.format('YYYY年M月');
        
        // 清空日历网格
        calendarGrid.innerHTML = '';
        
        // 添加星期标题
        const weekdays = ['一', '二', '三', '四', '五', '六', '日'];
        weekdays.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.className = 'week-calendar-day-header';
            dayHeader.textContent = `周${day}`;
            calendarGrid.appendChild(dayHeader);
        });
        
        // 获取当月第一天和最后一天
        const firstDay = currentDate.startOf('month');
        const lastDay = currentDate.endOf('month');
        const today = dayjs().tz('Asia/Shanghai');
        
        // 获取第一周开始日期（调整为周一开始）
        const startDate = firstDay.startOf('week');
        
        // 生成6周的日期（42天）
        const totalDays = 42;
        for (let i = 0; i < totalDays; i++) {
            const currentCalendarDate = startDate.add(i, 'day');
            
            const dayElement = this.createWeekCalendarDay(currentCalendarDate, today, firstDay, lastDay);
            calendarGrid.appendChild(dayElement);
        }
    }
    
    // 创建周日历日期元素
    createWeekCalendarDay(date, today, firstDay, lastDay) {
        const dayElement = document.createElement('div');
        dayElement.className = 'week-calendar-day';
        
        const dayNumber = date.date();
        const dateString = date.format('YYYY-MM-DD');
        const isCurrentMonth = date.isSameOrAfter(firstDay, 'day') && date.isSameOrBefore(lastDay, 'day');
        const isToday = date.isSame(today, 'day');
        
        // 设置基本样式
        if (!isCurrentMonth) {
            dayElement.classList.add('other-month');
        }
        if (isToday) {
            dayElement.classList.add('today');
        }
        
        // 检查是否有周计划
        const weekId = this.getWeekId(date);
        const hasWeekPlan = this.weekPlans.some(plan => plan.weekId === weekId);
        if (hasWeekPlan) {
            dayElement.classList.add('has-week-plan');
        }
        
        dayElement.textContent = dayNumber;
        dayElement.dataset.date = dateString;
        
        // 添加事件监听
        dayElement.addEventListener('mouseenter', () => this.highlightWeek(date, true));
        dayElement.addEventListener('mouseleave', () => this.highlightWeek(date, false));
        dayElement.addEventListener('click', () => this.selectWeek(date));
        
        return dayElement;
    }
    
    // 高亮周
    highlightWeek(date, highlight) {
        const weekRange = this.getWeekRange(date);
        const allDays = document.querySelectorAll('.week-calendar-day');
        
        allDays.forEach(dayElement => {
            const dayDate = dayElement.dataset.date;
            if (dayDate >= weekRange.start && dayDate <= weekRange.end) {
                if (highlight) {
                    dayElement.classList.add('week-hover');
                } else {
                    dayElement.classList.remove('week-hover');
                }
            }
        });
    }
    
    // 选择周
    selectWeek(date) {
        const weekRange = this.getWeekRange(date);
        
        // 清除之前的选择
        document.querySelectorAll('.week-calendar-day').forEach(el => {
            el.classList.remove('selected-week');
        });
        
        // 高亮选中的周
        const allDays = document.querySelectorAll('.week-calendar-day');
        allDays.forEach(dayElement => {
            const dayDate = dayElement.dataset.date;
            if (dayDate >= weekRange.start && dayDate <= weekRange.end) {
                dayElement.classList.add('selected-week');
            }
        });
        
        this.selectedWeekRange = weekRange;
        this.openWeekPlanModal(weekRange);
    }
    
    // 导航周月份
    navigateWeekMonth(direction) {
        this.currentWeekDate = dayjs(this.currentWeekDate).tz('Asia/Shanghai').add(direction, 'month').toDate();
        this.renderWeekPlanCalendar();
    }
    
    // 打开周计划模态框
    openWeekPlanModal(weekRange) {
        const modal = document.getElementById('weekPlanModal');
        const modalTitle = document.getElementById('weekPlanModalTitle');
        const dateRangeElement = document.getElementById('weekPlanDateRange');
        const dateRangeInput = document.getElementById('weekPlanDateRangeInput');
        
        // 检查是否已有周计划
        const weekId = this.getWeekId(dayjs(weekRange.start).tz('Asia/Shanghai'));
        const existingPlan = this.weekPlans.find(plan => plan.weekId === weekId);
        
        if (existingPlan) {
            modalTitle.textContent = '编辑周计划';
            this.editingWeekPlanId = existingPlan.id;
            this.fillWeekPlanForm(existingPlan);
        } else {
            modalTitle.textContent = '创建周计划';
            this.editingWeekPlanId = null;
            this.resetWeekPlanForm();
        }
        
        dateRangeElement.textContent = this.formatWeekRange(weekRange);
        // 设置起止日期输入框的值
        const startDate = dayjs(weekRange.start).tz('Asia/Shanghai');
        const endDate = dayjs(weekRange.end).tz('Asia/Shanghai');
        const startFormatted = startDate.format('YYYY-MM-DD');
        const endFormatted = endDate.format('YYYY-MM-DD');
        dateRangeInput.value = `${startFormatted} 至 ${endFormatted}`;
        
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
    
    // 关闭周计划模态框
    closeWeekPlanModal() {
        const modal = document.getElementById('weekPlanModal');
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
        this.editingWeekPlanId = null;
        this.selectedWeekRange = null;
        this.renderWeekPlanCalendar();
    }

    // 重置周计划表单
    resetWeekPlanForm() {
        document.getElementById('weekPlanForm').reset();
        this.renderWeekTasks([]);
    }

    // 填充周计划表单
    fillWeekPlanForm(weekPlan) {
        document.getElementById('weekPlanTitle').value = weekPlan.title || '';
        document.getElementById('weekPlanDescription').value = weekPlan.description || '';
        this.renderWeekTasks(weekPlan.tasks || []);
    }

    // 渲染周任务列表
    renderWeekTasks(tasks) {
        const tasksBody = document.getElementById('weekPlanTasksBody');
        tasksBody.innerHTML = '';
        
        if (tasks.length === 0) {
            const emptyRow = document.createElement('div');
            emptyRow.className = 'week-task-empty';
            emptyRow.textContent = '暂无任务，点击"添加任务"开始添加';
            tasksBody.appendChild(emptyRow);
        } else {
            tasks.forEach((task, index) => {
                const taskRow = this.createWeekTaskRow(task, index);
                tasksBody.appendChild(taskRow);
            });
        }
    }

    // 创建周任务行
    createWeekTaskRow(task, index) {
        const row = document.createElement('div');
        row.className = 'week-task-row';
        row.dataset.index = index;
        
        // 获取当前周的日期范围，用于设置日期选择器的范围
        const weekStart = this.selectedWeekRange ? this.selectedWeekRange.start : '';
        const weekEnd = this.selectedWeekRange ? this.selectedWeekRange.end : '';
        
        row.innerHTML = `
            <div class="week-task-cell" data-label="日期">
                <input type="date" min="${weekStart}" max="${weekEnd}" value="${task.date || ''}" />
            </div>
            <div class="week-task-cell" data-label="分类">
                <select>
                    ${this.categories.map(cat => 
                        `<option value="${cat.id}" ${task.category === cat.id ? 'selected' : ''}>${cat.name}</option>`
                    ).join('')}
                </select>
            </div>
            <div class="week-task-cell" data-label="内容">
                <textarea placeholder="请输入任务内容">${task.content || ''}</textarea>
            </div>
            <div class="week-task-cell" data-label="分数">
                <input type="number" min="0" max="100" value="${task.score || 10}" />
            </div>
            <div class="week-task-cell" data-label="操作">
                <div class="task-actions">
                    <button type="button" class="task-action-btn delete" onclick="scheduleManager.removeWeekTask(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        
        return row;
    }

    // 添加周任务
    addWeekTask() {
        const tasksBody = document.getElementById('weekPlanTasksBody');
        const emptyState = tasksBody.querySelector('.week-task-empty');
        
        if (emptyState) {
            emptyState.remove();
        }
        
        const newTask = {
            date: '',
            category: this.categories[0]?.id || 'other',
            content: '',
            score: 10
        };
        
        const currentTasks = this.getCurrentWeekTasks();
        currentTasks.push(newTask);
        
        this.renderWeekTasks(currentTasks);
    }

    // 移除周任务
    removeWeekTask(index) {
        const currentTasks = this.getCurrentWeekTasks();
        currentTasks.splice(index, 1);
        this.renderWeekTasks(currentTasks);
    }

    // 获取当前表单中的周任务
    getCurrentWeekTasks() {
        const tasks = [];
        const taskRows = document.querySelectorAll('.week-task-row');
        
        taskRows.forEach(row => {
            const cells = row.querySelectorAll('.week-task-cell');
            const task = {
                date: cells[0].querySelector('input').value,
                category: cells[1].querySelector('select').value,
                content: cells[2].querySelector('textarea').value,
                score: parseInt(cells[3].querySelector('input').value) || 10
            };
            tasks.push(task);
        });
        
        return tasks;
    }

    // 保存周计划
    async saveWeekPlan() {
        if (!this.selectedWeekRange) {
            this.showToast('请先选择一周', 'error');
            return;
        }
        
        const formData = new FormData(document.getElementById('weekPlanForm'));
        const tasks = this.getCurrentWeekTasks();
        
        const weekPlanData = {
            title: formData.get('title').trim(),
            description: formData.get('description').trim(),
            weekId: this.getWeekId(dayjs(this.selectedWeekRange.start).tz('Asia/Shanghai')),
            weekRange: this.selectedWeekRange,
            tasks: tasks,
            createdAt: dayjs().tz('Asia/Shanghai').toISOString()
        };
        
        if (!weekPlanData.title) {
            this.showToast('请填写周计划标题', 'error');
            return;
        }
        
        let isEdit = false;
        let previousTasks = [];
        
        if (this.editingWeekPlanId) {
            // 编辑现有周计划
            const index = this.weekPlans.findIndex(plan => plan.id === this.editingWeekPlanId);
            if (index !== -1) {
                previousTasks = this.weekPlans[index].tasks || [];
                weekPlanData.id = this.editingWeekPlanId;
                weekPlanData.createdAt = this.weekPlans[index].createdAt;
                this.weekPlans[index] = weekPlanData;
                isEdit = true;
            }
        } else {
            // 添加新周计划
            weekPlanData.id = this.generateId();
            this.weekPlans.push(weekPlanData);
        }
        
        // 如果是编辑模式，先删除之前从周计划创建的每日计划项
        if (isEdit && previousTasks.length > 0) {
            this.schedules = this.schedules.filter(schedule => {
                // 移除标记为来自周计划的任务
                return !(schedule.weekPlanId === this.editingWeekPlanId);
            });
        }
        
        // 将周计划任务添加到每日计划中
        this.addWeekTasksToSchedules(weekPlanData);
        
        try {
            await this.saveSchedulesToDisk();
            this.renderWeekPlanCalendar();
            this.closeWeekPlanModal();
            this.showToast(isEdit ? '周计划已更新，任务已同步到每日计划' : '周计划已创建，任务已添加到每日计划');
            
            // 如果当前在日历视图，刷新显示
            if (this.currentView === 'calendar') {
                this.renderCalendar();
            } else if (this.currentView === 'list') {
                this.renderList();
            }
        } catch (error) {
            // 如果保存失败，回滚操作
            if (isEdit) {
                this.loadSchedulesFromDisk();
            } else {
                this.weekPlans.pop();
            }
            this.renderWeekPlanCalendar();
        }
    }

    // 将周计划任务添加到每日计划中
    addWeekTasksToSchedules(weekPlan) {
        weekPlan.tasks.forEach(task => {
            // 只处理有日期和内容的任务
            if (task.date && task.content && task.content.trim()) {
                const scheduleData = {
                    id: this.generateId(),
                    title: task.content.trim(),
                    description: `来自周计划: ${weekPlan.title}`,
                    date: task.date,
                    time: '', // 周计划任务默认没有具体时间
                    priority: 'medium', // 默认中等优先级
                    category: task.category,
                    score: task.score || 10,
                    completed: false,
                    createdAt: dayjs().tz('Asia/Shanghai').toISOString(),
                    weekPlanId: weekPlan.id, // 标记这个任务来自哪个周计划
                    weekPlanTitle: weekPlan.title // 保存周计划标题便于识别
                };
                
                this.schedules.push(scheduleData);
            }
        });
    }
}

// 初始化应用
let scheduleManager;

// 供HTML中的密码验证通过后调用
function initializeScheduleManager() {
    scheduleManager = new ScheduleManager();
    
    // 全局快捷键
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + N: 添加新日程
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            scheduleManager.openModal();
        }
        
        // Ctrl/Cmd + F: 聚焦搜索框
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            document.getElementById('searchInput').focus();
        }
    });
} 