from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import json
import time

app = Flask(__name__)
# 允许跨域请求
CORS(app)

# 数据文件路径
DATA_FILE = 'schedules_data.json'
# 备份目录
BACKUP_DIR = 'backups'

# 确保备份目录存在
os.makedirs(BACKUP_DIR, exist_ok=True)

@app.route('/api/schedules', methods=['GET'])
def get_schedules():
    """获取所有日程数据"""
    try:
        if not os.path.exists(DATA_FILE):
            return jsonify([])
        
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return jsonify(data)
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/schedules', methods=['POST'])
def save_schedules():
    """保存日程数据"""
    try:
        data = request.get_json()
        
        # 先创建备份
        if os.path.exists(DATA_FILE):
            timestamp = time.strftime("%Y%m%d%H%M%S")
            backup_file = f"{BACKUP_DIR}/schedules_backup_{timestamp}.json"
            with open(DATA_FILE, 'r', encoding='utf-8') as src:
                with open(backup_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
        
        # 保存新数据
        with open(DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        return jsonify({"message": "数据保存成功", "timestamp": time.time()})
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({"status": "healthy", "timestamp": time.time()})

if __name__ == '__main__':
    print("磁盘存储服务启动中，监听端口5000...")
    app.run(host='0.0.0.0', port=5000, debug=True) 