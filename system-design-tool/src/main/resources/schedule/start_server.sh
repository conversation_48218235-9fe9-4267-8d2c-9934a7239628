#!/bin/bash

echo "=== 磁盘存储服务启动脚本 ==="
echo ""

# 检查是否安装了Python
if ! command -v /usr/bin/python3.6 &> /dev/null; then
    echo "错误: 未找到Python3.6，请先安装Python3.6"
    exit 1
fi

# 检查是否安装了pip
if ! command -v /usr/bin/pip3.6 &> /dev/null; then
    echo "错误: 未找到pip3.6，请先安装pip3.6"
    exit 1
fi

echo "1. 安装Python依赖..."
/usr/bin/pip3.6 install -r requirements.txt

echo ""
echo "2. 启动Flask Web服务（后台运行）..."
echo "服务地址: http://localhost:5000"
echo ""

# 后台启动Flask应用
nohup /usr/bin/python3.6 app.py > server.log 2>&1 &
SERVER_PID=$!

# 保存PID到文件
echo $SERVER_PID > server.pid

echo "✅ Flask服务已启动，PID: $SERVER_PID"
echo "📄 日志文件: server.log"
echo "🛑 停止服务: kill $SERVER_PID"
echo ""
echo "🌐 访问地址:"
echo "  - 主页: http://localhost:5000/"
echo "  - API: http://localhost:5000/api/schedules"
echo "  - 健康检查: http://localhost:5000/api/health"
echo ""
echo "查看日志: tail -f server.log"
echo "查看进程: ps aux | grep app.py"