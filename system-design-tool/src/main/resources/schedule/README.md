# Flask日程管理Web应用

## 快速开始

### 1. 启动服务
```bash
# 启动Flask Web服务
./start_server.sh

# 停止服务
./stop_server.sh
```

### 2. 访问方式
- **主页**: `http://localhost:5000/` → 直接访问日程管理页面
- **API**: `http://localhost:5000/api/schedules` → 数据接口
- **健康检查**: `http://localhost:5000/api/health` → 服务状态

### 3. 开放端口（外网访问）

#### CentOS/RHEL - firewalld
```bash
# 开放5000端口
sudo firewall-cmd --permanent --add-port=5000/tcp
sudo firewall-cmd --reload

# 检查端口状态
sudo firewall-cmd --list-ports
```

#### Ubuntu/Debian - ufw
```bash
# 开放5000端口
sudo ufw allow 5000/tcp

# 检查状态
sudo ufw status
```

#### 直接使用iptables
```bash
# 开放5000端口
sudo iptables -A INPUT -p tcp --dport 5000 -j ACCEPT

# 保存规则
sudo iptables-save > /etc/iptables/rules.v4
```

### 4. 内网穿透配置

#### frp配置示例
```ini
# frpc.ini
[schedule-web]
type = http
local_port = 5000
custom_domains = your-domain.com
```

#### ngrok配置示例
```bash
# 映射本地5000端口到公网
ngrok http 5000
```

## API接口说明

### 获取日程数据
```bash
curl -X GET http://localhost:5000/api/schedules
```

### 保存日程数据
```bash
curl -X POST http://localhost:5000/api/schedules \
  -H "Content-Type: application/json" \
  -d '[{"id": 1, "title": "测试日程", "date": "2024-01-01"}]'
```

### 健康检查
```bash
curl -X GET http://localhost:5000/api/health
```

## 文件结构

```
schedule/
├── app.py                    # Flask应用主文件
├── start_server.sh           # 启动脚本
├── stop_server.sh            # 停止脚本
├── requirements.txt          # Python依赖
├── server.log                # 运行日志
├── server.pid                # 进程ID文件
├── schedules_data.json       # 数据文件
└── backups/                  # 备份目录
    └── schedules_backup_*.json
```

## 配置说明

### 修改HTML文件路径
在 `app.py` 中修改：
```python
HTML_FILE = '/your/path/to/schedule-manager.html'
```

### 修改监听端口
在 `app.py` 中修改：
```python
PORT = 8080  # 改为你想要的端口
```

### 修改数据文件路径
在 `app.py` 中修改：
```python
DATA_FILE = '/your/path/to/schedules_data.json'
```

## 常见问题

### 1. 端口被占用
```bash
# 查看端口占用情况
lsof -i :5000

# 杀死占用端口的进程
kill -9 PID
```

### 2. 权限问题
```bash
# 给脚本添加执行权限
chmod +x start_server.sh
chmod +x stop_server.sh
```

### 3. 查看日志
```bash
# 实时查看日志
tail -f server.log

# 查看最近的日志
tail -100 server.log
```

### 4. 检查服务状态
```bash
# 检查进程是否运行
ps aux | grep app.py

# 检查端口是否监听
netstat -tlnp | grep :5000
```

## 生产环境部署

### 使用Gunicorn（推荐）
```bash
# 安装Gunicorn
pip3 install gunicorn

# 启动服务
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 使用systemd服务
```bash
# 创建服务文件
sudo nano /etc/systemd/system/schedule-web.service

# 启动服务
sudo systemctl enable schedule-web
sudo systemctl start schedule-web
``` 