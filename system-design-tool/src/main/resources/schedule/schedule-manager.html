<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日程管理器</title>
    <link rel="icon" href="favicon.svg" type="image/svg+xml">
    <link rel="icon" href="favicon.svg" type="image/x-icon">
    <link rel="apple-touch-icon" href="favicon.svg">
    <link rel="stylesheet" href="schedule-manager.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .auth-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
        }
        .auth-box {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            min-width: 350px;
        }
        .auth-title {
            font-size: 24px;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }
        .auth-subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
        }
        .auth-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 20px;
            box-sizing: border-box;
            outline: none;
            transition: border-color 0.3s ease;
        }
        .auth-input:focus {
            border-color: #667eea;
        }
        .auth-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }
        .auth-btn:hover {
            transform: translateY(-2px);
        }
        .auth-error {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 15px;
            display: none;
        }
        .main-app {
            display: none;
        }

    </style>
</head>
<body>
    <!-- 密码验证界面 -->
    <div id="authOverlay" class="auth-overlay">
        <div class="auth-box">
            <div class="auth-title">🔒 日程管理系统</div>
            <div class="auth-subtitle">请输入访问密码</div>
            <input type="password" id="passwordInput" class="auth-input" placeholder="请输入密码">
            <button id="loginBtn" class="auth-btn">登录</button>
            <div id="authError" class="auth-error">密码错误，请重试</div>
        </div>
    </div>

    <!-- 主应用界面 -->
    <div id="mainApp" class="main-app">
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <h1><i class="fas fa-calendar-alt"></i> 日程管理器</h1>
            <div class="header-actions">
                <div class="save-status" id="saveStatus">
                    <i class="fas fa-save"></i>
                    <span id="saveStatusText">就绪</span>
                </div>
                <button class="btn btn-success" id="categoryManagerBtn">
                    <i class="fas fa-tags"></i> 分类管理
                </button>
                <button class="btn btn-primary" id="addEventBtn">
                    <i class="fas fa-plus"></i> 添加日程
                </button>
            </div>
        </header>

        <!-- 视图切换和筛选 -->
        <div class="filter-section">
            <div class="view-toggle">
                <button class="view-btn active" id="listViewBtn" data-view="list">
                    <i class="fas fa-list"></i> 列表视图
                </button>
                <button class="view-btn" id="calendarViewBtn" data-view="calendar">
                    <i class="fas fa-calendar"></i> 日历视图
                </button>
                <button class="view-btn" id="summaryViewBtn" data-view="summary">
                    <i class="fas fa-chart-bar"></i> 数据总结
                </button>
                <button class="view-btn" id="weekPlanViewBtn" data-view="weekPlan">
                    <i class="fas fa-calendar-week"></i> 周计划
                </button>
            </div>
            <div class="search-box">
                <i class="fas fa-search"></i>
                <input type="text" id="searchInput" placeholder="搜索日程...">
            </div>
            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">全部</button>
                <button class="filter-btn" data-filter="pending">待完成</button>
                <button class="filter-btn" data-filter="completed">已完成</button>
                <button class="filter-btn" data-filter="today">今天</button>
            </div>
        </div>

        <!-- 日程列表视图 -->
        <div class="schedule-list" id="scheduleList">
            <!-- 日程项将通过JavaScript动态添加 -->
        </div>

        <!-- 日历视图 -->
        <div class="calendar-view" id="calendarView" style="display: none;">
            <div class="calendar-header">
                <button class="calendar-nav-btn" id="prevMonth">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <h2 id="currentMonth"></h2>
                <button class="calendar-nav-btn" id="nextMonth">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
            <div class="calendar-grid" id="calendarGrid">
                <!-- 日历网格将通过JavaScript动态生成 -->
            </div>
            <div class="calendar-legend">
                <div class="legend-item">
                    <div class="legend-color high-score"></div>
                    <span>80+ 分 (优秀)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color medium-score"></div>
                    <span>60-79 分 (良好)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color low-score"></div>
                    <span>< 60 分 (待改进)</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color no-data"></div>
                    <span>无数据</span>
                </div>
            </div>
        </div>

        <!-- 周计划视图 -->
        <div class="week-plan-view" id="weekPlanView" style="display: none;">
            <div class="week-plan-header">
                <h2>周计划管理</h2>
                <p>选择任意一周开始制定计划</p>
            </div>
            <div class="week-calendar-container">
                <div class="week-calendar-header">
                    <button class="calendar-nav-btn" id="prevWeekMonth">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <h3 id="currentWeekMonth"></h3>
                    <button class="calendar-nav-btn" id="nextWeekMonth">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
                <div class="week-calendar-grid" id="weekCalendarGrid">
                    <!-- 周日历网格将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>

        <!-- 数据总结视图 -->
        <div class="summary-view" id="summaryView" style="display: none;">
            <!-- 总结类型切换 -->
            <div class="summary-nav">
                <button class="summary-nav-btn active" data-type="monthly">
                    <i class="fas fa-calendar-month"></i> 月度总结
                </button>
                <button class="summary-nav-btn" data-type="quarterly">
                    <i class="fas fa-calendar-quarter"></i> 季度总结
                </button>
            </div>

            <!-- 月度总结 -->
            <div class="monthly-summary" id="monthlySummary">
                <div class="summary-header">
                    <button class="summary-nav-arrow" id="prevSummaryMonth">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <h2 id="summaryMonthTitle"></h2>
                    <button class="summary-nav-arrow" id="nextSummaryMonth">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="card-icon">📅</div>
                        <div class="card-content">
                            <div class="card-value" id="monthTotalTasks">0</div>
                            <div class="card-label">总任务数</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">✅</div>
                        <div class="card-content">
                            <div class="card-value" id="monthCompletedTasks">0</div>
                            <div class="card-label">已完成</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">📊</div>
                        <div class="card-content">
                            <div class="card-value" id="monthCompletionRate">0%</div>
                            <div class="card-label">完成率</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">⭐</div>
                        <div class="card-content">
                            <div class="card-value" id="monthTotalScore">0</div>
                            <div class="card-label">总得分</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">🎯</div>
                        <div class="card-content">
                            <div class="card-value" id="monthAvgScore">0</div>
                            <div class="card-label">日均分数</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">🔥</div>
                        <div class="card-content">
                            <div class="card-value" id="monthBestDay">-</div>
                            <div class="card-label">最佳日期</div>
                        </div>
                    </div>
                </div>

                <div class="summary-details">
                    <div class="detail-section">
                        <h3>📈 每日分数趋势</h3>
                        <div class="score-chart" id="monthScoreChart">
                            <!-- 分数趋势图 -->
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>📊 分类统计</h3>
                        <div class="category-stats" id="monthCategoryStats">
                            <!-- 分类统计 -->
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>🏆 表现分析</h3>
                        <div class="performance-analysis" id="monthPerformance">
                            <!-- 表现分析 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 季度总结 -->
            <div class="quarterly-summary" id="quarterlySummary" style="display: none;">
                <div class="summary-header">
                    <button class="summary-nav-arrow" id="prevSummaryQuarter">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <h2 id="summaryQuarterTitle"></h2>
                    <button class="summary-nav-arrow" id="nextSummaryQuarter">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="card-icon">📅</div>
                        <div class="card-content">
                            <div class="card-value" id="quarterTotalTasks">0</div>
                            <div class="card-label">总任务数</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">✅</div>
                        <div class="card-content">
                            <div class="card-value" id="quarterCompletedTasks">0</div>
                            <div class="card-label">已完成</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">📊</div>
                        <div class="card-content">
                            <div class="card-value" id="quarterCompletionRate">0%</div>
                            <div class="card-label">完成率</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">⭐</div>
                        <div class="card-content">
                            <div class="card-value" id="quarterTotalScore">0</div>
                            <div class="card-label">总得分</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">🎯</div>
                        <div class="card-content">
                            <div class="card-value" id="quarterAvgScore">0</div>
                            <div class="card-label">日均分数</div>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">📈</div>
                        <div class="card-content">
                            <div class="card-value" id="quarterTrend">-</div>
                            <div class="card-label">季度趋势</div>
                        </div>
                    </div>
                </div>

                <div class="summary-details">
                    <div class="detail-section">
                        <h3>📈 月度对比</h3>
                        <div class="quarter-comparison" id="quarterComparison">
                            <!-- 三个月对比 -->
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>📊 季度分类统计</h3>
                        <div class="category-stats" id="quarterCategoryStats">
                            <!-- 季度分类统计 -->
                        </div>
                    </div>

                    <div class="detail-section">
                        <h3>🎯 季度目标达成</h3>
                        <div class="quarter-goals" id="quarterGoals">
                            <!-- 季度目标分析 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="fas fa-calendar-times"></i>
            <h3>暂无日程</h3>
            <p>点击"添加日程"按钮创建您的第一个日程</p>
        </div>
    </div>

    <!-- 添加/编辑日程模态框 -->
    <div class="modal" id="eventModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">添加日程</h2>
                <button class="close-btn" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="eventForm">
                <div class="form-group">
                    <label for="eventTitle">标题 *</label>
                    <input type="text" id="eventTitle" name="title" required>
                </div>
                
                <div class="form-group">
                    <label for="eventDescription">描述</label>
                    <textarea id="eventDescription" name="description" rows="3"></textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="eventDate">日期 *</label>
                        <input type="date" id="eventDate" name="date" required>
                    </div>
                    <div class="form-group">
                        <label for="eventTime">时间</label>
                        <input type="time" id="eventTime" name="time">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="eventPriority">优先级</label>
                        <select id="eventPriority" name="priority">
                            <option value="low">低</option>
                            <option value="medium" selected>中</option>
                            <option value="high">高</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="eventCategory">分类</label>
                        <select id="eventCategory" name="category">
                            <option value="work">工作</option>
                            <option value="personal">个人</option>
                            <option value="study">学习</option>
                            <option value="health">健康</option>
                            <option value="other">其他</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="eventScore">任务分数 (0-100分)</label>
                    <input type="number" id="eventScore" name="score" min="0" max="100" value="10" step="1">
                    <small class="form-hint">每天所有任务的分数总和建议为100分</small>
                </div>
                
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="cancelBtn">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div class="modal" id="deleteModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>确认删除</h2>
                <button class="close-btn" id="closeDeleteModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要删除这个日程吗？此操作无法撤销。</p>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" id="cancelDeleteBtn">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">删除</button>
            </div>
        </div>
    </div>

    <!-- 分类管理模态框 -->
    <div class="modal" id="categoryModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>分类管理</h2>
                <button class="close-btn" id="closeCategoryModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="category-manager">
                    <div class="category-form">
                        <h3>添加新分类</h3>
                        <form id="categoryForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="categoryName">分类名称</label>
                                    <input type="text" id="categoryName" name="name" required maxlength="10">
                                </div>
                                <div class="form-group">
                                    <label for="categoryColor">分类颜色</label>
                                    <select id="categoryColor" name="color">
                                        <option value="blue">蓝色</option>
                                        <option value="green">绿色</option>
                                        <option value="red">红色</option>
                                        <option value="yellow">黄色</option>
                                        <option value="purple">紫色</option>
                                        <option value="orange">橙色</option>
                                        <option value="pink">粉色</option>
                                        <option value="gray">灰色</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-actions">
                                <button type="button" class="btn btn-secondary" id="cancelCategoryBtn">取消</button>
                                <button type="submit" class="btn btn-primary">保存</button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="category-list">
                        <h3>现有分类</h3>
                        <div id="categoryListContent">
                            <!-- 分类列表将通过JavaScript动态添加 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 日期详情模态框 -->
    <div class="modal" id="dayDetailModal">
        <div class="modal-content day-detail-content">
            <div class="modal-header">
                <h2 id="dayDetailTitle">日期详情</h2>
                <button class="close-btn" id="closeDayDetailModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="day-summary">
                    <div class="score-info">
                        <div class="total-score" id="dayTotalScore">0</div>
                        <div class="score-label">总分</div>
                    </div>
                    <div class="completion-info">
                        <div class="completion-rate" id="dayCompletionRate">0%</div>
                        <div class="completion-label">完成率</div>
                    </div>
                </div>
                <div class="day-tasks" id="dayTasksList">
                    <!-- 当天任务列表 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 周计划模态框 -->
    <div class="modal" id="weekPlanModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="weekPlanModalTitle">创建周计划</h2>
                <button class="close-btn" id="closeWeekPlanModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="week-plan-info">
                    <h3 id="weekPlanDateRange">2024年第35周 (2024-08-26 - 2024-09-01)</h3>
                    <form id="weekPlanForm">
                        <div class="form-row-split">
                            <div class="form-group">
                                <label for="weekPlanTitle">周计划标题 *</label>
                                <input type="text" id="weekPlanTitle" name="title" required placeholder="请输入周计划标题">
                            </div>
                            <div class="form-group">
                                <label for="weekPlanDateRange">起止日期 *</label>
                                <input type="text" id="weekPlanDateRangeInput" readonly placeholder="自动设置">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="weekPlanDescription">周计划描述</label>
                            <textarea id="weekPlanDescription" name="description" rows="3" placeholder="简要描述这周的主要目标"></textarea>
                        </div>
                    </form>
                </div>
                
                <div class="week-plan-tasks">
                    <div class="week-plan-tasks-header">
                        <h4>周计划任务列表</h4>
                        <button type="button" class="btn btn-primary" id="addWeekTaskBtn">
                            <i class="fas fa-plus"></i> 添加任务
                        </button>
                    </div>
                    <div class="week-plan-tasks-table">
                        <div class="week-plan-tasks-header-row">
                            <div class="task-header-cell">日期</div>
                            <div class="task-header-cell">分类</div>
                            <div class="task-header-cell">内容</div>
                            <div class="task-header-cell">分数</div>
                            <div class="task-header-cell">操作</div>
                        </div>
                        <div class="week-plan-tasks-body" id="weekPlanTasksBody">
                            <!-- 子任务列表 -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" id="cancelWeekPlanBtn">取消</button>
                <button type="button" class="btn btn-primary" id="saveWeekPlanBtn">保存周计划</button>
            </div>
        </div>
    </div>

    <!-- Toast 通知 -->
    <div class="toast" id="toast">
        <span id="toastMessage"></span>
    </div>

    </div> <!-- 结束 mainApp -->

    <!-- Day.js 日期处理库 -->
    <script src="https://cdn.bootcdn.net/ajax/libs/dayjs/1.11.10/dayjs.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/dayjs/1.11.10/plugin/weekOfYear.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/dayjs/1.11.10/plugin/timezone.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/dayjs/1.11.10/plugin/utc.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/dayjs/1.11.10/plugin/isSameOrBefore.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/dayjs/1.11.10/plugin/isSameOrAfter.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/dayjs/1.11.10/locale/zh-cn.min.js"></script>
    
    <script>
        // 配置 Day.js
        dayjs.extend(dayjs_plugin_weekOfYear);
        dayjs.extend(dayjs_plugin_timezone);
        dayjs.extend(dayjs_plugin_utc);
        dayjs.extend(dayjs_plugin_isSameOrBefore);
        dayjs.extend(dayjs_plugin_isSameOrAfter);
        
        // 设置默认时区为 GMT+8 和一周开始为周一
        dayjs.tz.setDefault('Asia/Shanghai');
        dayjs.locale('zh-cn');
    </script>
    
    <script src="schedule-manager.js"></script>
    <script>
        // 简单的密码验证逻辑
        const PASSWORD = 'xiaobiao.';
        let isAuthenticated = false;
        
        document.addEventListener('DOMContentLoaded', function() {
            const authOverlay = document.getElementById('authOverlay');
            const mainApp = document.getElementById('mainApp');
            const passwordInput = document.getElementById('passwordInput');
            const loginBtn = document.getElementById('loginBtn');
            const authError = document.getElementById('authError');
            
            // 检查是否已经登录
            if (sessionStorage.getItem('schedule_authenticated') === 'true') {
                showMainApp();
            }
            
            // 登录按钮点击事件
            loginBtn.addEventListener('click', function() {
                attemptLogin();
            });
            
            // 回车键登录
            passwordInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    attemptLogin();
                }
            });
            

            
            // 尝试登录
            function attemptLogin() {
                const password = passwordInput.value;
                
                if (password === PASSWORD) {
                    // 登录成功
                    isAuthenticated = true;
                    sessionStorage.setItem('schedule_authenticated', 'true');
                    showMainApp();
                } else {
                    // 登录失败
                    authError.style.display = 'block';
                    passwordInput.value = '';
                    passwordInput.focus();
                    
                    // 3秒后隐藏错误信息
                    setTimeout(function() {
                        authError.style.display = 'none';
                    }, 3000);
                }
            }
            
            // 显示主应用
            function showMainApp() {
                authOverlay.style.display = 'none';
                mainApp.style.display = 'block';
                isAuthenticated = true;
                
                // 初始化日程管理器
                initializeScheduleManager();
            }
            
            // 退出登录
            function logout() {
                sessionStorage.removeItem('schedule_authenticated');
                authOverlay.style.display = 'flex';
                mainApp.style.display = 'none';
                passwordInput.value = '';
                passwordInput.focus();
                isAuthenticated = false;
            }
            
            // 自动聚焦到密码输入框
            if (!isAuthenticated) {
                setTimeout(function() {
                    passwordInput.focus();
                }, 100);
            }
        });
    </script>
</body>
</html> 