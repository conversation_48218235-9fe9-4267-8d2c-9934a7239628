from flask import Flask, send_file, jsonify, request, send_from_directory, session, render_template_string
from flask_cors import CORS
import os
import json
import time
from pathlib import Path
from datetime import timedelta

app = Flask(__name__)
CORS(app)  # 允许跨域请求
app.secret_key = 'xiaobiao_schedule_manager_secret_key_2024'  # 用于session加密
app.permanent_session_lifetime = timedelta(days=30)  # session有效期30天

# 配置
HTML_FILE = '/amos/schedule/schedule-manager.html'
STATIC_DIR = '/amos/schedule'  # 静态文件目录
DATA_FILE = 'schedules_data.json'



# 合并后的财务管理系统配置
FINANCIAL_HTML_FILE = '/amos/financial-management/index.html'
FINANCIAL_STATIC_DIR = '/amos/financial-management'
FINANCIAL_PASSWORD = 'xiaobiao.'  # 财务系统访问密码
FINANCIAL_DATA_DIR = '/amos/financial-management/data'  # 财务数据目录
FINANCIAL_EXPENSES_FILE = '/amos/financial-management/data/expenses.json'
FINANCIAL_CURRENT_ASSETS_FILE = '/amos/financial-management/data/current_assets.json'
FINANCIAL_ASSET_HISTORY_FILE = '/amos/financial-management/data/asset_history.json'

# Augment账号管理系统配置
AUGMENT_HTML_FILE = '/amos/account/augment-accounts.html'
AUGMENT_STATIC_DIR = '/amos/account'
AUGMENT_DATA_FILE = '/amos/account/augment_accounts.json'

PORT = 5000

# 登录页面HTML模板
LOGIN_PAGE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>资产系统登录</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        .login-title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .login-subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
        }
        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .login-btn {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 15px;
            padding: 10px;
            background: rgba(231, 76, 60, 0.1);
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
        }
        .back-link {
            color: #667eea;
            text-decoration: none;
            font-size: 14px;
            margin-top: 20px;
            display: inline-block;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        .icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="icon">🔐</div>
        <h1 class="login-title">资产系统</h1>
        <p class="login-subtitle">请输入访问密码</p>
        
        <form method="POST">
            <div class="form-group">
                <label class="form-label" for="password">访问密码</label>
                <input type="password" id="password" name="password" class="form-input" placeholder="请输入密码" required>
            </div>
            <button type="submit" class="login-btn">进入系统</button>
        </form>
        
        {% if error %}
        <div class="error-message">
            ❌ {{ error }}
        </div>
        {% endif %}
        
        <a href="/" class="back-link">← 返回主页</a>
    </div>
</body>
</html>
'''

# 认证检查函数
def check_financial_auth():
    """检查财务管理系统认证状态"""
    return session.get('financial_authenticated', False)

def require_financial_auth():
    """装饰器：要求财务管理系统认证"""
    def decorator(f):
        def wrapper(*args, **kwargs):
            if not check_financial_auth():
                return render_template_string(LOGIN_PAGE), 401
            return f(*args, **kwargs)
        wrapper.__name__ = f.__name__
        return wrapper
    return decorator

@app.route('/')
def index():
    """根路径直接返回HTML页面"""
    try:
        return send_file(HTML_FILE)
    except Exception as e:
        return f"无法访问HTML文件: {str(e)}", 500

@app.route('/schedule')
def schedule():
    """备用路径访问HTML页面"""
    try:
        return send_file(HTML_FILE)
    except Exception as e:
        return f"无法访问HTML文件: {str(e)}", 500





# ===== 合并后的财务管理系统路由 =====
@app.route('/financial', methods=['GET', 'POST'])
def financial_management():
    """合并后的财务管理系统主页 - 支持密码认证"""
    if request.method == 'POST':
        # 处理登录
        password = request.form.get('password')
        if password == FINANCIAL_PASSWORD:
            session['financial_authenticated'] = True
            session.permanent = True
            print(f"[{time.strftime('%H:%M:%S')}] 财务管理系统认证成功")
            # 登录成功，重定向到财务管理页面
            try:
                return send_file(FINANCIAL_HTML_FILE)
            except Exception as e:
                return f"无法访问财务管理HTML文件: {str(e)}", 500
        else:
            print(f"[{time.strftime('%H:%M:%S')}] 财务管理系统认证失败")
            return render_template_string(LOGIN_PAGE, error="密码错误，请重试")

    # GET请求
    if check_financial_auth():
        # 已认证，直接显示财务管理页面
        try:
            return send_file(FINANCIAL_HTML_FILE)
        except Exception as e:
            return f"无法访问财务管理HTML文件: {str(e)}", 500
    else:
        # 未认证，显示登录页面
        return render_template_string(LOGIN_PAGE)

@app.route('/financial/logout')
def financial_logout():
    """财务管理系统登出"""
    session.pop('financial_authenticated', None)
    print(f"[{time.strftime('%H:%M:%S')}] 财务管理系统已登出")
    return render_template_string(LOGIN_PAGE, error="已安全登出")

@app.route('/financial/<path:filename>')
@require_financial_auth()
def financial_static_files(filename):
    """处理财务管理系统的静态文件请求（CSS、JS等）- 需要认证"""
    try:
        file_path = os.path.join(FINANCIAL_STATIC_DIR, filename)
        if os.path.exists(file_path):
            return send_from_directory(FINANCIAL_STATIC_DIR, filename)
        else:
            print(f"[{time.strftime('%H:%M:%S')}] 财务管理系统静态文件不存在: {file_path}")
            return f"文件不存在: {filename}", 404
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 财务管理系统静态文件访问错误: {str(e)}")
        return f"访问静态文件错误: {str(e)}", 500

# ===== 财务管理系统API路由 =====

def ensure_financial_data_dir():
    """确保财务数据目录存在"""
    os.makedirs(FINANCIAL_DATA_DIR, exist_ok=True)

@app.route('/api/financial/expenses', methods=['GET'])
@require_financial_auth()
def get_financial_expenses():
    """获取财务管理系统支出数据"""
    try:
        ensure_financial_data_dir()
        if not os.path.exists(FINANCIAL_EXPENSES_FILE):
            return jsonify([])

        with open(FINANCIAL_EXPENSES_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"[{time.strftime('%H:%M:%S')}] 读取财务管理支出数据: {len(data)} 条记录")
        return jsonify(data)

    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 读取财务管理支出数据失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/financial/expenses', methods=['POST'])
@require_financial_auth()
def save_financial_expenses():
    """保存财务管理系统支出数据"""
    try:
        ensure_financial_data_dir()
        data = request.get_json()

        if data is None:
            return jsonify({"error": "请求体为空"}), 400

        # 创建数据备份
        backup_dir = os.path.join(FINANCIAL_DATA_DIR, "backups")
        os.makedirs(backup_dir, exist_ok=True)

        if os.path.exists(FINANCIAL_EXPENSES_FILE):
            timestamp = time.strftime("%Y%m%d%H%M%S")
            backup_file = os.path.join(backup_dir, f"expenses_backup_{timestamp}.json")
            with open(FINANCIAL_EXPENSES_FILE, 'r', encoding='utf-8') as src:
                with open(backup_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())

        # 保存到磁盘
        with open(FINANCIAL_EXPENSES_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        print(f"[{time.strftime('%H:%M:%S')}] 财务管理支出数据已保存: {len(data)} 条记录")

        return jsonify({
            "status": "success",
            "message": "支出数据保存成功",
            "count": len(data)
        })

    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 保存财务管理支出数据失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/financial/current-assets', methods=['GET'])
@require_financial_auth()
def get_financial_current_assets():
    """获取财务管理系统当前资产数据"""
    try:
        ensure_financial_data_dir()
        if not os.path.exists(FINANCIAL_CURRENT_ASSETS_FILE):
            return jsonify({})

        with open(FINANCIAL_CURRENT_ASSETS_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"[{time.strftime('%H:%M:%S')}] 读取财务管理当前资产数据: {len(data)} 个渠道")
        return jsonify(data)

    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 读取财务管理当前资产数据失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/financial/current-assets', methods=['POST'])
@require_financial_auth()
def save_financial_current_assets():
    """保存财务管理系统当前资产数据"""
    try:
        ensure_financial_data_dir()
        data = request.get_json()

        if data is None:
            return jsonify({"error": "请求体为空"}), 400

        # 创建数据备份
        backup_dir = os.path.join(FINANCIAL_DATA_DIR, "backups")
        os.makedirs(backup_dir, exist_ok=True)

        if os.path.exists(FINANCIAL_CURRENT_ASSETS_FILE):
            timestamp = time.strftime("%Y%m%d%H%M%S")
            backup_file = os.path.join(backup_dir, f"current_assets_backup_{timestamp}.json")
            with open(FINANCIAL_CURRENT_ASSETS_FILE, 'r', encoding='utf-8') as src:
                with open(backup_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())

        # 保存到磁盘
        with open(FINANCIAL_CURRENT_ASSETS_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        print(f"[{time.strftime('%H:%M:%S')}] 财务管理当前资产数据已保存: {len(data)} 个渠道")

        return jsonify({
            "status": "success",
            "message": "当前资产数据保存成功",
            "channels": len(data)
        })

    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 保存财务管理当前资产数据失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/financial/asset-history', methods=['GET'])
@require_financial_auth()
def get_financial_asset_history():
    """获取财务管理系统资产历史数据"""
    try:
        ensure_financial_data_dir()
        if not os.path.exists(FINANCIAL_ASSET_HISTORY_FILE):
            return jsonify([])

        with open(FINANCIAL_ASSET_HISTORY_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)

        print(f"[{time.strftime('%H:%M:%S')}] 读取财务管理资产历史数据: {len(data)} 条记录")
        return jsonify(data)

    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 读取财务管理资产历史数据失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/financial/asset-history', methods=['POST'])
@require_financial_auth()
def save_financial_asset_history():
    """保存财务管理系统资产历史数据"""
    try:
        ensure_financial_data_dir()
        data = request.get_json()

        if data is None:
            return jsonify({"error": "请求体为空"}), 400

        # 创建数据备份
        backup_dir = os.path.join(FINANCIAL_DATA_DIR, "backups")
        os.makedirs(backup_dir, exist_ok=True)

        if os.path.exists(FINANCIAL_ASSET_HISTORY_FILE):
            timestamp = time.strftime("%Y%m%d%H%M%S")
            backup_file = os.path.join(backup_dir, f"asset_history_backup_{timestamp}.json")
            with open(FINANCIAL_ASSET_HISTORY_FILE, 'r', encoding='utf-8') as src:
                with open(backup_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())

        # 保存到磁盘
        with open(FINANCIAL_ASSET_HISTORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        print(f"[{time.strftime('%H:%M:%S')}] 财务管理资产历史数据已保存: {len(data)} 条记录")

        return jsonify({
            "status": "success",
            "message": "资产历史数据保存成功",
            "count": len(data)
        })

    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 保存财务管理资产历史数据失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

# ===== Augment账号管理系统路由 =====

@app.route('/augment')
def augment_accounts():
    """Augment账号管理系统主页"""
    try:
        return send_file(AUGMENT_HTML_FILE)
    except Exception as e:
        return f"无法访问Augment账号管理HTML文件: {str(e)}", 500

@app.route('/augment/<path:filename>')
def augment_static_files(filename):
    """处理Augment账号管理系统的静态文件请求（CSS、JS等）"""
    try:
        file_path = os.path.join(AUGMENT_STATIC_DIR, filename)
        if os.path.exists(file_path):
            return send_from_directory(AUGMENT_STATIC_DIR, filename)
        else:
            print(f"[{time.strftime('%H:%M:%S')}] Augment系统静态文件不存在: {file_path}")
            return f"文件不存在: {filename}", 404
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] Augment系统静态文件访问错误: {str(e)}")
        return f"访问静态文件错误: {str(e)}", 500

@app.route('/api/accounts', methods=['GET'])
def get_augment_accounts():
    """获取所有Augment账号信息"""
    try:
        if not os.path.exists(AUGMENT_DATA_FILE):
            return jsonify({
                "success": True,
                "message": "暂无账号数据",
                "accounts": []
            })

        with open(AUGMENT_DATA_FILE, 'r', encoding='utf-8') as f:
            accounts = json.load(f)

        print(f"[{time.strftime('%H:%M:%S')}] 读取Augment账号数据: {len(accounts)} 个账号")
        return jsonify({
            "success": True,
            "message": "获取账号列表成功",
            "count": len(accounts),
            "accounts": accounts
        })

    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 读取Augment账号数据失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"获取账号列表失败: {str(e)}",
            "accounts": []
        }), 500

@app.route('/api/accounts', methods=['POST'])
def save_augment_account():
    """保存Augment账号信息"""
    try:
        account_data = request.get_json()

        if account_data is None:
            return jsonify({
                "success": False,
                "message": "请求体为空"
            }), 400

        # 验证必填字段
        required_fields = ['email', 'token', 'quota']
        for field in required_fields:
            if field not in account_data or not account_data[field]:
                return jsonify({
                    "success": False,
                    "message": f"缺少必填字段: {field}"
                }), 400

        # 确保目录存在
        os.makedirs(os.path.dirname(AUGMENT_DATA_FILE), exist_ok=True)

        # 读取现有数据
        accounts = []
        if os.path.exists(AUGMENT_DATA_FILE):
            with open(AUGMENT_DATA_FILE, 'r', encoding='utf-8') as f:
                accounts = json.load(f)

        # 生成递增ID
        next_id = 1
        if accounts:
            max_id = max(acc.get('id', 0) for acc in accounts)
            next_id = max_id + 1

        # 添加新账号信息
        account_data['id'] = next_id
        account_data['createTime'] = time.strftime('%Y-%m-%d %H:%M:%S')
        account_data['status'] = 'active'

        accounts.append(account_data)

        # 创建备份
        if len(accounts) > 1:  # 如果不是第一次保存，创建备份
            backup_dir = os.path.join(AUGMENT_STATIC_DIR, "backups")
            os.makedirs(backup_dir, exist_ok=True)
            timestamp = time.strftime("%Y%m%d%H%M%S")
            backup_file = os.path.join(backup_dir, f"accounts_backup_{timestamp}.json")
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(accounts[:-1], f, ensure_ascii=False, indent=2)  # 备份之前的数据

        # 保存到磁盘
        with open(AUGMENT_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, ensure_ascii=False, indent=2)

        print(f"[{time.strftime('%H:%M:%S')}] Augment账号已保存: {account_data['email']}")

        return jsonify({
            "success": True,
            "message": "账号信息保存成功"
        })

    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 保存Augment账号失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"保存账号信息失败: {str(e)}"
        }), 500

@app.route('/api/accounts/<account_id>', methods=['PUT'])
def update_augment_account(account_id):
    """更新Augment账号信息"""
    try:
        if not os.path.exists(AUGMENT_DATA_FILE):
            return jsonify({
                "success": False,
                "message": "账号数据文件不存在"
            }), 404

        account_data = request.get_json()

        if account_data is None:
            return jsonify({
                "success": False,
                "message": "请求体为空"
            }), 400

        # 验证必填字段
        required_fields = ['email', 'token', 'quota', 'used']
        for field in required_fields:
            if field not in account_data or not account_data[field]:
                return jsonify({
                    "success": False,
                    "message": f"缺少必填字段: {field}"
                }), 400

        # 读取现有数据
        with open(AUGMENT_DATA_FILE, 'r', encoding='utf-8') as f:
            accounts = json.load(f)

        # 查找要更新的账号
        account_found = False
        for i, account in enumerate(accounts):
            if str(account.get('id')) == str(account_id):
                # 保留原有的ID、创建时间和状态
                account_data['id'] = account.get('id')
                account_data['createTime'] = account.get('createTime')
                account_data['status'] = account.get('status', 'active')

                # 更新账号信息
                accounts[i] = account_data
                account_found = True
                break

        if not account_found:
            return jsonify({
                "success": False,
                "message": "未找到指定的账号"
            }), 404

        # 创建备份
        backup_dir = os.path.join(AUGMENT_STATIC_DIR, "backups")
        os.makedirs(backup_dir, exist_ok=True)
        timestamp = time.strftime("%Y%m%d%H%M%S")
        backup_file = os.path.join(backup_dir, f"accounts_backup_before_update_{timestamp}.json")
        with open(backup_file, 'w', encoding='utf-8') as f:
            # 备份更新前的完整数据
            with open(AUGMENT_DATA_FILE, 'r', encoding='utf-8') as src:
                f.write(src.read())

        # 保存更新后的数据
        with open(AUGMENT_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, ensure_ascii=False, indent=2)

        print(f"[{time.strftime('%H:%M:%S')}] Augment账号已更新: ID {account_id}, 邮箱 {account_data['email']}")

        return jsonify({
            "success": True,
            "message": "账号更新成功"
        })

    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 更新Augment账号失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"更新账号失败: {str(e)}"
        }), 500

@app.route('/api/accounts/<account_id>', methods=['DELETE'])
def delete_augment_account(account_id):
    """删除Augment账号"""
    try:
        if not os.path.exists(AUGMENT_DATA_FILE):
            return jsonify({
                "success": False,
                "message": "账号数据文件不存在"
            }), 404

        # 读取现有数据
        with open(AUGMENT_DATA_FILE, 'r', encoding='utf-8') as f:
            accounts = json.load(f)

        # 查找并删除账号
        original_count = len(accounts)
        accounts = [acc for acc in accounts if str(acc.get('id')) != str(account_id)]

        if len(accounts) == original_count:
            return jsonify({
                "success": False,
                "message": "未找到指定的账号"
            }), 404

        # 创建备份
        backup_dir = os.path.join(AUGMENT_STATIC_DIR, "backups")
        os.makedirs(backup_dir, exist_ok=True)
        timestamp = time.strftime("%Y%m%d%H%M%S")
        backup_file = os.path.join(backup_dir, f"accounts_backup_before_delete_{timestamp}.json")
        with open(backup_file, 'w', encoding='utf-8') as f:
            # 备份删除前的完整数据
            with open(AUGMENT_DATA_FILE, 'r', encoding='utf-8') as src:
                f.write(src.read())

        # 保存更新后的数据
        with open(AUGMENT_DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump(accounts, f, ensure_ascii=False, indent=2)

        print(f"[{time.strftime('%H:%M:%S')}] Augment账号已删除: ID {account_id}")

        return jsonify({
            "success": True,
            "message": "账号删除成功"
        })

    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 删除Augment账号失败: {str(e)}")
        return jsonify({
            "success": False,
            "message": f"删除账号失败: {str(e)}"
        }), 500



@app.route('/api/schedules', methods=['GET'])
def get_schedules():
    """获取所有日程数据"""
    try:
        if not os.path.exists(DATA_FILE):
            # 返回空的新格式数据
            return jsonify({"schedules": [], "categories": []})
        
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 兼容旧格式和新格式
        if isinstance(data, list):
            # 旧格式：直接是数组
            print(f"[{time.strftime('%H:%M:%S')}] 读取旧格式数据 {len(data)} 条日程")
            return jsonify(data)
        elif isinstance(data, dict) and 'schedules' in data:
            # 新格式：包含schedules和categories
            schedules_count = len(data.get('schedules', []))
            categories_count = len(data.get('categories', []))
            print(f"[{time.strftime('%H:%M:%S')}] 读取新格式数据 {schedules_count} 条日程, {categories_count} 个分类")
            return jsonify(data)
        else:
            # 其他格式，返回空数据
            print(f"[{time.strftime('%H:%M:%S')}] 数据格式未知，返回空数据")
            return jsonify({"schedules": [], "categories": []})
            
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 读取数据失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/schedules', methods=['POST'])
def save_schedules():
    """保存日程数据"""
    try:
        data = request.get_json()
        
        if data is None:
            return jsonify({"error": "请求体为空"}), 400
        
        # 支持新格式（包含schedules和categories）和旧格式（直接是数组）
        if isinstance(data, dict) and 'schedules' in data and 'categories' in data:
            # 新格式：包含schedules和categories
            schedules_count = len(data.get('schedules', []))
            categories_count = len(data.get('categories', []))
            print(f"[{time.strftime('%H:%M:%S')}] 接收到新格式数据: {schedules_count} 条日程, {categories_count} 个分类")
            
            # 直接保存新格式数据
            with open(DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            return jsonify({
                "message": "数据保存成功", 
                "timestamp": time.time(),
                "schedules_count": schedules_count,
                "categories_count": categories_count
            })
            
        elif isinstance(data, list):
            # 旧格式：直接是数组
            print(f"[{time.strftime('%H:%M:%S')}] 接收到旧格式数据: {len(data)} 条日程")
            
            # 直接保存数组数据
            with open(DATA_FILE, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            return jsonify({
                "message": "数据保存成功", 
                "timestamp": time.time(),
                "count": len(data)
            })
        else:
            return jsonify({"error": "数据格式错误，应为数组或包含schedules和categories的对象"}), 400
        
        print(f"[{time.strftime('%H:%M:%S')}] 数据已保存到磁盘")
        
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 保存数据失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        # 检查数据文件状态
        file_exists = os.path.exists(DATA_FILE)
        file_size = os.path.getsize(DATA_FILE) if file_exists else 0
        
        # 检查HTML文件状态
        html_exists = os.path.exists(HTML_FILE)
        
        # 检查静态文件目录状态
        static_exists = os.path.exists(STATIC_DIR)
        


        # 检查合并财务管理系统文件状态
        financial_html_exists = os.path.exists(FINANCIAL_HTML_FILE)
        financial_static_exists = os.path.exists(FINANCIAL_STATIC_DIR)
        financial_data_dir_exists = os.path.exists(FINANCIAL_DATA_DIR)
        financial_expenses_exists = os.path.exists(FINANCIAL_EXPENSES_FILE)
        financial_assets_exists = os.path.exists(FINANCIAL_CURRENT_ASSETS_FILE)
        financial_history_exists = os.path.exists(FINANCIAL_ASSET_HISTORY_FILE)
        
        response = {
            "status": "healthy",
            "timestamp": time.time(),
            "schedule_system": {
                "data_file_exists": file_exists,
                "data_file_size": file_size,
                "html_file_exists": html_exists,
                "html_file_path": HTML_FILE,
                "static_dir_exists": static_exists,
                "static_dir_path": STATIC_DIR
            },

            "financial_system": {
                "html_file_exists": financial_html_exists,
                "html_file_path": FINANCIAL_HTML_FILE,
                "static_dir_exists": financial_static_exists,
                "static_dir_path": FINANCIAL_STATIC_DIR,
                "data_dir_exists": financial_data_dir_exists,
                "data_dir_path": FINANCIAL_DATA_DIR,
                "expenses_file_exists": financial_expenses_exists,
                "current_assets_file_exists": financial_assets_exists,
                "asset_history_file_exists": financial_history_exists
            },
            "server": "Flask Web服务器"
        }
        
        print(f"[{time.strftime('%H:%M:%S')}] 健康检查请求")
        return jsonify(response)
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 健康检查失败: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/<path:filename>')
def static_files(filename):
    """处理静态文件请求（CSS、JS、图片等）"""
    try:
        # 特殊处理：如果是account或argument，重定向到Augment账号管理
        if filename == 'account' or filename == 'argument':
            try:
                return send_file(AUGMENT_HTML_FILE)
            except Exception as e:
                return f"无法访问Augment账号管理HTML文件: {str(e)}", 500

        # 检查文件是否存在
        file_path = os.path.join(STATIC_DIR, filename)
        if os.path.exists(file_path):
            return send_from_directory(STATIC_DIR, filename)
        else:
            print(f"[{time.strftime('%H:%M:%S')}] 静态文件不存在: {file_path}")
            return f"文件不存在: {filename}", 404
    except Exception as e:
        print(f"[{time.strftime('%H:%M:%S')}] 静态文件访问错误: {str(e)}")
        return f"访问静态文件错误: {str(e)}", 500

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return jsonify({"error": "资源不存在"}), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    return jsonify({"error": "服务器内部错误"}), 500

if __name__ == '__main__':
    # 检查HTML文件是否存在
    if not os.path.exists(HTML_FILE):
        print(f"⚠️  警告：HTML文件不存在: {HTML_FILE}")
        print("请确保HTML文件路径正确")
    else:
        print(f"✅ 找到HTML文件: {HTML_FILE}")
    
    # 检查静态文件目录是否存在
    if not os.path.exists(STATIC_DIR):
        print(f"⚠️  警告：静态文件目录不存在: {STATIC_DIR}")
        print("请确保静态文件目录路径正确")
    else:
        print(f"✅ 找到静态文件目录: {STATIC_DIR}")
        # 列出静态文件
        try:
            files = os.listdir(STATIC_DIR)
            static_files = [f for f in files if f.endswith(('.css', '.js', '.png', '.jpg', '.ico'))]
            if static_files:
                print(f"📁 静态文件: {', '.join(static_files)}")
            else:
                print("📁 静态文件目录为空或无常见静态文件")
        except Exception as e:
            print(f"⚠️  无法读取静态文件目录: {e}")
    


    # 检查合并财务管理系统文件
    if not os.path.exists(FINANCIAL_HTML_FILE):
        print(f"⚠️  警告：合并财务管理系统HTML文件不存在: {FINANCIAL_HTML_FILE}")
    else:
        print(f"✅ 找到合并财务管理系统HTML文件: {FINANCIAL_HTML_FILE}")

    if not os.path.exists(FINANCIAL_STATIC_DIR):
        print(f"⚠️  警告：合并财务管理系统静态文件目录不存在: {FINANCIAL_STATIC_DIR}")
    else:
        print(f"✅ 找到合并财务管理系统静态文件目录: {FINANCIAL_STATIC_DIR}")
        try:
            files = os.listdir(FINANCIAL_STATIC_DIR)
            financial_static_files = [f for f in files if f.endswith(('.css', '.js', '.png', '.jpg', '.ico', '.svg'))]
            if financial_static_files:
                print(f"📁 合并财务管理系统静态文件: {', '.join(financial_static_files)}")
        except Exception as e:
            print(f"⚠️  无法读取合并财务管理系统静态文件目录: {e}")

    # 检查Augment账号管理系统文件
    if not os.path.exists(AUGMENT_HTML_FILE):
        print(f"⚠️  警告：Augment账号管理系统HTML文件不存在: {AUGMENT_HTML_FILE}")
    else:
        print(f"✅ 找到Augment账号管理系统HTML文件: {AUGMENT_HTML_FILE}")

    if not os.path.exists(AUGMENT_STATIC_DIR):
        print(f"⚠️  警告：Augment账号管理系统静态文件目录不存在: {AUGMENT_STATIC_DIR}")
    else:
        print(f"✅ 找到Augment账号管理系统静态文件目录: {AUGMENT_STATIC_DIR}")
        try:
            files = os.listdir(AUGMENT_STATIC_DIR)
            augment_static_files = [f for f in files if f.endswith(('.css', '.js', '.png', '.jpg', '.ico', '.svg', '.html'))]
            if augment_static_files:
                print(f"📁 Augment账号管理系统静态文件: {', '.join(augment_static_files)}")
        except Exception as e:
            print(f"⚠️  无法读取Augment账号管理系统静态文件目录: {e}")

    # 检查Augment数据目录
    augment_data_dir = os.path.dirname(AUGMENT_DATA_FILE)
    if not os.path.exists(augment_data_dir):
        print(f"⚠️  警告：Augment数据目录不存在: {augment_data_dir}")
        print("💡 提示：系统将在首次保存数据时自动创建此目录")
    else:
        print(f"✅ 找到Augment数据目录: {augment_data_dir}")
        if os.path.exists(AUGMENT_DATA_FILE):
            size = os.path.getsize(AUGMENT_DATA_FILE)
            print(f"📄 Augment数据文件: augment_accounts.json ({size} bytes)")
        else:
            print("📄 Augment数据文件: 暂无数据文件")

    # 检查财务管理系统数据目录
    if not os.path.exists(FINANCIAL_DATA_DIR):
        print(f"⚠️  警告：财务管理系统数据目录不存在: {FINANCIAL_DATA_DIR}")
        print("💡 提示：系统将在首次保存数据时自动创建此目录")
    else:
        print(f"✅ 找到财务管理系统数据目录: {FINANCIAL_DATA_DIR}")
        data_files = []
        if os.path.exists(FINANCIAL_EXPENSES_FILE):
            size = os.path.getsize(FINANCIAL_EXPENSES_FILE)
            data_files.append(f"expenses.json ({size} bytes)")
        if os.path.exists(FINANCIAL_CURRENT_ASSETS_FILE):
            size = os.path.getsize(FINANCIAL_CURRENT_ASSETS_FILE)
            data_files.append(f"current_assets.json ({size} bytes)")
        if os.path.exists(FINANCIAL_ASSET_HISTORY_FILE):
            size = os.path.getsize(FINANCIAL_ASSET_HISTORY_FILE)
            data_files.append(f"asset_history.json ({size} bytes)")

        if data_files:
            print(f"📄 财务管理系统数据文件: {', '.join(data_files)}")
        else:
            print("📄 财务管理系统数据文件: 暂无数据文件")

    print("=" * 60)
    print("Flask Web服务启动成功！")
    print(f"本地访问: http://localhost:{PORT}")
    print(f"外网访问: http://YOUR_DOMAIN:{PORT}")
    print("")
    print("📅 日程管理系统:")
    print(f"  访问地址: http://localhost:{PORT}/")
    print(f"  备用地址: http://localhost:{PORT}/schedule")
    print(f"  HTML文件: {HTML_FILE}")
    print(f"  静态文件目录: {STATIC_DIR}")
    print("")

    print("💰💵 合并财务管理系统:")
    print(f"  访问地址: http://localhost:{PORT}/financial")
    print(f"  HTML文件: {FINANCIAL_HTML_FILE}")
    print(f"  静态文件目录: {FINANCIAL_STATIC_DIR}")
    print("")
    print("🚀 Augment账号管理系统:")
    print(f"  访问地址: http://localhost:{PORT}/account")
    print(f"  备用地址: http://localhost:{PORT}/augment")
    print(f"  备用地址: http://localhost:{PORT}/argument")
    print(f"  HTML文件: {AUGMENT_HTML_FILE}")
    print(f"  静态文件目录: {AUGMENT_STATIC_DIR}")
    print("")
    print("🔗 API接口:")
    print("  日程系统:")
    print(f"    GET  /api/schedules        - 获取日程数据")
    print(f"    POST /api/schedules        - 保存日程数据")

    print("  合并财务管理系统:")
    print(f"    GET  /api/financial/expenses      - 获取支出数据")
    print(f"    POST /api/financial/expenses      - 保存支出数据")
    print(f"    GET  /api/financial/current-assets - 获取当前资产")
    print(f"    POST /api/financial/current-assets - 保存当前资产")
    print(f"    GET  /api/financial/asset-history  - 获取资产历史")
    print(f"    POST /api/financial/asset-history  - 保存资产历史")
    print("  Augment账号管理系统:")
    print(f"    GET  /api/accounts         - 获取账号列表")
    print(f"    POST /api/accounts         - 保存账号信息")
    print(f"    PUT  /api/accounts/<id>    - 更新账号信息")
    print(f"    DELETE /api/accounts/<id>  - 删除账号")
    print("  通用:")
    print(f"    GET  /api/health           - 健康检查")
    print("")
    print(f"📄 数据文件:")
    print(f"  日程数据: {DATA_FILE}")
    print(f"  合并财务管理数据目录: {FINANCIAL_DATA_DIR}")
    print(f"    - 支出数据: {FINANCIAL_EXPENSES_FILE}")
    print(f"    - 当前资产: {FINANCIAL_CURRENT_ASSETS_FILE}")
    print(f"    - 资产历史: {FINANCIAL_ASSET_HISTORY_FILE}")
    print(f"  Augment账号数据: {AUGMENT_DATA_FILE}")
    print("=" * 60)
    
    # 启动Flask应用，监听所有IP地址
    app.run(host='0.0.0.0', port=PORT, debug=True) 