/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px 30px;
    margin-bottom: 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header h1 {
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.header h1 i {
    color: #667eea;
}

.save-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    font-size: 14px;
    margin-right: 10px;
    backdrop-filter: blur(5px);
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.save-status.loading {
    animation: pulse 1.5s infinite;
    background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.save-status.error {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.save-status.success {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

@keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
}

/* 按钮样式 */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #64748b;
}

.btn-secondary:hover {
    background: #cbd5e1;
}

.btn-success {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.4);
}

.btn-danger {
    background: #ef4444;
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* 筛选部分 */
.filter-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

/* 视图切换 */
.view-toggle {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.view-btn {
    padding: 10px 16px;
    border: 2px solid #e2e8f0;
    background: white;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.view-btn.active,
.view-btn:hover {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.search-box {
    position: relative;
    margin-bottom: 20px;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
}

.search-box input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
}

.filter-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 8px 16px;
    border: 2px solid #e2e8f0;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    color: #64748b;
}

.filter-btn.active,
.filter-btn:hover {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

/* 日程列表 */
.schedule-list {
    display: grid;
    gap: 15px;
}

.schedule-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid #e2e8f0;
}

.schedule-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.schedule-item.completed {
    opacity: 0.7;
    border-left-color: #22c55e;
}

.schedule-item.high-priority {
    border-left-color: #ef4444;
}

.schedule-item.medium-priority {
    border-left-color: #f59e0b;
}

.schedule-item.low-priority {
    border-left-color: #3b82f6;
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.schedule-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 5px;
}

.schedule-title.completed {
    text-decoration: line-through;
    color: #64748b;
}

.schedule-meta {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 10px;
    font-size: 13px;
    color: #64748b;
}

.schedule-meta i {
    width: 16px;
}

.schedule-description {
    color: #64748b;
    margin-bottom: 15px;
    line-height: 1.5;
}

.schedule-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.complete-btn {
    background: #dcfce7;
    color: #166534;
}

.complete-btn:hover {
    background: #bbf7d0;
}

.edit-btn {
    background: #dbeafe;
    color: #1e40af;
}

.edit-btn:hover {
    background: #bfdbfe;
}

.delete-btn {
    background: #fee2e2;
    color: #dc2626;
}

.delete-btn:hover {
    background: #fecaca;
}

.category-tag {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.category-work { background: #e0e7ff; color: #3730a3; }
.category-personal { background: #fce7f3; color: #be185d; }
.category-study { background: #ecfdf5; color: #065f46; }
.category-health { background: #fef3c7; color: #92400e; }
.category-other { background: #f1f5f9; color: #475569; }

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.empty-state i {
    font-size: 4rem;
    color: #cbd5e1;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #64748b;
    margin-bottom: 10px;
}

.empty-state p {
    color: #94a3b8;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    animation: fadeIn 0.3s ease;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: slideUp 0.3s ease;
}

.modal-header {
    padding: 25px 30px 20px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #64748b;
    padding: 5px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #f1f5f9;
    color: #1e293b;
}

.modal-body {
    padding: 25px 30px;
}

/* 表单样式 */
form {
    padding: 25px 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #374151;
}

input, textarea, select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: #667eea;
}

textarea {
    resize: vertical;
    min-height: 80px;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
    margin-top: 20px;
}

/* Toast 通知 */
.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #1f2937;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 1001;
}

.toast.show {
    transform: translateX(0);
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .filter-section {
        padding: 15px 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .schedule-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .schedule-actions {
        flex-wrap: wrap;
    }
    
    .modal-content {
        margin: 10px;
    }
    
    .modal-header, form, .modal-body {
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .header h1 {
        font-size: 1.3rem;
    }
    
    .filter-buttons {
        justify-content: center;
    }
    
    .schedule-meta {
        flex-direction: column;
        gap: 5px;
        align-items: flex-start;
    }
}

/* 日历视图样式 */
.calendar-view {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    max-width: 95%;
    margin: 0 auto;
}

.calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    margin-bottom: 8px;
}

.calendar-header h2 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.calendar-nav-btn {
    background: #f9fafb;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    color: #374151;
    font-size: 14px;
}

.calendar-nav-btn:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 15px;
}

.calendar-day-header {
    padding: 6px 3px;
    text-align: center;
    font-weight: 600;
    color: #64748b;
    font-size: 10px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    background: #f8fafc;
    color: #64748b;
    position: relative;
    min-height: 28px;
    padding: 1px;
    margin: 0;
    overflow: hidden;
}

.calendar-day-number {
    font-size: 11px;
    margin-bottom: 1px;
    line-height: 1;
    font-weight: 600;
    text-align: center;
    width: 100%;
}

.calendar-day-score {
    font-size: 10px;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    width: 100%;
}

.calendar-day:hover {
    border-color: #667eea;
    transform: scale(1.01);
}

/* 移除添加的分数后缀和额外样式 */
.calendar-day-score::after {
    content: none;
}

/* 恢复原始颜色设置 */
.calendar-day.high-score {
    background: #dcfce7;
    color: #166534;
}

.calendar-day.medium-score {
    background: #fef3c7;
    color: #92400e;
}

.calendar-day.low-score {
    background: #fee2e2;
    color: #dc2626;
}

.calendar-legend {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #e2e8f0;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 11px;
    color: #64748b;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 3px;
}

.legend-color.high-score {
    background: #dcfce7;
}

.legend-color.medium-score {
    background: #fef3c7;
}

.legend-color.low-score {
    background: #fee2e2;
}

.legend-color.no-data {
    background: #f8fafc;
}

/* 日期详情模态框 */
.day-detail-content {
    max-width: 600px;
    max-height: 80vh;
}

.day-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
}

.score-info, .completion-info {
    text-align: center;
}

.total-score, .completion-rate {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.total-score {
    color: #667eea;
}

.completion-rate {
    color: #22c55e;
}

.score-label, .completion-label {
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
}

.day-tasks {
    max-height: 400px;
    overflow-y: auto;
}

.day-task-item {
    background: white;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
    border-left: 4px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.day-task-item.completed {
    opacity: 0.7;
    border-left-color: #22c55e;
}

.day-task-item.high-priority {
    border-left-color: #ef4444;
}

.day-task-item.medium-priority {
    border-left-color: #f59e0b;
}

.day-task-item.low-priority {
    border-left-color: #3b82f6;
}

.day-task-content {
    flex: 1;
}

.day-task-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #1e293b;
}

.day-task-title.completed {
    text-decoration: line-through;
    color: #64748b;
}

.day-task-meta {
    font-size: 13px;
    color: #64748b;
    display: flex;
    gap: 15px;
    align-items: center;
}

.day-task-score {
    font-size: 18px;
    font-weight: 600;
    color: #667eea;
    min-width: 50px;
    text-align: right;
}

/* 表单提示文本 */
.form-hint {
    color: #64748b;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* 分数输入样式 */
#eventScore {
    text-align: center;
    font-weight: 600;
    font-size: 16px;
}

/* 数据总结视图样式 */
.summary-view {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.summary-nav {
    display: flex;
    gap: 10px;
    margin-bottom: 25px;
    justify-content: center;
}

.summary-nav-btn {
    padding: 12px 20px;
    border: 2px solid #e2e8f0;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    color: #64748b;
    display: flex;
    align-items: center;
    gap: 8px;
}

.summary-nav-btn.active,
.summary-nav-btn:hover {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.summary-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30px;
}

.summary-header h2 {
    color: #1e293b;
    font-size: 1.8rem;
    font-weight: 600;
    margin: 0;
}

.summary-nav-arrow {
    background: #f1f5f9;
    border: none;
    border-radius: 8px;
    width: 40px;
    height: 40px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: #64748b;
}

.summary-nav-arrow:hover {
    background: #667eea;
    color: white;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.summary-card .card-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.card-content {
    text-align: center;
}

.card-value {
    font-size: 2rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 5px;
}

.card-label {
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
}

.summary-details {
    display: grid;
    gap: 25px;
}

.detail-section {
    background: #f8fafc;
    border-radius: 12px;
    padding: 20px;
}

.detail-section h3 {
    color: #1e293b;
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
}

/* 分数趋势图 */
.score-chart {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(30px, 1fr));
    gap: 4px;
    height: 120px;
    align-items: end;
    padding: 10px;
    background: white;
    border-radius: 8px;
}

.score-bar {
    background: linear-gradient(to top, #667eea, #764ba2);
    border-radius: 3px 3px 0 0;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 5px;
}

.score-bar:hover {
    opacity: 0.8;
    transform: scale(1.05);
}

.score-bar.high {
    background: linear-gradient(to top, #22c55e, #16a34a);
}

.score-bar.medium {
    background: linear-gradient(to top, #f59e0b, #d97706);
}

.score-bar.low {
    background: linear-gradient(to top, #ef4444, #dc2626);
}

.score-bar-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1f2937;
    color: white;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.score-bar:hover .score-bar-tooltip {
    opacity: 1;
}

/* 分类统计 */
.category-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.category-stat-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    border-left: 4px solid #e2e8f0;
}

.category-stat-item.work {
    border-left-color: #3730a3;
}

.category-stat-item.personal {
    border-left-color: #be185d;
}

.category-stat-item.study {
    border-left-color: #065f46;
}

.category-stat-item.health {
    border-left-color: #92400e;
}

.category-stat-item.other {
    border-left-color: #475569;
}

.category-stat-name {
    font-weight: 600;
    margin-bottom: 5px;
    color: #1e293b;
}

.category-stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

.category-stat-percentage {
    font-size: 12px;
    color: #64748b;
}

/* 表现分析 */
.performance-analysis {
    background: white;
    border-radius: 8px;
    padding: 20px;
}

.performance-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #e2e8f0;
}

.performance-item:last-child {
    border-bottom: none;
}

.performance-label {
    font-weight: 500;
    color: #1e293b;
}

.performance-value {
    font-weight: 600;
    color: #667eea;
}

.performance-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.performance-badge.excellent {
    background: #dcfce7;
    color: #166534;
}

.performance-badge.good {
    background: #fef3c7;
    color: #92400e;
}

.performance-badge.average {
    background: #fee2e2;
    color: #dc2626;
}

/* 季度对比 */
.quarter-comparison {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.month-comparison-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.month-comparison-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.month-name {
    font-weight: 600;
    margin-bottom: 10px;
    color: #1e293b;
}

.month-score {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: #667eea;
}

.month-trend {
    font-size: 12px;
    font-weight: 500;
}

.month-trend.up {
    color: #22c55e;
}

.month-trend.down {
    color: #ef4444;
}

.month-trend.stable {
    color: #64748b;
}

/* 季度目标 */
.quarter-goals {
    background: white;
    border-radius: 8px;
    padding: 20px;
}

.goal-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid #e2e8f0;
}

.goal-item:last-child {
    border-bottom: none;
}

.goal-description {
    font-weight: 500;
    color: #1e293b;
}

.goal-progress {
    display: flex;
    align-items: center;
    gap: 10px;
}

.progress-bar {
    width: 100px;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(to right, #667eea, #764ba2);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 14px;
    font-weight: 600;
    color: #667eea;
}

/* 响应式日历 */
@media (max-width: 768px) {
    .calendar-grid {
        gap: 2px;
    }
    
    .calendar-day {
        min-height: 38px;
        font-size: 14px;
    }
    
    .calendar-day-number {
        font-size: 12px;
    }
    
    .calendar-day-score {
        font-size: 10px;
    }
    
    .calendar-legend {
        gap: 15px;
    }
    
    .legend-item {
        font-size: 12px;
    }
    
    .day-summary {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }
    
    .view-toggle {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .summary-cards {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .quarter-comparison {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .summary-nav {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .calendar-day {
        min-height: 35px;
    }
    
    .calendar-day-number {
        font-size: 11px;
    }
    
    .calendar-legend {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }
    
    .summary-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .category-stats {
        grid-template-columns: 1fr;
    }
}

/* 保存状态指示器 */
.save-status {
    display: inline-flex;
    align-items: center;
    margin-right: 10px;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 8px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    font-size: 0.85rem;
    backdrop-filter: blur(5px);
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
}

.save-status i {
    color: white;
}

.save-status.loading i {
    color: white;
    animation: pulse 1s infinite;
}

.save-status.success i {
    color: white;
}

.save-status.error i {
    color: white;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* 单选框样式 */
.radio-group {
    margin-bottom: 15px;
}

.radio-group label {
    display: block;
    margin-bottom: 8px;
    cursor: pointer;
}

/* 开关样式 */
.switch-toggle {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.switch-toggle input[type="checkbox"] {
    height: 0;
    width: 0;
    visibility: hidden;
    position: absolute;
}

.switch-toggle label {
    cursor: pointer;
    width: 50px;
    height: 24px;
    background: #ccc;
    display: block;
    border-radius: 24px;
    position: relative;
    margin-right: 10px;
}

.switch-toggle label:after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: #fff;
    border-radius: 20px;
    transition: 0.3s;
}

.switch-toggle input:checked + label {
    background: #4CAF50;
}

.switch-toggle input:checked + label:after {
    left: calc(100% - 2px);
    transform: translateX(-100%);
}

/* 分类管理样式 */
.category-manager {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.category-form {
    background: #f8fafc;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.category-form h3 {
    margin-bottom: 15px;
    color: #1e293b;
    font-size: 1.1rem;
}

.category-list {
    flex: 1;
}

.category-list h3 {
    margin-bottom: 15px;
    color: #1e293b;
    font-size: 1.1rem;
}

.category-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 8px;
    transition: all 0.3s ease;
}

.category-item:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
}

.category-item-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.category-color-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
}

.category-item-name {
    font-weight: 500;
    color: #1e293b;
}

.category-item-actions {
    display: flex;
    gap: 8px;
}

.category-item-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.category-item-btn.edit {
    background: #e0e7ff;
    color: #3730a3;
}

.category-item-btn.edit:hover {
    background: #c7d2fe;
}

.category-item-btn.delete {
    background: #fee2e2;
    color: #dc2626;
}

.category-item-btn.delete:hover {
    background: #fecaca;
}

.category-item-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.category-empty {
    text-align: center;
    color: #64748b;
    padding: 20px;
    font-style: italic;
}

/* 动态分类颜色样式 */
.category-blue { background: #e0e7ff; color: #3730a3; }
.category-green { background: #dcfce7; color: #166534; }
.category-red { background: #fee2e2; color: #dc2626; }
.category-yellow { background: #fef3c7; color: #92400e; }
.category-purple { background: #f3e8ff; color: #7c3aed; }
.category-orange { background: #fed7aa; color: #c2410c; }
.category-pink { background: #fce7f3; color: #be185d; }
.category-gray { background: #f1f5f9; color: #475569; }

/* 分类颜色指示器 */
.category-color-indicator.blue { background: #3730a3; }
.category-color-indicator.green { background: #166534; }
.category-color-indicator.red { background: #dc2626; }
.category-color-indicator.yellow { background: #92400e; }
.category-color-indicator.purple { background: #7c3aed; }
.category-color-indicator.orange { background: #c2410c; }
.category-color-indicator.pink { background: #be185d; }
.category-color-indicator.gray { background: #475569; }

/* 设置信息区域 */
.settings-info {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 12px;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-top: 15px;
}

.settings-info p {
    margin: 0 0 10px 0;
}

.settings-info p:last-child {
    margin-bottom: 0;
}

/* 周计划样式 */
.week-plan-view {
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.week-plan-header {
    text-align: center;
    margin-bottom: 30px;
}

.week-plan-header h2 {
    color: #374151;
    margin-bottom: 10px;
    font-size: 1.5rem;
    font-weight: 600;
}

.week-plan-header p {
    color: #64748b;
    font-size: 0.9rem;
}

.week-calendar-container {
    max-width: 800px;
    margin: 0 auto;
}

.week-calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.week-calendar-header h3 {
    color: #374151;
    font-size: 1.2rem;
    margin: 0;
    font-weight: 600;
}

.week-calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 1px;
    background: #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #d1d5db;
}

.week-calendar-day-header {
    background: #f9fafb;
    padding: 12px;
    text-align: center;
    font-weight: 600;
    color: #374151;
    font-size: 14px;
}

.week-calendar-day {
    background: white;
    padding: 12px;
    min-height: 80px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 16px;
    color: #1e293b;
    position: relative;
    border-radius: 2px;
}

.week-calendar-day:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
    transform: none;
    transition: all 0.2s ease;
}

.week-calendar-day.other-month {
    color: #9ca3af;
    background: linear-gradient(135deg, #f9fafb 0%, #f1f5f9 100%);
    opacity: 0.6;
    font-weight: 400;
}

.week-calendar-day.today {
    background: #f0f9ff;
    color: #0369a1;
    font-weight: 600;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2);
}

.week-calendar-day.selected-week {
    background: #667eea;
    border-color: #667eea;
    color: white;
    font-weight: 600;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
    transform: none;
    transition: all 0.2s ease;
}

.week-calendar-day.week-hover {
    background: #e2e8f0;
    border-color: #cbd5e1;
    color: #374151;
    font-weight: 500;
    transform: none;
}

.week-calendar-day.has-week-plan {
    background: #ecfdf5;
    border-color: #22c55e;
    color: #16a34a;
    font-weight: 500;
}

.week-calendar-day.has-week-plan.selected-week {
    background: #667eea;
    border-color: #667eea;
    color: white;
    font-weight: 600;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
}

.week-calendar-day::after {
    content: '';
    position: absolute;
    bottom: 4px;
    left: 50%;
    transform: translateX(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: transparent;
    transition: all 0.3s ease;
}

.week-calendar-day.has-week-plan::after {
    background: #22c55e;
    width: 6px;
    height: 6px;
}

.week-calendar-day.has-week-plan.selected-week::after {
    background: white;
}

/* 周计划模态框 */
#weekPlanModal .modal-content {
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
}

.week-plan-info {
    margin-bottom: 30px;
}

.week-plan-info h3 {
    color: #1e293b;
    margin-bottom: 20px;
    font-size: 1.2rem;
    text-align: center;
}

.form-row-split {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row-split .form-group {
    margin-bottom: 0;
}

.form-row-split label {
    font-weight: 600;
    color: #374151;
    display: block;
    margin-bottom: 8px;
}

.form-row-split label[for="weekPlanTitle"]::after {
    content: "";
    color: #ef4444;
    margin-left: 2px;
}

.form-row-split label[for="weekPlanDateRange"]::after {
    content: "";
    color: #ef4444;
    margin-left: 2px;
}

#weekPlanDateRangeInput {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #475569;
    cursor: not-allowed;
    font-weight: 500;
    border: 1px solid #cbd5e1;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.week-plan-tasks {
    margin-top: 30px;
}

.week-plan-tasks-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.week-plan-tasks-header h4 {
    color: #1e293b;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.week-plan-tasks-table {
    background: white;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.week-plan-tasks-header-row {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr 0.8fr 1fr;
    gap: 0;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
    background: linear-gradient(90deg, #8b5cf6 0%, #7c3aed 20%, #6d28d9 40%, #5b21b6 60%, #4c1d95 80%, #581c87 100%);
}

.task-header-cell {
    padding: 16px 12px;
    font-weight: 600;
    color: white;
    text-align: center;
    font-size: 14px;
    background: transparent;
    border-right: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.2s ease;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.task-header-cell:last-child {
    border-right: none;
}

/* 紫色渐变系列 - 从浅到深 */
.task-header-cell:nth-child(1) {
    background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 100%); /* 浅紫色 - 日期 */
}

.task-header-cell:nth-child(2) {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); /* 中紫色 - 分类 */
}

.task-header-cell:nth-child(3) {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%); /* 主紫色 - 内容 */
}

.task-header-cell:nth-child(4) {
    background: linear-gradient(135deg, #6d28d9 0%, #5b21b6 100%); /* 深紫色 - 分数 */
}

.task-header-cell:nth-child(5) {
    background: linear-gradient(135deg, #5b21b6 0%, #4c1d95 100%); /* 最深紫色 - 操作 */
}

.task-header-cell:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.week-plan-tasks-body {
    max-height: 300px;
    overflow-y: auto;
}

.week-task-row {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr 0.8fr 1fr;
    gap: 1px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    transition: background-color 0.3s ease;
}

.week-task-row:hover {
    background: #f1f5f9;
}

.week-task-row:last-child {
    border-bottom: none;
}

.week-task-cell {
    padding: 12px 15px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.week-task-cell input,
.week-task-cell select,
.week-task-cell textarea {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    font-size: 13px;
    min-height: 32px;
}

.week-task-cell textarea {
    resize: vertical;
    min-height: 40px;
}

.week-task-cell .task-actions {
    display: flex;
    gap: 8px;
}

.week-task-cell .task-action-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.week-task-cell .task-action-btn.delete {
    background: #fee2e2;
    color: #dc2626;
}

.week-task-cell .task-action-btn.delete:hover {
    background: #fecaca;
}

.week-task-empty {
    padding: 40px 20px;
    text-align: center;
    color: #64748b;
    font-style: italic;
    background: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .week-calendar-grid {
        gap: 1px;
    }
    
    .week-calendar-day {
        font-size: 14px;
        min-height: 60px;
    }
    
    .week-plan-tasks-header {
        flex-direction: column;
        gap: 10px;
        align-items: stretch;
    }
    
    .week-plan-tasks-header-row,
    .week-task-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .task-header-cell,
    .week-task-cell {
        text-align: left;
        border-bottom: 1px solid #e2e8f0;
    }
    
    .task-header-cell {
        background: #f8fafc;
        font-weight: 600;
    }
    
    .week-task-cell {
        padding: 8px 15px;
    }
    
    .week-task-cell::before {
        content: attr(data-label);
        font-weight: 600;
        color: #64748b;
        margin-right: 10px;
        min-width: 60px;
        display: inline-block;
    }
    
    .week-task-cell input[type="date"] {
        width: 100%;
        padding: 6px 10px;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        font-size: 13px;
        min-height: 32px;
    }
    
    .form-row-split {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .form-row-split .form-group {
        margin-bottom: 15px;
    }
}

/* 周计划任务特殊样式 */
.schedule-item.week-plan-task {
    border-left: 4px solid #8b5cf6;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.schedule-item.week-plan-task:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.15);
}

.day-task-item.week-plan-task {
    border-left: 4px solid #8b5cf6;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.day-task-item.week-plan-task:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

/* 禁用状态的按钮 */
.action-btn.disabled {
    background: #e2e8f0 !important;
    color: #94a3b8 !important;
    cursor: not-allowed !important;
    opacity: 0.6;
}

.action-btn.disabled:hover {
    transform: none !important;
    box-shadow: none !important;
    background: #e2e8f0 !important;
} 