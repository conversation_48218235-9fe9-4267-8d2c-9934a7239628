#!/bin/bash

echo "=== 停止Flask Web服务 ==="
echo ""

# 检查PID文件是否存在
if [ -f "server.pid" ]; then
    PID=$(cat server.pid)
    echo "发现Flask服务进程 PID: $PID"
    
    # 检查进程是否存在
    if ps -p $PID > /dev/null; then
        echo "正在停止Flask服务..."
        kill $PID
        echo "✅ Flask服务已停止"
        rm -f server.pid
    else
        echo "⚠️  进程不存在，删除PID文件"
        rm -f server.pid
    fi
else
    echo "❌ 未找到PID文件，服务可能未运行"
    echo "手动查找进程: ps aux | grep app.py"
fi

echo "" 