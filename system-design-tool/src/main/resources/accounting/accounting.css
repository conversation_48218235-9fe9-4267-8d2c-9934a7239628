/* 全局样式 */
:root {
  --primary-color: #3f51b5;
  --primary-light: #757de8;
  --primary-dark: #002984;
  --secondary-color: #ff4081;
  --secondary-light: #ff79b0;
  --secondary-dark: #c60055;
  --text-color: #212121;
  --text-secondary: #757575;
  --background-color: #f5f5f5;
  --card-color: #ffffff;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --danger-color: #f44336;
  --border-radius: 12px;
  --box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
  --transition: all 0.3s ease;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* 头部区域 */
.app-header {
  background-color: var(--primary-color);
  color: white;
  padding: var(--spacing-md) var(--spacing-xl);
  box-shadow: var(--box-shadow);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1280px;
  margin: 0 auto;
}

.header-content h1 {
  font-size: 1.5rem;
  font-weight: var(--font-weight-medium);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

/* 主容器 */
.main-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* 仪表盘区域 */
.dashboard-section {
  margin-bottom: var(--spacing-lg);
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--spacing-lg);
}

.dashboard-card {
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-xl);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.dashboard-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.total-card {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
}

.card-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
  opacity: 0.9;
}

.card-value {
  font-size: 1.8rem;
  font-weight: var(--font-weight-bold);
}

.card-subtitle {
  font-size: 0.85rem;
  opacity: 0.7;
  margin-top: var(--spacing-xs);
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

/* 通用卡片样式 */
.form-section,
.chart-section,
.expenses-section {
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.section-header h2 {
  font-size: 1.25rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.section-header.with-actions {
  margin-bottom: var(--spacing-lg);
}

.section-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* 表单样式 */
.expense-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: var(--spacing-md);
  font-size: 1rem;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: var(--border-radius);
  background-color: white;
  transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.15);
}

.form-actions {
  margin-top: var(--spacing-md);
}

/* 按钮样式 */
.btn-primary,
.btn-secondary,
.btn-danger,
.btn-large {
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  font-size: 0.95rem;
  cursor: pointer;
  border: none;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--text-color);
}

.btn-secondary:hover {
  background-color: rgba(0, 0, 0, 0.15);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-danger:hover {
  background-color: #d32f2f;
}

.btn-large {
  width: 100%;
  padding: var(--spacing-md);
}

.btn-icon {
  padding: var(--spacing-sm);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 图表容器 */
.chart-container {
  height: 300px;
  position: relative;
  margin-bottom: var(--spacing-lg);
}

/* 图表控制区域 */
.chart-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.chart-control-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.chart-type-toggle {
  display: flex;
  border-radius: var(--spacing-sm);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.chart-type-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: white;
  border: none;
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.chart-type-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-color);
}

.chart-type-btn.active {
  background-color: var(--primary-color);
  color: white;
}

.chart-period-select {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--spacing-sm);
  border: 1px solid rgba(0, 0, 0, 0.1);
  font-size: 0.9rem;
  background-color: white;
  transition: var(--transition);
}

.chart-period-select:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(63, 81, 181, 0.15);
}

/* 图表图例 */
.chart-legend {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.chart-legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 0.9rem;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--spacing-sm);
  transition: var(--transition);
}

.chart-legend-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.chart-legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.chart-legend-item.inactive {
  opacity: 0.5;
}

/* 筛选下拉菜单 */
.filter-dropdown select {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  border: 1px solid rgba(0, 0, 0, 0.15);
  background-color: white;
  font-size: 0.9rem;
}

/* 支出网格 */
.expenses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* 月份聚合卡片 */
.month-card {
  position: relative;
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-lg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

.month-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  background-color: rgba(63, 81, 181, 0.05);
}

.month-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.month-name {
  font-size: 1.2rem;
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

.month-total {
  font-size: 1.4rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-color);
}

.month-stats {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-top: var(--spacing-md);
}

.month-stat-item {
  flex: 1 0 calc(50% - var(--spacing-md));
  display: flex;
  flex-direction: column;
  padding: var(--spacing-sm);
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: var(--spacing-sm);
}

.month-stat-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.month-stat-value {
  font-weight: var(--font-weight-medium);
}

/* 原支出项卡片 */
.expense-item {
  position: relative;
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.expense-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.expense-item-type {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-sm);
  font-size: 1rem;
}

.expense-item-type span {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  font-size: 1rem;
}

.expense-item-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.expense-item-date {
  color: var(--text-secondary);
  font-size: 0.85rem;
}

.expense-item-amount {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-size: 1.2rem;
}

.expense-item-note {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.expense-item-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

.expense-item-actions button {
  background: none;
  border: none;
  font-size: 1rem;
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition);
  padding: var(--spacing-xs);
}

.expense-item-actions button:hover {
  color: var(--primary-color);
}

.expense-item-actions button.btn-delete:hover {
  color: var(--danger-color);
}

/* 月度明细模态框 */
.modal-large {
  max-width: 800px;
  width: 90%;
}

.month-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.month-detail-title {
  font-size: 1.3rem;
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

.month-detail-summary {
  display: flex;
  justify-content: space-between;
  background-color: rgba(0, 0, 0, 0.02);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
}

.month-detail-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.month-detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.month-detail-item:last-child {
  border-bottom: none;
}

.month-detail-type {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.month-detail-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
}

.month-detail-info {
  flex: 1;
  padding: 0 var(--spacing-md);
}

.month-detail-date {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.month-detail-note {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.month-detail-amount {
  font-weight: var(--font-weight-medium);
}

.month-detail-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: var(--spacing-md);
}

.modal-container {
  background-color: var(--card-color);
  border-radius: var(--border-radius);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalFadeIn 0.3s ease forwards;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-small {
  max-width: 400px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--text-secondary);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.close-btn:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
}

/* 提示消息 */
.toast {
  position: fixed;
  bottom: var(--spacing-xl);
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(33, 33, 33, 0.9);
  color: white;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  z-index: 2000;
  opacity: 0;
  transition: opacity 0.3s, transform 0.3s;
  max-width: 80%;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .expenses-grid {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .main-container {
    padding: var(--spacing-md);
  }
  
  .dashboard-cards {
    grid-template-columns: 1fr;
  }
  
  .month-stats {
    flex-direction: column;
  }
  
  .month-stat-item {
    flex: 1 0 100%;
  }
  
  .chart-controls {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* 空状态 */
.empty-state {
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--text-secondary);
} 