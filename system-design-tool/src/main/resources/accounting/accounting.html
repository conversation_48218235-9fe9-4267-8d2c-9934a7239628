<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>个人财务管理系统</title>
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1">
  <link rel="stylesheet" href="/accounting/accounting.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
  <!-- 顶部导航 -->
  <header class="app-header">
    <div class="header-content">
      <h1>个人财务管理</h1>
    </div>
  </header>

  <!-- 主要内容区 -->
  <div class="main-container">
    <!-- 仪表盘区域 -->
    <section class="dashboard-section">
      <div class="dashboard-cards">
        <div class="dashboard-card total-card">
          <div class="card-icon">
            <i class="fas fa-wallet"></i>
          </div>
          <div class="card-content">
            <h3>本月支出</h3>
            <div class="card-value" id="total-expense">¥ 0.00</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 记录与图表区域 -->
    <div class="content-grid">
      <!-- 新增支出表单 -->
      <section class="form-section">
        <div class="section-header">
          <h2>新增支出</h2>
        </div>
        <form id="expense-form" class="expense-form">
          <div class="form-row">
            <div class="form-group">
              <label for="expense-type">
                <i class="fas fa-tag"></i> 支出类型
              </label>
              <select id="expense-type" required>
                <option value="">请选择类型</option>
                <option value="icbc_credit">工行信用卡</option>
                <option value="cmb_credit">招行信用卡</option>
                <option value="alipay">支付宝</option>
                <option value="wechat">微信 (包含房租和水电费)</option>
              </select>
            </div>
            <div class="form-group">
              <label for="expense-amount">
                <i class="fas fa-yen-sign"></i> 金额 (元)
              </label>
              <input type="number" id="expense-amount" step="0.01" placeholder="输入金额" required>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="expense-date">
                <i class="fas fa-calendar"></i> 日期
              </label>
              <input type="date" id="expense-date" required>
            </div>
            <div class="form-group">
              <label for="expense-note">
                <i class="fas fa-sticky-note"></i> 备注
              </label>
              <input type="text" id="expense-note" placeholder="添加备注(可选)">
            </div>
          </div>
          <div class="form-actions">
            <button type="submit" class="btn-primary btn-large">
              <i class="fas fa-plus"></i> 添加支出
            </button>
          </div>
        </form>
      </section>

      <!-- 趋势图卡片 -->
      <section class="chart-section">
        <div class="section-header">
          <h2>月度消费趋势</h2>
          <div class="chart-controls">
            <div class="chart-control-group">
              <div class="chart-type-toggle">
                <button class="chart-type-btn active" data-type="bar" title="柱状图">
                  <i class="fas fa-chart-bar"></i>
                </button>
                <button class="chart-type-btn" data-type="line" title="折线图">
                  <i class="fas fa-chart-line"></i>
                </button>
              </div>
            </div>
            <div class="chart-control-group">
              <select id="chart-period" class="chart-period-select">
                <option value="6">近6个月</option>
                <option value="12" selected>近12个月</option>
                <option value="24">近24个月</option>
              </select>
            </div>
          </div>
        </div>
        <div class="chart-container">
          <canvas id="expense-trend-chart"></canvas>
        </div>
        <div class="chart-legend" id="chart-legend"></div>
      </section>
    </div>

    <!-- 支出记录区域 -->
    <section class="expenses-section">
      <div class="section-header with-actions">
        <h2>支出记录</h2>
        <div class="section-actions">
          <div class="filter-dropdown">
            <select id="expense-filter">
              <option value="all">全部记录</option>
              <option value="current-month" selected>本月</option>
              <option value="last-month">上月</option>
            </select>
          </div>
          <button class="btn-icon btn-danger" id="clear-expense-btn" title="清空记录">
            <i class="fas fa-trash-alt"></i>
          </button>
        </div>
      </div>
      
      <div class="expenses-grid" id="expense-list">
        <!-- 支出记录将由JavaScript生成并平铺展示 -->
      </div>
    </section>
  </div>

  <!-- 月度明细弹窗 -->
  <div class="modal-overlay" id="month-detail-modal">
    <div class="modal-container modal-large">
      <div class="modal-header">
        <h3 id="month-detail-title">月度明细</h3>
        <button class="close-btn" id="close-month-detail-modal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div id="month-detail-content">
          <!-- 月份明细将由JavaScript生成 -->
        </div>
      </div>
    </div>
  </div>

  <!-- 编辑弹窗 -->
  <div class="modal-overlay" id="edit-modal">
    <div class="modal-container">
      <div class="modal-header">
        <h3 id="modal-title-text">编辑支出</h3>
        <button class="close-btn" id="close-modal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body" id="modal-body">
        <!-- 编辑内容将由JavaScript生成 -->
      </div>
      <div class="modal-footer">
        <button class="btn-secondary" id="cancel-btn">取消</button>
        <button class="btn-primary" id="confirm-btn">确认</button>
      </div>
    </div>
  </div>

  <!-- 确认删除弹窗 -->
  <div class="modal-overlay" id="confirm-modal">
    <div class="modal-container modal-small">
      <div class="modal-header">
        <h3>确认删除</h3>
        <button class="close-btn" id="close-confirm-modal">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <p id="confirm-message">确定要删除这条记录吗？</p>
      </div>
      <div class="modal-footer">
        <button class="btn-secondary" id="confirm-cancel-btn">取消</button>
        <button class="btn-danger" id="confirm-delete-btn">删除</button>
      </div>
    </div>
  </div>

  <!-- 提示消息 -->
  <div class="toast" id="toast"></div>

  <script src="/accounting/accounting.js"></script>
</body>
</html> 