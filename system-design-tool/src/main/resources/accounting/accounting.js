// 常量定义
const EXPENSE_TYPES = {
  icbc_credit: { name: '工行信用卡', icon: 'fa-credit-card', color: '#C41E3A' },
  cmb_credit: { name: '招行信用卡', icon: 'fa-credit-card', color: '#E60012' },
  alipay: { name: '支付宝', icon: 'fa-mobile-alt', color: '#1677FF' },
  wechat: { name: '微信', icon: 'fa-comment', color: '#1AAD19' }
};

// 全局变量
let expenses = [];
let trendChart = null;
let editExpenseId = null;
let deleteExpenseId = null;
let selectedMonth = null;
let chartType = 'bar'; // 默认图表类型
let chartPeriod = 12;  // 默认显示月份数量

// 图表颜色配置
const CHART_COLORS = {
  primary: 'rgba(63, 81, 181, 0.7)',
  primaryBorder: 'rgba(63, 81, 181, 1)',
  icbc_credit: 'rgba(196, 30, 58, 0.7)',
  cmb_credit: 'rgba(230, 0, 18, 0.7)',
  alipay: 'rgba(22, 119, 255, 0.7)',
  wechat: 'rgba(26, 173, 25, 0.7)',
  border: {
    icbc_credit: 'rgba(196, 30, 58, 1)',
    cmb_credit: 'rgba(230, 0, 18, 1)',
    alipay: 'rgba(22, 119, 255, 1)',
    wechat: 'rgba(26, 173, 25, 1)'
  }
};

// 主要类
class AccountingSystem {
  constructor() {
    this.init();
  }

  init() {
    this.loadExpenses();
    this.setDefaultDate();
    this.bindEvents();
    this.renderExpenseList();
    this.updateTotalExpense();
    this.renderTrendChart();
    
    // 自动保存数据的定时器
    setInterval(() => {
      this.saveExpenses();
    }, 30000); // 每30秒自动保存一次
  }

  // 设置表单默认日期为今天
  setDefaultDate() {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    document.getElementById('expense-date').value = `${year}-${month}-${day}`;
  }

  // 绑定事件
  bindEvents() {
    // 提交表单
    document.getElementById('expense-form').addEventListener('submit', (e) => {
      e.preventDefault();
      this.addExpense();
    });

    // 清空记录
    document.getElementById('clear-expense-btn').addEventListener('click', () => {
      this.showConfirmModal('确定要清空所有记录吗？这将无法恢复！', () => {
        this.clearAllExpenses();
      });
    });

    // 筛选记录
    document.getElementById('expense-filter').addEventListener('change', () => {
      this.renderExpenseList();
    });

    // 编辑模态框
    document.getElementById('close-modal').addEventListener('click', () => this.closeEditModal());
    document.getElementById('cancel-btn').addEventListener('click', () => this.closeEditModal());
    document.getElementById('confirm-btn').addEventListener('click', () => this.confirmEdit());

    // 确认模态框
    document.getElementById('close-confirm-modal').addEventListener('click', () => this.closeConfirmModal());
    document.getElementById('confirm-cancel-btn').addEventListener('click', () => this.closeConfirmModal());
    document.getElementById('confirm-delete-btn').addEventListener('click', () => this.confirmDelete());
    
    // 月度明细模态框
    document.getElementById('close-month-detail-modal').addEventListener('click', () => this.closeMonthDetailModal());
    
    // 微信支出类型提示
    document.getElementById('expense-type').addEventListener('change', (e) => {
      if (e.target.value === 'wechat') {
        document.getElementById('expense-note').placeholder = '包含房租和水电费';
      } else {
        document.getElementById('expense-note').placeholder = '添加备注(可选)';
      }
    });
    
    // 图表类型切换
    document.querySelectorAll('.chart-type-btn').forEach(btn => {
      btn.addEventListener('click', () => {
        document.querySelectorAll('.chart-type-btn').forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
        chartType = btn.dataset.type;
        this.renderTrendChart();
      });
    });
    
    // 图表周期选择
    document.getElementById('chart-period').addEventListener('change', (e) => {
      chartPeriod = parseInt(e.target.value);
      this.renderTrendChart();
    });
  }

  // 添加支出
  addExpense() {
    const typeEl = document.getElementById('expense-type');
    const amountEl = document.getElementById('expense-amount');
    const dateEl = document.getElementById('expense-date');
    const noteEl = document.getElementById('expense-note');

    const type = typeEl.value;
    let amount = parseFloat(amountEl.value);
    const date = dateEl.value;
    let note = noteEl.value;

    // 验证
    if (!type || !amount || !date) {
      this.showToast('请填写完整信息');
      return;
    }
    
    // 如果是微信支出且没有填写备注，自动添加包含房租水电的备注
    if (type === 'wechat' && !note) {
      note = '包含房租和水电费';
    }

    const expense = {
      id: this.generateId(),
      type: type,
      amount: amount,
      date: date,
      note: note,
      createdAt: new Date().toISOString()
    };

    expenses.push(expense);
    
    // 重置表单
    document.getElementById('expense-form').reset();
    this.setDefaultDate();
    
    // 更新UI
    this.renderExpenseList();
    this.updateTotalExpense();
    this.renderTrendChart();
    this.saveExpenses();
    
    this.showToast('已添加新支出记录');
  }

  // 编辑支出
  editExpense(id) {
    const expense = expenses.find(e => e.id === id);
    if (!expense) return;

    editExpenseId = id;
    
    // 生成编辑表单
    const formHtml = `
      <div class="form-row">
        <div class="form-group">
          <label for="edit-expense-type">
            <i class="fas fa-tag"></i> 支出类型
          </label>
          <select id="edit-expense-type" required>
            ${Object.entries(EXPENSE_TYPES).map(([value, { name }]) => 
              `<option value="${value}" ${expense.type === value ? 'selected' : ''}>${name}</option>`
            ).join('')}
          </select>
        </div>
        <div class="form-group">
          <label for="edit-expense-amount">
            <i class="fas fa-yen-sign"></i> 金额 (元)
          </label>
          <input type="number" id="edit-expense-amount" step="0.01" value="${expense.amount}" required>
        </div>
      </div>
      <div class="form-row">
        <div class="form-group">
          <label for="edit-expense-date">
            <i class="fas fa-calendar"></i> 日期
          </label>
          <input type="date" id="edit-expense-date" value="${expense.date}" required>
        </div>
        <div class="form-group">
          <label for="edit-expense-note">
            <i class="fas fa-sticky-note"></i> 备注
          </label>
          <input type="text" id="edit-expense-note" value="${expense.note || ''}" placeholder="${expense.type === 'wechat' ? '包含房租和水电费' : '添加备注(可选)'}">
        </div>
      </div>
    `;
    
    document.getElementById('modal-title-text').textContent = '编辑支出';
    document.getElementById('modal-body').innerHTML = formHtml;
    
    // 显示模态框
    document.getElementById('edit-modal').style.display = 'flex';
  }

  // 确认编辑
  confirmEdit() {
    if (!editExpenseId) return;
    
    const expense = expenses.find(e => e.id === editExpenseId);
    if (!expense) return;
    
    const type = document.getElementById('edit-expense-type').value;
    const amount = parseFloat(document.getElementById('edit-expense-amount').value);
    const date = document.getElementById('edit-expense-date').value;
    let note = document.getElementById('edit-expense-note').value;
    
    // 验证
    if (!type || !amount || !date) {
      this.showToast('请填写完整信息');
      return;
    }
    
    // 如果是微信支出且没有填写备注，自动添加包含房租水电的备注
    if (type === 'wechat' && !note) {
      note = '包含房租和水电费';
    }
    
    expense.type = type;
    expense.amount = amount;
    expense.date = date;
    expense.note = note;
    expense.updatedAt = new Date().toISOString();
    
    this.closeEditModal();
    this.renderExpenseList();
    this.updateTotalExpense();
    this.renderTrendChart();
    this.saveExpenses();
    
    // 如果当前有打开月份明细，更新它
    if (selectedMonth) {
      this.showMonthDetail(selectedMonth);
    }
    
    this.showToast('支出记录已更新');
  }

  // 删除支出
  deleteExpense(id) {
    deleteExpenseId = id;
    this.showConfirmModal('确定要删除此支出记录吗？', () => {
      expenses = expenses.filter(e => e.id !== id);
      this.renderExpenseList();
      this.updateTotalExpense();
      this.renderTrendChart();
      this.saveExpenses();
      
      // 如果当前有打开月份明细，更新它
      if (selectedMonth) {
        this.showMonthDetail(selectedMonth);
      }
      
      this.showToast('支出记录已删除');
    });
  }

  // 清空所有支出记录
  clearAllExpenses() {
    expenses = [];
    this.renderExpenseList();
    this.updateTotalExpense();
    this.renderTrendChart();
    this.saveExpenses();
    this.showToast('所有支出记录已清空');
  }

  // 确认删除
  confirmDelete() {
    if (typeof this.pendingDeleteCallback === 'function') {
      this.pendingDeleteCallback();
      this.pendingDeleteCallback = null;
    }
    this.closeConfirmModal();
  }

  // 显示确认弹窗
  showConfirmModal(message, callback) {
    document.getElementById('confirm-message').textContent = message;
    document.getElementById('confirm-modal').style.display = 'flex';
    this.pendingDeleteCallback = callback;
  }

  // 关闭编辑弹窗
  closeEditModal() {
    document.getElementById('edit-modal').style.display = 'none';
    editExpenseId = null;
  }

  // 关闭确认弹窗
  closeConfirmModal() {
    document.getElementById('confirm-modal').style.display = 'none';
    this.pendingDeleteCallback = null;
  }
  
  // 关闭月份明细弹窗
  closeMonthDetailModal() {
    document.getElementById('month-detail-modal').style.display = 'none';
    selectedMonth = null;
  }

  // 更新总支出
  updateTotalExpense() {
    const total = this.calculateCurrentMonthTotal();
    document.getElementById('total-expense').textContent = `¥ ${this.formatNumber(total)}`;
  }

  // 计算当前月份总支出
  calculateCurrentMonthTotal() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    return expenses
      .filter(expense => {
        const expenseDate = new Date(expense.date);
        return expenseDate.getFullYear() === currentYear && 
               expenseDate.getMonth() + 1 === currentMonth;
      })
      .reduce((total, expense) => total + expense.amount, 0);
  }

  // 渲染支出列表
  renderExpenseList() {
    const listEl = document.getElementById('expense-list');
    const filterValue = document.getElementById('expense-filter').value;
    
    // 根据筛选条件过滤记录
    let filteredExpenses = [...expenses];
    if (filterValue === 'current-month') {
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth() + 1;
      
      filteredExpenses = expenses.filter(expense => {
        const expenseDate = new Date(expense.date);
        return expenseDate.getFullYear() === currentYear && 
               expenseDate.getMonth() + 1 === currentMonth;
      });
      
      // 按日期降序排序
      filteredExpenses.sort((a, b) => new Date(b.date) - new Date(a.date));
      
      if (filteredExpenses.length === 0) {
        listEl.innerHTML = '<div class="empty-state">本月暂无支出记录</div>';
        return;
      }
      
      // 渲染当月支出列表（直接显示详细记录）
      const expenseItems = this.renderExpenseItems(filteredExpenses);
      listEl.innerHTML = expenseItems;
      
    } else if (filterValue === 'last-month') {
      const now = new Date();
      let lastMonthYear = now.getFullYear();
      let lastMonth = now.getMonth();
      if (lastMonth === 0) {
        lastMonth = 12;
        lastMonthYear--;
      }
      
      filteredExpenses = expenses.filter(expense => {
        const expenseDate = new Date(expense.date);
        return expenseDate.getFullYear() === lastMonthYear && 
               expenseDate.getMonth() + 1 === lastMonth;
      });
      
      // 按日期降序排序
      filteredExpenses.sort((a, b) => new Date(b.date) - new Date(a.date));
      
      if (filteredExpenses.length === 0) {
        listEl.innerHTML = '<div class="empty-state">上月暂无支出记录</div>';
        return;
      }
      
      // 渲染上月支出列表（直接显示详细记录）
      const expenseItems = this.renderExpenseItems(filteredExpenses);
      listEl.innerHTML = expenseItems;
      
    } else {
      // 全部记录 - 按月份聚合
      if (expenses.length === 0) {
        listEl.innerHTML = '<div class="empty-state">暂无支出记录</div>';
        return;
      }
      
      // 按月份聚合数据
      const monthlyData = this.getMonthlyExpenses();
      
      if (Object.keys(monthlyData).length === 0) {
        listEl.innerHTML = '<div class="empty-state">暂无支出记录</div>';
        return;
      }
      
      // 渲染月份卡片
      const monthCards = Object.keys(monthlyData)
        .sort((a, b) => new Date(b) - new Date(a)) // 降序排序月份
        .map(month => {
          const { expenses: monthExpenses, total } = monthlyData[month];
          const [year, monthNum] = month.split('-');
          
          // 计算该月各支付方式占比
          const typeStats = {};
          monthExpenses.forEach(expense => {
            if (!typeStats[expense.type]) {
              typeStats[expense.type] = 0;
            }
            typeStats[expense.type] += expense.amount;
          });
          
          // 找出最多的两个支付方式
          const sortedTypes = Object.entries(typeStats)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 2);
          
          return `
            <div class="month-card" onclick="accountingSystem.showMonthDetail('${month}')">
              <div class="month-card-header">
                <div class="month-name">${year}年${monthNum}月</div>
                <div class="month-total">¥ ${this.formatNumber(total)}</div>
              </div>
              <div class="month-stats">
                ${sortedTypes.map(([type, amount]) => `
                  <div class="month-stat-item">
                    <div class="month-stat-label">
                      <i class="fas ${EXPENSE_TYPES[type]?.icon || 'fa-question'}"></i> ${EXPENSE_TYPES[type]?.name || '其他'}
                    </div>
                    <div class="month-stat-value">¥ ${this.formatNumber(amount)}</div>
                  </div>
                `).join('')}
              </div>
            </div>
          `;
        }).join('');
        
      listEl.innerHTML = monthCards;
    }
  }
  
  // 渲染单个支出项
  renderExpenseItems(items) {
    return items.map(expense => {
      const { type, amount, date, note, id } = expense;
      const expenseType = EXPENSE_TYPES[type] || { name: '未知类型', icon: 'fa-question', color: '#999' };
      
      return `
        <div class="expense-item">
          <div class="expense-item-type">
            <span style="color:${expenseType.color}">
              <i class="fas ${expenseType.icon}"></i>
            </span> 
            ${expenseType.name}
          </div>
          <div class="expense-item-details">
            <div class="expense-item-date">
              <i class="fas fa-calendar-alt"></i> ${this.formatDate(date)}
            </div>
          </div>
          <div class="expense-item-amount">¥ ${this.formatNumber(amount)}</div>
          ${note ? `<div class="expense-item-note">
            <i class="fas fa-sticky-note"></i> ${note}
          </div>` : ''}
          <div class="expense-item-actions">
            <button class="btn-edit" onclick="accountingSystem.editExpense('${id}')">
              <i class="fas fa-edit"></i>
            </button>
            <button class="btn-delete" onclick="accountingSystem.deleteExpense('${id}')">
              <i class="fas fa-trash-alt"></i>
            </button>
          </div>
        </div>
      `;
    }).join('');
  }
  
  // 显示月份详情
  showMonthDetail(month) {
    selectedMonth = month;
    const [year, monthNum] = month.split('-');
    const monthlyData = this.getMonthlyExpenses()[month];
    
    if (!monthlyData) {
      this.showToast('找不到该月份的数据');
      return;
    }
    
    // 设置标题
    document.getElementById('month-detail-title').textContent = `${year}年${monthNum}月支出明细`;
    
    // 准备内容
    const { expenses: monthExpenses, total } = monthlyData;
    
    // 按支付类型统计
    const typeSummary = {};
    monthExpenses.forEach(expense => {
      if (!typeSummary[expense.type]) {
        typeSummary[expense.type] = 0;
      }
      typeSummary[expense.type] += expense.amount;
    });
    
    // 按日期降序排序
    monthExpenses.sort((a, b) => new Date(b.date) - new Date(a.date));
    
    const detailHtml = `
      <div class="month-detail-header">
        <div class="month-detail-title">${year}年${monthNum}月总支出</div>
        <div class="month-detail-total">¥ ${this.formatNumber(total)}</div>
      </div>
      
      <div class="month-detail-summary">
        ${Object.entries(typeSummary).map(([type, amount]) => {
          const expenseType = EXPENSE_TYPES[type] || { name: '未知类型', icon: 'fa-question', color: '#999' };
          return `
            <div class="month-stat-item">
              <div class="month-stat-label">
                <i class="fas ${expenseType.icon}" style="color:${expenseType.color}"></i> ${expenseType.name}
              </div>
              <div class="month-stat-value">¥ ${this.formatNumber(amount)}</div>
            </div>
          `;
        }).join('')}
      </div>
      
      <div class="month-detail-list">
        ${monthExpenses.map(expense => {
          const { id, type, amount, date, note } = expense;
          const expenseType = EXPENSE_TYPES[type] || { name: '未知类型', icon: 'fa-question', color: '#999' };
          
          return `
            <div class="month-detail-item">
              <div class="month-detail-type">
                <div class="month-detail-icon" style="background-color:${expenseType.color}; color: white;">
                  <i class="fas ${expenseType.icon}"></i>
                </div>
                <div class="month-detail-name">${expenseType.name}</div>
              </div>
              
              <div class="month-detail-info">
                <div class="month-detail-date">${this.formatDate(date)}</div>
                ${note ? `<div class="month-detail-note">${note}</div>` : ''}
              </div>
              
              <div class="month-detail-amount">¥ ${this.formatNumber(amount)}</div>
              
              <div class="month-detail-actions">
                <button class="btn-edit" onclick="accountingSystem.editExpense('${id}'); accountingSystem.closeMonthDetailModal();">
                  <i class="fas fa-edit"></i>
                </button>
                <button class="btn-delete" onclick="accountingSystem.deleteExpense('${id}')">
                  <i class="fas fa-trash-alt"></i>
                </button>
              </div>
            </div>
          `;
        }).join('')}
      </div>
    `;
    
    document.getElementById('month-detail-content').innerHTML = detailHtml;
    document.getElementById('month-detail-modal').style.display = 'flex';
  }

  // 渲染趋势图
  renderTrendChart() {
    // 如果图表存在，先销毁
    if (trendChart) {
      trendChart.destroy();
    }
    
    const ctx = document.getElementById('expense-trend-chart');
    
    // 根据选择的时间范围获取数据
    const { labels, datasets } = this.getChartData();
    
    // 初始化图表
    trendChart = new Chart(ctx, {
      type: chartType,
      data: {
        labels: labels,
        datasets: datasets
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          x: {
            grid: {
              display: false
            }
          },
          y: {
            beginAtZero: true,
            ticks: {
              callback: (value) => `¥${value}`
            },
            grid: {
              color: 'rgba(0, 0, 0, 0.05)'
            }
          }
        },
        interaction: {
          mode: 'index',
          intersect: false
        },
        plugins: {
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleFont: {
              size: 14
            },
            bodyFont: {
              size: 13
            },
            callbacks: {
              label: (context) => {
                let label = context.dataset.label || '';
                if (label) {
                  label += ': ';
                }
                label += `¥${this.formatNumber(context.parsed.y)}`;
                return label;
              },
              title: (tooltipItems) => {
                return tooltipItems[0].label + ' 支出明细';
              }
            },
            padding: 12,
            cornerRadius: 8
          },
          legend: {
            display: false,
            position: 'bottom',
            labels: {
              font: {
                size: 12
              },
              usePointStyle: true,
              padding: 20
            }
          },
          title: {
            display: false,
            text: '月度支出趋势'
          }
        }
      }
    });

    // 生成自定义图例
    this.renderChartLegend(datasets);
  }
  
  // 渲染自定义图例
  renderChartLegend(datasets) {
    const legendEl = document.getElementById('chart-legend');
    legendEl.innerHTML = '';
    
    if (datasets.length > 1) {
      const legendItems = datasets.map((dataset, index) => `
        <div class="chart-legend-item" data-index="${index}">
          <div class="chart-legend-color" style="background-color: ${dataset.backgroundColor}"></div>
          <div class="chart-legend-label">${dataset.label}</div>
        </div>
      `).join('');
      
      legendEl.innerHTML = legendItems;
      
      // 添加事件监听，点击图例可以切换数据集的可见性
      document.querySelectorAll('.chart-legend-item').forEach(item => {
        item.addEventListener('click', () => {
          const index = parseInt(item.dataset.index);
          const ci = trendChart;
          const meta = ci.getDatasetMeta(index);
          
          // 切换可见性
          meta.hidden = meta.hidden === null ? !ci.data.datasets[index].hidden : null;
          
          // 更新图表和图例样式
          ci.update();
          if (meta.hidden) {
            item.classList.add('inactive');
          } else {
            item.classList.remove('inactive');
          }
        });
      });
    } else if (datasets.length === 1) {
      legendEl.innerHTML = `
        <div class="chart-legend-item">
          <div class="chart-legend-color" style="background-color: ${datasets[0].backgroundColor}"></div>
          <div class="chart-legend-label">${datasets[0].label}</div>
        </div>
      `;
    }
  }

  // 获取图表数据
  getChartData() {
    // 初始化结果
    let labels = [];
    let result = {};
    
    // 获取日期范围
    const now = new Date();
    let startMonth = new Date(now.getFullYear(), now.getMonth() - chartPeriod + 1, 1);
    
    // 按月聚合数据
    const monthlyData = {};
    const typeMonthlyData = {};
    
    // 按时间和类型分组
    expenses.forEach(expense => {
      const expDate = new Date(expense.date);
      // 只处理范围内的数据
      if (expDate >= startMonth) {
        const year = expDate.getFullYear();
        const month = expDate.getMonth() + 1;
        const monthKey = `${year}-${String(month).padStart(2, '0')}`;
        
        // 初始化月份数据
        if (!monthlyData[monthKey]) {
          monthlyData[monthKey] = 0;
        }
        
        // 初始化类型月份数据
        if (!typeMonthlyData[expense.type]) {
          typeMonthlyData[expense.type] = {};
        }
        
        if (!typeMonthlyData[expense.type][monthKey]) {
          typeMonthlyData[expense.type][monthKey] = 0;
        }
        
        // 累计总金额
        monthlyData[monthKey] += expense.amount;
        
        // 累计类型金额
        typeMonthlyData[expense.type][monthKey] += expense.amount;
      }
    });
    
    // 生成标签数组
    for (let i = 0; i < chartPeriod; i++) {
      const date = new Date(now.getFullYear(), now.getMonth() - chartPeriod + 1 + i, 1);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const monthStr = `${year}/${String(month).padStart(2, '0')}`;
      labels.push(monthStr);
    }
    
    // 创建数据集
    const datasets = [];
    
    if (chartType === 'bar') {
      // 堆叠柱状图 - 按支出类型显示
      Object.keys(EXPENSE_TYPES).forEach(type => {
        if (typeMonthlyData[type]) {
          const data = labels.map(label => {
            const [year, month] = label.split('/');
            const monthKey = `${year}-${month}`;
            return typeMonthlyData[type][monthKey] || 0;
          });
          
          datasets.push({
            label: EXPENSE_TYPES[type].name,
            data: data,
            backgroundColor: CHART_COLORS[type],
            borderColor: CHART_COLORS.border[type],
            borderWidth: 1
          });
        }
      });
      
      // 如果没有数据，使用默认总计展示
      if (datasets.length === 0) {
        const data = labels.map(label => {
          const [year, month] = label.split('/');
          const monthKey = `${year}-${month}`;
          return monthlyData[monthKey] || 0;
        });
        
        datasets.push({
          label: '月度支出',
          data: data,
          backgroundColor: CHART_COLORS.primary,
          borderColor: CHART_COLORS.primaryBorder,
          borderWidth: 1
        });
      }
      
    } else {
      // 折线图 - 显示总趋势
      const totalData = labels.map(label => {
        const [year, month] = label.split('/');
        const monthKey = `${year}-${month}`;
        return monthlyData[monthKey] || 0;
      });
      
      datasets.push({
        label: '月度总支出',
        data: totalData,
        borderColor: CHART_COLORS.primaryBorder,
        backgroundColor: 'rgba(63, 81, 181, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.3,
        pointBackgroundColor: CHART_COLORS.primaryBorder,
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      });
      
      // 添加各类型支出的折线
      Object.keys(EXPENSE_TYPES).forEach(type => {
        if (typeMonthlyData[type]) {
          const data = labels.map(label => {
            const [year, month] = label.split('/');
            const monthKey = `${year}-${month}`;
            return typeMonthlyData[type][monthKey] || 0;
          });
          
          // 确认是否有非零数据
          if (data.some(value => value > 0)) {
            datasets.push({
              label: EXPENSE_TYPES[type].name,
              data: data,
              borderColor: CHART_COLORS.border[type],
              backgroundColor: 'transparent',
              borderWidth: 2,
              tension: 0.3,
              pointBackgroundColor: CHART_COLORS.border[type],
              pointBorderColor: '#fff',
              pointBorderWidth: 1.5,
              pointRadius: 3,
              pointHoverRadius: 5
            });
          }
        }
      });
    }
    
    return { labels, datasets };
  }
  
  // 获取按月份分组的支出数据
  getMonthlyExpenses() {
    const monthlyData = {};
    
    // 按月份分组
    expenses.forEach(expense => {
      const expDate = new Date(expense.date);
      const year = expDate.getFullYear();
      const month = expDate.getMonth() + 1;
      const monthKey = `${year}-${String(month).padStart(2, '0')}`;
      
      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          expenses: [],
          total: 0
        };
      }
      
      monthlyData[monthKey].expenses.push(expense);
      monthlyData[monthKey].total += expense.amount;
    });
    
    return monthlyData;
  }

  // 保存支出记录到本地存储
  saveExpenses() {
    localStorage.setItem('accountingExpenses', JSON.stringify(expenses));
  }

  // 从本地存储加载支出记录
  loadExpenses() {
    const saved = localStorage.getItem('accountingExpenses');
    expenses = saved ? JSON.parse(saved) : [];
  }

  // 生成唯一ID
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
  }

  // 格式化日期
  formatDate(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // 格式化数字
  formatNumber(num, decimals = 2) {
    return parseFloat(num).toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // 显示提示消息
  showToast(message) {
    const toast = document.getElementById('toast');
    toast.textContent = message;
    toast.style.opacity = '1';
    
    setTimeout(() => {
      toast.style.opacity = '0';
    }, 3000);
  }
}

// 创建记账系统实例
const accountingSystem = new AccountingSystem();

// 暴露到全局，方便HTML调用
window.accountingSystem = accountingSystem; 