<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限初始化配置页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1.1rem;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .permission-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .permission-item {
            display: grid;
            grid-template-columns: 80px 120px 2fr 2fr 40px 80px;
            gap: 15px;
            align-items: center;
            margin-bottom: 10px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            position: relative;
        }

        .child-permissions-area {
            grid-column: 1 / -1;
            margin-top: 10px;
            display: none;
        }

        .child-permissions-area.show {
            display: block;
        }

        .child-permission-item {
            display: grid;
            grid-template-columns: 120px 2fr 2fr 40px 80px;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border-left: 3px solid #28a745;
            position: relative;
        }

        .child-permission-item .child-permissions-area {
            grid-column: 1 / -1;
            margin-top: 10px;
            margin-left: 20px;
        }

        .child-permission-item:last-child {
            margin-bottom: 0;
        }

        .permission-item.level-1 {
            margin-left: 0;
            border-left: 4px solid #667eea;
        }

        .permission-item.level-2 {
            margin-left: 20px;
            border-left: 4px solid #56ab2f;
        }

        .permission-item.level-3 {
            margin-left: 40px;
            border-left: 4px solid #ff9500;
        }

        .permission-item.level-4 {
            margin-left: 60px;
            border-left: 4px solid #ff416c;
        }

        .permission-item.level-5 {
            margin-left: 80px;
            border-left: 4px solid #e91e63;
        }

        .permission-item.level-6 {
            margin-left: 100px;
            border-left: 4px solid #9c27b0;
        }

        .permission-item.level-7 {
            margin-left: 120px;
            border-left: 4px solid #673ab7;
        }

        .permission-item.level-8 {
            margin-left: 140px;
            border-left: 4px solid #3f51b5;
        }

        .permission-item.level-9 {
            margin-left: 160px;
            border-left: 4px solid #2196f3;
        }

        .permission-item.level-10 {
            margin-left: 180px;
            border-left: 4px solid #00bcd4;
        }



        .permission-level-select {
            font-size: 0.9rem;
        }

        .parent-permission-select {
            font-size: 0.9rem;
        }

        .permission-item:last-child {
            margin-bottom: 0;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 65, 108, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            font-size: 0.9rem;
            padding: 8px 15px;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .btn-add-child {
            background: #28a745;
            color: white;
            font-size: 0.8rem;
            padding: 6px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-add-child:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .btn-add-child:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .btn-add-child.active {
            background: #dc3545;
        }

        .btn-add-child.active:hover {
            background: #c82333;
        }

        .btn-small {
            font-size: 0.8rem;
            padding: 4px 8px;
        }

        .btn-plus {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            background: #28a745;
            color: white;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .btn-plus:hover {
            background: #218838;
            transform: scale(1.1);
        }

        .permission-level-display {
            font-size: 0.9rem;
            font-weight: 600;
            color: #495057;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 4px;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
        }

        .sql-output {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 25px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .system-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 8px 8px 0;
        }

        .system-info h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .system-info p {
            color: #424242;
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .permission-item {
                grid-template-columns: 1fr;
                gap: 10px;
                margin-left: 0 !important;
                padding: 10px;
            }
            
            .btn-add-child {
                font-size: 0.7rem;
                padding: 4px 8px;
            }
            
            .btn-plus {
                width: 25px;
                height: 25px;
                font-size: 1rem;
            }
            
            .permission-item.level-2 {
                margin-left: 10px !important;
            }

            .permission-item.level-3 {
                margin-left: 20px !important;
            }

            .permission-item.level-4 {
                margin-left: 30px !important;
            }
            
            .collapse-btn {
                width: 20px;
                height: 20px;
                font-size: 0.8rem;
            }
            
            .button-group {
                justify-content: center;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }

        /* Tab样式 */
        .tab-container {
            margin-bottom: 25px;
        }

        .tab-header {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 0;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
            overflow: hidden;
        }

        .tab-button {
            background: none;
            border: none;
            padding: 15px 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
            flex: 1;
            justify-content: center;
        }

        .tab-button:hover {
            background: linear-gradient(135deg, rgba(25, 118, 210, 0.08), rgba(156, 39, 176, 0.08));
            color: #1976d2;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
        }

        .tab-button.active {
            color: #1976d2;
            border-bottom-color: #1976d2;
            background: linear-gradient(135deg, rgba(25, 118, 210, 0.12), rgba(156, 39, 176, 0.12));
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
        }

        .tab-button.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1976d2, #9c27b0);
        }

        .tab-icon {
            font-size: 18px;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
            transition: transform 0.3s ease;
        }

        .tab-button:hover .tab-icon {
            transform: scale(1.1);
        }

        .tab-button.active .tab-icon {
            transform: scale(1.05);
        }

        .tab-content {
            animation: fadeInUp 0.4s ease-out;
            background: white;
            border-radius: 0 0 8px 8px;
            border: 1px solid #e9ecef;
            border-top: none;
            padding: 20px;
            margin-top: -2px;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式Tab设计 */
        @media (max-width: 768px) {
            .tab-button {
                padding: 12px 15px;
                font-size: 14px;
                flex-direction: column;
                gap: 4px;
            }
            
            .tab-icon {
                font-size: 16px;
            }
            
            .tab-header {
                border-radius: 6px 6px 0 0;
            }
            
                         .tab-content {
                 border-radius: 0 0 6px 6px;
                 padding: 15px;
             }
         }

         /* 配置分组样式 */
         .config-group {
             background: #ffffff;
             border: 1px solid #e9ecef;
             border-radius: 12px;
             margin-bottom: 25px;
             box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
             overflow: hidden;
             transition: all 0.3s ease;
         }

         .config-group:hover {
             box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
             transform: translateY(-2px);
         }

         .config-group-header {
             background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
             padding: 20px 25px;
             border-bottom: 1px solid #dee2e6;
             display: flex;
             align-items: center;
             gap: 15px;
         }

         .config-group-icon {
             font-size: 24px;
             width: 45px;
             height: 45px;
             background: linear-gradient(135deg, #1976d2, #1565c0);
             border-radius: 50%;
             display: flex;
             align-items: center;
             justify-content: center;
             box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);
         }

         .config-group-title {
             color: #2c3e50;
             font-size: 18px;
             font-weight: 600;
             margin: 0;
             flex: 1;
         }

         .config-group-desc {
             color: #6c757d;
             font-size: 14px;
             margin: 0;
             margin-left: auto;
             font-style: italic;
         }

         .config-group-content {
             padding: 25px;
             background: #fafbfc;
         }

         /* 必填字段样式 */
         .required {
             color: #dc3545;
             font-weight: bold;
         }

         /* 增强表单样式 */
         .config-group .form-control {
             border: 1px solid #ced4da;
             border-radius: 8px;
             padding: 12px 15px;
             font-size: 14px;
             transition: all 0.3s ease;
             background: #ffffff;
         }

         .config-group .form-control:focus {
             border-color: #1976d2;
             box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
             background: #ffffff;
         }

         .config-group .form-group label {
             font-weight: 500;
             color: #495057;
             margin-bottom: 8px;
         }

         .config-group .form-text {
             color: #6c757d;
             font-size: 12px;
             margin-top: 5px;
         }

         /* 响应式配置分组 */
         @media (max-width: 768px) {
             .config-group-header {
                 padding: 15px 20px;
                 flex-direction: column;
                 text-align: center;
                 gap: 10px;
             }

             .config-group-icon {
                 width: 40px;
                 height: 40px;
                 font-size: 20px;
             }

             .config-group-title {
                 font-size: 16px;
             }

             .config-group-desc {
                 margin-left: 0;
                 font-size: 13px;
             }

             .config-group-content {
                 padding: 20px 15px;
             }

                           .config-group {
                  margin-bottom: 20px;
              }
          }

          /* 权限配置增强样式 */
          .permission-actions {
              margin-top: 20px;
              padding-top: 20px;
              border-top: 1px solid #dee2e6;
              text-align: center;
          }

          .permission-actions .btn {
              padding: 10px 20px;
              font-weight: 500;
              border-radius: 8px;
              display: inline-flex;
              align-items: center;
              gap: 8px;
          }

          /* 操作按钮组样式 */
          .action-group {
              margin: 30px 0;
              text-align: center;
          }

          .action-buttons {
              display: flex;
              gap: 15px;
              justify-content: center;
              flex-wrap: wrap;
          }

          .btn-large {
              padding: 15px 30px;
              font-size: 16px;
              font-weight: 600;
              border-radius: 10px;
              display: inline-flex;
              align-items: center;
              gap: 10px;
              transition: all 0.3s ease;
              min-width: 160px;
              justify-content: center;
          }

          .btn-large:hover {
              transform: translateY(-2px);
              box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
          }

          .btn-icon {
              font-size: 18px;
              filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
          }

          /* 优化权限列表在配置组中的样式 */
          .config-group-content .permission-list {
              background: #ffffff;
              border: 1px solid #e9ecef;
              border-radius: 8px;
              padding: 15px;
              margin-bottom: 0;
          }

          .config-group-content .permission-item {
              background: #f8f9fa;
              border: 1px solid #dee2e6;
              border-radius: 6px;
              margin-bottom: 10px;
              padding: 15px;
          }

          .config-group-content .permission-item:last-child {
              margin-bottom: 0;
          }

          /* 超管账号页面的权限列表样式 */
          .permission-list-admin {
              background: #ffffff;
              border: 1px solid #e9ecef;
              border-radius: 8px;
              padding: 15px;
              margin: 20px 0;
          }

          .permission-list-admin .permission-item {
              background: #f8f9fa;
              border: 1px solid #dee2e6;
              border-radius: 6px;
              margin-bottom: 10px;
              padding: 15px;
          }

          .permission-list-admin .permission-item:last-child {
              margin-bottom: 0;
          }

          /* 响应式操作按钮 */
          @media (max-width: 768px) {
              .action-buttons {
                  flex-direction: column;
                  align-items: center;
              }

              .btn-large {
                  width: 100%;
                  max-width: 280px;
              }

              .permission-actions .btn {
                  width: 100%;
                  max-width: 200px;
              }
          }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 权限初始化配置页面</h1>
            <p>为不同系统环境快速生成权限初始化SQL语句</p>
        </div>
        
        <div class="content">
            <div class="tab-container">
                <div class="tab-header">
                    <button class="tab-button active" onclick="switchTab('permission')" id="permissionTab">
                        <i class="tab-icon">🔐</i>
                        初始化权限数据
                    </button>
                    <button class="tab-button" onclick="switchTab('admin')" id="adminTab">
                        <i class="tab-icon">👤</i>
                        初始化超管账号
                    </button>
                </div>
            </div>

            <div id="basicConfigSection">
                <div class="form-row">
                    <div class="form-group">
                        <label for="systemSelect">选择系统：</label>
                        <select id="systemSelect" class="form-control" onchange="updateSystemInfo()">
                                                <option value="">请选择系统</option>
                        <option value="sky_op">撮合平台-运营中心</option>
                        <option value="unifly_customer">一网统飞客户工作台</option>
                        <option value="unifly_op">一网统飞-运营中心</option>
                        <option value="traffic_police">交警系统</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="envSelect">选择环境：</label>
                        <select id="envSelect" class="form-control" onchange="updateSystemInfo()">
                            <option value="">请选择环境</option>
                            <option value="dev">开发环境</option>
                            <option value="prod">生产环境</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="superAdminRoleInput">超管角色Code：</label>
                    <input type="text" id="superAdminRoleInput" class="form-control" placeholder="请输入超管角色code (例如：sky_op_super_admin)" oninput="updateSystemInfoDisplay()" />
                </div>
            </div>

            <div id="permissionModeSection" class="tab-content">
                <!-- 权限配置分组 -->
                <div class="config-group">
                    <div class="config-group-header">
                        <div class="config-group-icon">⚙️</div>
                        <h3 class="config-group-title">权限配置</h3>
                        <p class="config-group-desc">权限生成基础设置</p>
                    </div>
                    <div class="config-group-content">
                        <div class="form-group">
                            <label for="startPermissionId">权限起始ID：</label>
                            <input type="number" id="startPermissionId" class="form-control" placeholder="请输入权限起始ID（如：1000）" value="1000" min="1" />
                            <small class="form-text text-muted">权限ID将从此值开始递增生成</small>
                        </div>
                    </div>
                </div>
            </div>

            <div id="adminModeSection" class="tab-content" style="display: none;">
                <!-- 系统配置分组 -->
                <div class="config-group">
                    <div class="config-group-header">
                        <div class="config-group-icon">🏢</div>
                        <h3 class="config-group-title">系统配置</h3>
                        <p class="config-group-desc">基础系统配置信息</p>
                    </div>
                    <div class="config-group-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="tenantIdInput">租户ID：</label>
                                <input type="text" id="tenantIdInput" class="form-control" placeholder="请输入租户ID（如：sz_unifly）" />
                                <small class="form-text text-muted">交警系统请留空</small>
                            </div>
                            <div class="form-group">
                                <label for="sceneType">场景类型：</label>
                                <input type="text" id="sceneType" class="form-control" placeholder="请输入场景类型（如：operation_center）" />
                                <small class="form-text text-muted">常用值：supplier_dashboard(服务商工作台)、customer_dashboard(客户工作台)、operation_center(运营中心)、traffic_police(交警业务)、spatiotemporal_openprod(开放平台)、open_operation_platform(运营开放平台)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户信息分组 -->
                <div class="config-group">
                    <div class="config-group-header">
                        <div class="config-group-icon">👤</div>
                        <h3 class="config-group-title">用户信息</h3>
                        <p class="config-group-desc">超级管理员用户基本信息</p>
                    </div>
                    <div class="config-group-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="userNo">用户编号 <span class="required">*</span>：</label>
                                <input type="text" id="userNo" class="form-control" placeholder="请输入用户编号（如：uid_sky_unifly_01）" />
                            </div>
                            <div class="form-group">
                                <label for="userName">用户名称：</label>
                                <input type="text" id="userName" class="form-control" placeholder="请输入用户名称（如：超级管理员账号）" />
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="userPhone">用户手机号：</label>
                                <input type="text" id="userPhone" class="form-control" placeholder="请输入手机号（如：18069859083）" />
                            </div>
                            <div class="form-group">
                                <label for="userType">用户类型：</label>
                                <input type="text" id="userType" class="form-control" placeholder="请输入用户类型（如：operation）" />
                                <small class="form-text text-muted">常用值：supplier(服务商)、customer(客户)、operation(运营平台)、spatiotemporal_openprod(开放平台)、open_operation_platform_operation(开放运营平台)</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 账号信息分组 -->
                <div class="config-group">
                    <div class="config-group-header">
                        <div class="config-group-icon">🔑</div>
                        <h3 class="config-group-title">账号信息</h3>
                        <p class="config-group-desc">登录账号和认证信息</p>
                    </div>
                    <div class="config-group-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="accountNo">账号编号 <span class="required">*</span>：</label>
                                <input type="text" id="accountNo" class="form-control" placeholder="请输入账号编号（如：aid_sky_unifly_01）" />
                            </div>
                            <div class="form-group">
                                <label for="accountLogin">登录账号：</label>
                                <input type="text" id="accountLogin" class="form-control" placeholder="请输入登录账号（通常为手机号）" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="accountPassword">账号密码（加密后）：</label>
                            <input type="text" id="accountPassword" class="form-control" placeholder="请输入加密后的密码" />
                            <small class="form-text text-muted">请输入经过加密处理的密码字符串</small>
                        </div>
                    </div>
                </div>

                <!-- 角色信息分组 -->
                <div class="config-group">
                    <div class="config-group-header">
                        <div class="config-group-icon">⚡</div>
                        <h3 class="config-group-title">角色信息</h3>
                        <p class="config-group-desc">超级管理员角色定义</p>
                    </div>
                    <div class="config-group-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="roleCode">角色代码 <span class="required">*</span>：</label>
                                <input type="text" id="roleCode" class="form-control" placeholder="请输入角色代码（如：sz_unifly_super_admin）" />
                            </div>
                            <div class="form-group">
                                <label for="roleName">角色名称：</label>
                                <input type="text" id="roleName" class="form-control" placeholder="请输入角色名称（如：超级管理员）" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="roleDesc">角色描述：</label>
                            <input type="text" id="roleDesc" class="form-control" placeholder="请输入角色描述（如：系统超级管理员，拥有所有权限）" />
                        </div>
                    </div>
                </div>

                <!-- 权限配置分组 -->
                <div class="config-group">
                    <div class="config-group-header">
                        <div class="config-group-icon">🔐</div>
                        <h3 class="config-group-title">权限配置</h3>
                        <p class="config-group-desc">为超管角色配置权限数据</p>
                    </div>
                    <div class="config-group-content">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="adminStartPermissionId">权限起始ID：</label>
                                <input type="number" id="adminStartPermissionId" class="form-control" placeholder="请输入权限起始ID（如：1000）" value="1000" min="1" />
                                <small class="form-text text-muted">权限ID将从此值开始递增生成</small>
                            </div>
                            <div class="form-group">
                                <label for="permissionPrefix">权限前缀：</label>
                                <input type="text" id="permissionPrefix" class="form-control" placeholder="请输入权限前缀（如：skyOp:，交警系统留空）" />
                                <small class="form-text text-muted">交警系统请留空，其他系统请填写对应前缀</small>
                            </div>
                        </div>
                        
                        <div class="permission-list-admin" id="permissionListAdmin">
                            <div class="permission-item level-1" data-permission-id="admin-perm-1" data-level="1">
                                <button class="btn-add-child" onclick="toggleChildPermissionAreaAdmin(this)">+子权限</button>
                                <div class="permission-level-display">一级权限</div>
                                <input type="text" class="form-control permission-code" placeholder="权限代码 (不含前缀)" oninput="updatePermissionStructureAdmin()" />
                                <input type="text" class="form-control permission-name" placeholder="权限名称" oninput="updatePermissionStructureAdmin()" />
                                <button type="button" class="btn-plus" onclick="addSameLevelPermissionAdmin(this)" title="添加同级权限">+</button>
                                <button type="button" class="btn btn-secondary" onclick="removePermissionAdmin(this)">删除</button>
                                
                                <div class="child-permissions-area">
                                    <div class="child-permissions-list">
                                        <!-- 子权限项将在这里动态添加 -->
                                    </div>
                                    <button type="button" class="btn btn-success btn-small" onclick="addChildPermissionItemAdmin(this)">添加子权限</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="permission-actions">
                            <button type="button" class="btn btn-success" onclick="addPermissionAdmin()">
                                <i class="btn-icon">➕</i> 添加权限
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="systemInfo" class="system-info" style="display: none;">
                <h3>当前系统配置信息</h3>
                <p><strong>租户ID：</strong><span id="tenantId"></span></p>
                <p><strong>场景：</strong><span id="scene"></span></p>
                <p><strong>默认超管角色：</strong><span id="defaultSuperAdminRole" style="color: #1976d2; font-weight: 500;"></span></p>
                <p><strong>当前超管角色：</strong><span id="superAdminRole"></span></p>
                <p><strong>权限前缀：</strong><span id="permissionPrefix"></span></p>
            </div>

            <div id="permissionConfigSection">
                <!-- 权限结构分组 -->
                <div class="config-group">
                    <div class="config-group-header">
                        <div class="config-group-icon">🔐</div>
                        <h3 class="config-group-title">权限结构设计</h3>
                        <p class="config-group-desc">设计多级权限层次结构</p>
                    </div>
                    <div class="config-group-content">
                        <div class="permission-list" id="permissionList">
                            <div class="permission-item level-1" data-permission-id="perm-1" data-level="1">
                                <button class="btn-add-child" onclick="toggleChildPermissionArea(this)">+子权限</button>
                                <div class="permission-level-display">一级权限</div>
                                <input type="text" class="form-control permission-code" placeholder="权限代码 (不含前缀)" oninput="updatePermissionStructure()" />
                                <input type="text" class="form-control permission-name" placeholder="权限名称" oninput="updatePermissionStructure()" />
                                <button type="button" class="btn-plus" onclick="addSameLevelPermission(this)" title="添加同级权限">+</button>
                                <button type="button" class="btn btn-secondary" onclick="removePermission(this)">删除</button>
                                
                                <div class="child-permissions-area">
                                    <div class="child-permissions-list">
                                        <!-- 子权限项将在这里动态添加 -->
                                    </div>
                                    <button type="button" class="btn btn-success btn-small" onclick="addChildPermissionItem(this)">添加子权限</button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="permission-actions">
                            <button type="button" class="btn btn-success" onclick="addPermission()">
                                <i class="btn-icon">➕</i> 添加权限
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="action-group">
                <div class="action-buttons">
                    <button type="button" class="btn btn-primary btn-large" onclick="generateSQL()">
                        <i class="btn-icon">🚀</i> 生成SQL
                    </button>
                    <button type="button" class="btn btn-secondary btn-large" onclick="clearAll()">
                        <i class="btn-icon">🗑️</i> 清空所有
                    </button>
                </div>
            </div>

            <div id="sqlOutput" class="sql-output" style="display: none;"></div>
            
            <div id="copyButton" style="display: none; margin-top: 15px;">
                <button type="button" class="btn btn-primary" onclick="copyToClipboard()">📋 复制SQL到剪贴板</button>
            </div>
        </div>
    </div>

    <script>
        // 系统配置信息
        const systemConfigs = {
            sky_op: {
                name: '撮合平台-运营中心',
                dev: { tenantId: 'deepinnet', scene: 'operation_center', defaultSuperAdminRole: 'sky_op_super_admin', prefix: 'skyOp:' },
                prod: { tenantId: 'sz_lg', scene: 'operation_center', defaultSuperAdminRole: 'sky_op_super_admin', prefix: 'skyOp:' }
            },
            unifly_customer: {
                name: '一网统飞客户工作台',
                dev: { tenantId: 'sz_unifly', scene: 'customer_dashboard', defaultSuperAdminRole: 'sz_unifly_super_admin', prefix: 'szUnifly:' },
                prod: { tenantId: 'sz_unifly', scene: 'customer_dashboard', defaultSuperAdminRole: 'sz_unifly_super_admin', prefix: 'szUnifly:' }
            },
            unifly_op: {
                name: '一网统飞-运营中心',
                dev: { tenantId: 'sz_unifly', scene: 'operation_center', defaultSuperAdminRole: 'unifly_sky_op_super_admin', prefix: 'uniflySkyOp:' },
                prod: { tenantId: 'sz_unifly', scene: 'operation_center', defaultSuperAdminRole: 'unifly_sky_op_super_admin', prefix: 'uniflySkyOp:' }
            },
            traffic_police: {
                name: '交警系统',
                dev: { tenantId: null, scene: 'traffic_police', defaultSuperAdminRole: 'super_admin', prefix: '' },
                prod: { tenantId: null, scene: 'traffic_police', defaultSuperAdminRole: 'super_admin', prefix: '' }
            }
        };

        function updateSystemInfo() {
            const systemSelect = document.getElementById('systemSelect');
            const envSelect = document.getElementById('envSelect');
            const superAdminRoleInput = document.getElementById('superAdminRoleInput');
            
            if (systemSelect.value && envSelect.value) {
                const config = systemConfigs[systemSelect.value][envSelect.value];
                // 自动填充当前系统的默认超管角色code
                superAdminRoleInput.value = config.defaultSuperAdminRole;
                updateSystemInfoDisplay();
            } else {
                document.getElementById('systemInfo').style.display = 'none';
            }
        }

        function updateSystemInfoDisplay() {
            const systemSelect = document.getElementById('systemSelect');
            const envSelect = document.getElementById('envSelect');
            const superAdminRoleInput = document.getElementById('superAdminRoleInput');
            const systemInfo = document.getElementById('systemInfo');
            
            if (systemSelect.value && envSelect.value) {
                const config = systemConfigs[systemSelect.value][envSelect.value];
                document.getElementById('tenantId').textContent = config.tenantId === null ? 'NULL' : config.tenantId;
                document.getElementById('scene').textContent = config.scene;
                document.getElementById('defaultSuperAdminRole').textContent = config.defaultSuperAdminRole;
                document.getElementById('superAdminRole').textContent = superAdminRoleInput.value || '未设置';
                document.getElementById('permissionPrefix').textContent = config.prefix;
                systemInfo.style.display = 'block';
            } else {
                systemInfo.style.display = 'none';
            }
        }

        function switchTab(tabName) {
            // 更新Tab按钮状态
            const permissionTab = document.getElementById('permissionTab');
            const adminTab = document.getElementById('adminTab');
            
            permissionTab.classList.remove('active');
            adminTab.classList.remove('active');
            
            // 获取内容区域
            const basicConfigSection = document.getElementById('basicConfigSection');
            const permissionSection = document.getElementById('permissionModeSection');
            const adminSection = document.getElementById('adminModeSection');
            const permissionConfigSection = document.getElementById('permissionConfigSection');
            
            if (tabName === 'permission') {
                permissionTab.classList.add('active');
                basicConfigSection.style.display = 'block';
                permissionSection.style.display = 'block';
                adminSection.style.display = 'none';
                permissionConfigSection.style.display = 'block';
            } else {
                adminTab.classList.add('active');
                basicConfigSection.style.display = 'none';
                permissionSection.style.display = 'none';
                adminSection.style.display = 'block';
                permissionConfigSection.style.display = 'none';
            }
        }

        let permissionIdCounter = 1;
        let adminPermissionIdCounter = 1;

        function addPermission() {
            const permissionList = document.getElementById('permissionList');
            const newPermission = document.createElement('div');
            permissionIdCounter++;
            newPermission.className = 'permission-item level-1';
            newPermission.setAttribute('data-permission-id', 'perm-' + permissionIdCounter);
            newPermission.setAttribute('data-level', '1');
            newPermission.innerHTML = `
                <button class="btn-add-child" onclick="toggleChildPermissionArea(this)">+子权限</button>
                <div class="permission-level-display">一级权限</div>
                <input type="text" class="form-control permission-code" placeholder="权限代码 (不含前缀)" oninput="updatePermissionStructure()" />
                <input type="text" class="form-control permission-name" placeholder="权限名称" oninput="updatePermissionStructure()" />
                <button type="button" class="btn-plus" onclick="addSameLevelPermission(this)" title="添加同级权限">+</button>
                <button type="button" class="btn btn-secondary" onclick="removePermission(this)">删除</button>
                
                <div class="child-permissions-area">
                    <div class="child-permissions-list">
                        <!-- 子权限项将在这里动态添加 -->
                    </div>
                    <button type="button" class="btn btn-success btn-small" onclick="addChildPermissionItem(this)">添加子权限</button>
                </div>
            `;
            permissionList.appendChild(newPermission);
            updatePermissionStructure();
        }

        function toggleChildPermissionArea(button) {
            const permissionItem = button.parentElement;
            const childArea = permissionItem.querySelector('.child-permissions-area');
            
            if (childArea.classList.contains('show')) {
                // 隐藏子权限区域
                childArea.classList.remove('show');
                button.textContent = '+子权限';
                button.classList.remove('active');
            } else {
                // 显示子权限区域
                childArea.classList.add('show');
                button.textContent = '-子权限';
                button.classList.add('active');
            }
            
            updatePermissionStructure();
        }

        function addChildPermissionItem(button) {
            const childArea = button.parentElement;
            const parentItem = childArea.parentElement;
            const parentLevel = parseInt(parentItem.getAttribute('data-level') || '1');
            
            if (parentLevel >= 10) {
                alert('已达到最大权限层级（十级）！');
                return;
            }
            
            const childLevel = parentLevel + 1;
            const levelNames = ['', '一级权限', '二级权限', '三级权限', '四级权限', '五级权限', '六级权限', '七级权限', '八级权限', '九级权限', '十级权限'];
            
            // 查找最后一个子权限项，在其子权限区域中添加
            const childList = childArea.querySelector('.child-permissions-list');
            const lastChildItem = childList.lastElementChild;
            
            let targetContainer = childList;
            let targetLevel = childLevel;
            
            // 如果已有子权限，找到最深层级的子权限
            if (lastChildItem) {
                const findDeepestChild = (item) => {
                    const itemChildArea = item.querySelector('.child-permissions-area');
                    if (itemChildArea) {
                        const itemChildList = itemChildArea.querySelector('.child-permissions-list');
                        if (itemChildList && itemChildList.children.length > 0) {
                            const lastChild = itemChildList.lastElementChild;
                            return findDeepestChild(lastChild);
                        }
                    }
                    return item;
                };
                
                const deepestChild = findDeepestChild(lastChildItem);
                const deepestLevel = parseInt(deepestChild.getAttribute('data-level'));
                
                if (deepestLevel < 10) {
                    const deepestChildArea = deepestChild.querySelector('.child-permissions-area');
                    if (deepestChildArea) {
                        targetContainer = deepestChildArea.querySelector('.child-permissions-list');
                        targetLevel = deepestLevel + 1;
                        // 自动显示该子权限区域
                        deepestChildArea.classList.add('show');
                    }
                }
            }
            
            const childItem = document.createElement('div');
            permissionIdCounter++;
            childItem.className = 'child-permission-item';
            childItem.setAttribute('data-permission-id', 'child-perm-' + permissionIdCounter);
            childItem.setAttribute('data-level', targetLevel.toString());
            
            childItem.innerHTML = `
                <div class="permission-level-display">${levelNames[targetLevel]}</div>
                <input type="text" class="form-control permission-code" placeholder="权限代码 (不含前缀)" oninput="updatePermissionStructure()" />
                <input type="text" class="form-control permission-name" placeholder="权限名称" oninput="updatePermissionStructure()" />
                <button type="button" class="btn-plus" onclick="addSameLevelPermission(this)" title="添加同级权限">+</button>
                <button type="button" class="btn btn-secondary btn-small" onclick="removeChildPermissionItem(this)">删除</button>
            `;
            
            // 如果不是最高级别，添加子权限区域
            if (targetLevel < 10) {
                const childPermissionArea = document.createElement('div');
                childPermissionArea.className = 'child-permissions-area';
                childPermissionArea.innerHTML = `
                    <div class="child-permissions-list">
                        <!-- 子权限项将在这里动态添加 -->
                    </div>
                    <button type="button" class="btn btn-success btn-small" onclick="addChildPermissionItem(this)">添加子权限</button>
                `;
                childItem.appendChild(childPermissionArea);
            }
            
            targetContainer.appendChild(childItem);
            
            // 自动显示子权限区域
            childArea.classList.add('show');
            updatePermissionStructure();
        }

        function addSameLevelPermission(button) {
            const currentItem = button.parentElement;
            const currentLevel = parseInt(currentItem.getAttribute('data-level') || '1');
            const levelNames = ['', '一级权限', '二级权限', '三级权限', '四级权限', '五级权限', '六级权限', '七级权限', '八级权限', '九级权限', '十级权限'];
            
            if (currentLevel === 1) {
                // 如果是一级权限，添加到权限列表中
                addPermission();
            } else {
                // 如果是子权限，添加到同一个父级下
                const parentArea = currentItem.parentElement; // 这是child-permissions-list
                const childItem = document.createElement('div');
                permissionIdCounter++;
                childItem.className = 'child-permission-item';
                childItem.setAttribute('data-permission-id', 'child-perm-' + permissionIdCounter);
                childItem.setAttribute('data-level', currentLevel.toString());
                
                childItem.innerHTML = `
                    <div class="permission-level-display">${levelNames[currentLevel]}</div>
                    <input type="text" class="form-control permission-code" placeholder="权限代码 (不含前缀)" oninput="updatePermissionStructure()" />
                    <input type="text" class="form-control permission-name" placeholder="权限名称" oninput="updatePermissionStructure()" />
                    <button type="button" class="btn-plus" onclick="addSameLevelPermission(this)" title="添加同级权限">+</button>
                    <button type="button" class="btn btn-secondary btn-small" onclick="removeChildPermissionItem(this)">删除</button>
                `;
                
                // 如果不是最高级别，添加子权限区域
                if (currentLevel < 10) {
                    const childPermissionArea = document.createElement('div');
                    childPermissionArea.className = 'child-permissions-area';
                    childPermissionArea.innerHTML = `
                        <div class="child-permissions-list">
                            <!-- 子权限项将在这里动态添加 -->
                        </div>
                        <button type="button" class="btn btn-success btn-small" onclick="addChildPermissionItem(this)">添加子权限</button>
                    `;
                    childItem.appendChild(childPermissionArea);
                }
                
                parentArea.appendChild(childItem);
            }
            
            updatePermissionStructure();
        }

        function removeChildPermissionItem(button) {
            const childItem = button.parentElement;
            childItem.remove();
            updatePermissionStructure();
        }

        function removePermission(button) {
            const permissionList = document.getElementById('permissionList');
            if (permissionList.children.length > 1) {
                const permissionItem = button.parentElement;
                permissionItem.remove();
                updatePermissionStructure();
            } else {
                alert('至少需要保留一个权限配置项');
            }
        }

        function getAllPermissions() {
            const permissions = [];
            
            function extractPermissions(container, parentCode = null, level = 1) {
                const items = container.children;
                
                for (let item of items) {
                    if (item.classList.contains('permission-item') || item.classList.contains('child-permission-item')) {
                        const code = item.querySelector('.permission-code')?.value.trim();
                        const name = item.querySelector('.permission-name')?.value.trim();
                        
                        if (code && name) {
                            const permission = {
                                code,
                                name,
                                level: level.toString(),
                                parentCode: parentCode
                            };
                            permissions.push(permission);
                            
                            // 递归处理子权限
                            const childArea = item.querySelector('.child-permissions-area.show');
                            if (childArea) {
                                const childList = childArea.querySelector('.child-permissions-list');
                                if (childList) {
                                    extractPermissions(childList, code, level + 1);
                                }
                            }
                        }
                    }
                }
            }
            
            const permissionList = document.getElementById('permissionList');
            extractPermissions(permissionList, null, 1);
            
            return permissions;
        }

        function updatePermissionStructure() {
            // 这个函数现在主要用于触发界面更新
            // 新的结构不需要复杂的重排序逻辑
        }

        function clearAll() {
            if (confirm('确定要清空所有配置吗？')) {
                document.getElementById('systemSelect').value = '';
                document.getElementById('envSelect').value = '';
                document.getElementById('superAdminRoleInput').value = '';
                document.getElementById('startPermissionId').value = '1000';
                document.getElementById('systemInfo').style.display = 'none';
                
                // 清空超管账号相关字段
                document.getElementById('tenantIdInput').value = '';
                document.getElementById('userNo').value = '';
                document.getElementById('userName').value = '';
                document.getElementById('userPhone').value = '';
                document.getElementById('userType').value = 'supplier';
                document.getElementById('accountNo').value = '';
                document.getElementById('accountLogin').value = '';
                document.getElementById('accountPassword').value = '';
                document.getElementById('roleCode').value = '';
                document.getElementById('roleName').value = '';
                document.getElementById('roleDesc').value = '';
                document.getElementById('sceneType').value = 'supplier_dashboard';
                
                const permissionList = document.getElementById('permissionList');
                permissionIdCounter = 1;
                permissionList.innerHTML = `
                    <div class="permission-item level-1" data-permission-id="perm-1" data-level="1">
                        <button class="btn-add-child" onclick="toggleChildPermissionArea(this)">+子权限</button>
                        <div class="permission-level-display">一级权限</div>
                        <input type="text" class="form-control permission-code" placeholder="权限代码 (不含前缀)" oninput="updatePermissionStructure()" />
                        <input type="text" class="form-control permission-name" placeholder="权限名称" oninput="updatePermissionStructure()" />
                        <button type="button" class="btn-plus" onclick="addSameLevelPermission(this)" title="添加同级权限">+</button>
                        <button type="button" class="btn btn-secondary" onclick="removePermission(this)">删除</button>
                        
                        <div class="child-permissions-area">
                            <div class="child-permissions-list">
                                <!-- 子权限项将在这里动态添加 -->
                            </div>
                            <button type="button" class="btn btn-success btn-small" onclick="addChildPermissionItem(this)">添加子权限</button>
                        </div>
                    </div>
                `;
                
                document.getElementById('sqlOutput').style.display = 'none';
                document.getElementById('copyButton').style.display = 'none';
            }
        }

        function generateSQL() {
            // 检查当前激活的Tab
            const permissionTab = document.getElementById('permissionTab');
            const isPermissionMode = permissionTab.classList.contains('active');
            
            if (isPermissionMode) {
                generatePermissionSQL();
            } else {
                generateAdminSQL();
            }
        }

        function generatePermissionSQL() {
            const systemSelect = document.getElementById('systemSelect');
            const envSelect = document.getElementById('envSelect');
            const superAdminRoleInput = document.getElementById('superAdminRoleInput');
            const startPermissionIdInput = document.getElementById('startPermissionId');
            
            if (!systemSelect.value || !envSelect.value) {
                alert('请先选择系统和环境！');
                return;
            }

            if (!superAdminRoleInput.value.trim()) {
                alert('请输入超管角色Code！');
                return;
            }

            const startPermissionId = parseInt(startPermissionIdInput.value) || 1000;
            if (startPermissionId < 1) {
                alert('权限起始ID必须大于0！');
                return;
            }

            const config = systemConfigs[systemSelect.value][envSelect.value];
            const superAdminRole = superAdminRoleInput.value.trim();
            const permissions = getAllPermissions();

            if (permissions.length === 0) {
                alert('请至少配置一个权限！');
                return;
            }

            // 生成当前时间戳
            const now = new Date().toISOString().replace('T', ' ').substring(0, 19);

            // 生成SQL
            let sql = `-- 权限初始化SQL - ${systemConfigs[systemSelect.value].name} (${envSelect.value === 'dev' ? '开发环境' : '生产环境'})\n`;
            sql += `-- 生成时间: ${now}\n\n`;
            sql += `BEGIN;\n\n`;
            
            // 检查所有非一级权限的父级权限是否存在
            const levelNames = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
            for (const permission of permissions) {
                if (permission.level !== '1') {
                    if (!permission.parentCode) {
                        const levelName = levelNames[parseInt(permission.level)] || permission.level;
                        alert(`${levelName}级权限 "${permission.name}" 必须有父级权限！`);
                        return;
                    }
                    const expectedParentLevel = (parseInt(permission.level) - 1).toString();
                    const parentExists = permissions.some(p => p.level === expectedParentLevel && p.code === permission.parentCode);
                    if (!parentExists) {
                        const levelName = levelNames[parseInt(permission.level)] || permission.level;
                        alert(`${levelName}级权限 "${permission.name}" 的父级权限 "${permission.parentCode}" 在当前配置中不存在或级别不正确！`);
                        return;
                    }
                }
            }
            
            // 按级别分离权限
            const permissionsByLevel = {};
            for (let i = 1; i <= 10; i++) {
                permissionsByLevel[i.toString()] = permissions.filter(p => p.level === i.toString());
            }
            
            // 创建权限代码到ID的映射
            const codeToIdMap = {};
            let currentId = startPermissionId;
            
            // 插入各级权限数据
            for (let level = 1; level <= 10; level++) {
                const levelPermissions = permissionsByLevel[level.toString()];
                if (levelPermissions.length > 0) {
                    const levelName = levelNames[level];
                    sql += `\n-- 插入${levelName}级权限数据\n`;
                    
                    levelPermissions.forEach((permission, index) => {
                        const fullCode = config.prefix + permission.code;
                        const id = currentId++;
                        codeToIdMap[permission.code] = id;
                        
                        let parentId = -1;
                        if (level > 1 && permission.parentCode) {
                            parentId = codeToIdMap[permission.parentCode];
                        }
                        
                        const tenantIdValue = config.tenantId === null ? 'NULL' : `'${config.tenantId}'`;
                        sql += `INSERT INTO "public"."permission" ("id", "parent_id", "code", "name", "is_deleted", "gmt_created", "gmt_modified", "tenant_id", "scene") VALUES (${id}, ${parentId}, '${fullCode}', '${permission.name}', 'f', '${now}', '${now}', ${tenantIdValue}, '${config.scene}');\n`;
                    });
                }
            }
            
            sql += `\n-- 插入角色权限关联数据\n`;
            const tenantIdValueForRole = config.tenantId === null ? 'NULL' : `'${config.tenantId}'`;
            permissions.forEach(permission => {
                const fullCode = config.prefix + permission.code;
                sql += `INSERT INTO "public"."role_permission" ("role_code", "permission_code", "is_deleted", "biz_data", "gmt_created", "gmt_modified", "tenant_id") VALUES ('${superAdminRole}', '${fullCode}', 'f', NULL, '${now}', '${now}', ${tenantIdValueForRole});\n`;
            });
            
            sql += `\nCOMMIT;\n`;

            // 显示SQL
            document.getElementById('sqlOutput').textContent = sql;
            document.getElementById('sqlOutput').style.display = 'block';
            document.getElementById('copyButton').style.display = 'block';
        }

        function generateAdminSQL() {
            // 获取表单数据
            const tenantIdInput = document.getElementById('tenantIdInput').value.trim();
            const userNo = document.getElementById('userNo').value.trim();
            const userName = document.getElementById('userName').value.trim();
            const userPhone = document.getElementById('userPhone').value.trim();
            const userType = document.getElementById('userType').value.trim();
            const accountNo = document.getElementById('accountNo').value.trim();
            const accountLogin = document.getElementById('accountLogin').value.trim();
            const accountPassword = document.getElementById('accountPassword').value.trim();
            const roleCode = document.getElementById('roleCode').value.trim();
            const roleName = document.getElementById('roleName').value.trim();
            const roleDesc = document.getElementById('roleDesc').value.trim();
            const sceneType = document.getElementById('sceneType').value.trim();
            
            // 获取权限相关数据
            const adminStartPermissionId = parseInt(document.getElementById('adminStartPermissionId').value) || 1000;
            const permissionPrefixInput = document.getElementById('permissionPrefix').value.trim();
            const permissions = getAllPermissionsAdmin();

            // 验证必填字段
            if (!userNo || !accountNo || !roleCode) {
                alert('用户编号、账号编号、角色代码为必填项！');
                return;
            }

            // 验证必填字段
            if (!userNo || !accountNo || !roleCode) {
                alert('用户编号、账号编号、角色代码为必填项！');
                return;
            }

            // 生成当前时间戳
            const now = new Date().toISOString().replace('T', ' ').substring(0, 19);

            // 生成SQL
            let sql = `-- 超管账号初始化SQL\n`;
            sql += `-- 生成时间: ${now}\n\n`;
            sql += `BEGIN;\n\n`;

            // 处理租户ID
            const tenantIdValue = tenantIdInput === '' ? 'NULL' : `'${tenantIdInput}'`;
            const userNameValue = userName || 'NULL';
            const userPhoneValue = userPhone || 'NULL';
            const userTypeValue = userType || 'NULL';
            const accountLoginValue = accountLogin || (userPhone || 'NULL');
            const accountPasswordValue = accountPassword || 'NULL';
            const roleNameValue = roleName || '超级管理员';
            const roleDescValue = roleDesc || '超级管理员';

            // 插入用户数据
            sql += `-- 插入用户数据\n`;
            sql += `INSERT INTO "public"."users" ("user_no", "user_name", "gmt_created", "gmt_modified", "phone", "is_deleted", "tenant_id", "user_type", "registration_time") VALUES ('${userNo}', `;
            sql += userNameValue === 'NULL' ? 'NULL' : `'${userNameValue}'`;
            sql += `, '${now}', '${now}', `;
            sql += userPhoneValue === 'NULL' ? 'NULL' : `'${userPhoneValue}'`;
            sql += `, 'f', ${tenantIdValue}, `;
            sql += userTypeValue === 'NULL' ? 'NULL' : `'${userTypeValue}'`;
            sql += `, '${now}');\n\n`;

            // 插入账号数据
            sql += `-- 插入账号数据\n`;
            sql += `INSERT INTO "public"."account" ("account_no", "account", "phone", "name", "password", "is_deleted", "biz_data", "gmt_created", "gmt_modified", "creator_id", "visible", "user_no", "tenant_id", "user_type", "registration_time") VALUES ('${accountNo}', `;
            sql += accountLoginValue === 'NULL' ? 'NULL' : `'${accountLoginValue}'`;
            sql += `, `;
            sql += userPhoneValue === 'NULL' ? 'NULL' : `'${userPhoneValue}'`;
            sql += `, `;
            sql += userNameValue === 'NULL' ? 'NULL' : `'${userNameValue}'`;
            sql += `, `;
            sql += accountPasswordValue === 'NULL' ? 'NULL' : `'${accountPasswordValue}'`;
            sql += `, 'f', NULL, '${now}', '${now}', '${accountNo}', 'f', '${userNo}', ${tenantIdValue}, `;
            sql += userTypeValue === 'NULL' ? 'NULL' : `'${userTypeValue}'`;
            sql += `, '${now}');\n\n`;

            // 插入用户账号关联数据
            sql += `-- 插入用户账号关联数据\n`;
            sql += `INSERT INTO "public"."user_account" ("account_no", "user_no", "gmt_created", "gmt_modified", "is_deleted", "tenant_id") VALUES ('${accountNo}', '${userNo}', '${now}', '${now}', 'f', ${tenantIdValue});\n\n`;

            // 插入角色数据
            sql += `-- 插入角色数据\n`;
            sql += `INSERT INTO "public"."role" ("role_code", "role_name", "role_desc", "creator_id", "visible", "gmt_created", "gmt_modified", "is_deleted", "tenant_id", "scene") VALUES ('${roleCode}', '${roleNameValue}', '${roleDescValue}', '${accountNo}', 'f', '${now}', '${now}', 'f', ${tenantIdValue}, '${sceneType}');\n\n`;

            // 插入用户角色关联数据
            sql += `-- 插入用户角色关联数据\n`;
            sql += `INSERT INTO "public"."user_role" ("user_no", "role_code", "is_deleted", "gmt_created", "gmt_modified", "tenant_id") VALUES ('${userNo}', '${roleCode}', 'f', '${now}', '${now}', ${tenantIdValue});\n\n`;

            // 如果配置了权限，则生成权限相关SQL
            if (permissions.length > 0) {
                // 检查权限层级关系
                const levelNames = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
                for (const permission of permissions) {
                    if (permission.level !== '1') {
                        if (!permission.parentCode) {
                            const levelName = levelNames[parseInt(permission.level)] || permission.level;
                            alert(`${levelName}级权限 "${permission.name}" 必须有父级权限！`);
                            return;
                        }
                        const expectedParentLevel = (parseInt(permission.level) - 1).toString();
                        const parentExists = permissions.some(p => p.level === expectedParentLevel && p.code === permission.parentCode);
                        if (!parentExists) {
                            const levelName = levelNames[parseInt(permission.level)] || permission.level;
                            alert(`${levelName}级权限 "${permission.name}" 的父级权限 "${permission.parentCode}" 在当前配置中不存在或级别不正确！`);
                            return;
                        }
                    }
                }

                // 按级别分离权限
                const permissionsByLevel = {};
                for (let i = 1; i <= 10; i++) {
                    permissionsByLevel[i.toString()] = permissions.filter(p => p.level === i.toString());
                }
                
                // 创建权限代码到ID的映射
                const codeToIdMap = {};
                let currentId = adminStartPermissionId;
                
                // 插入各级权限数据
                for (let level = 1; level <= 10; level++) {
                    const levelPermissions = permissionsByLevel[level.toString()];
                    if (levelPermissions.length > 0) {
                        const levelName = levelNames[level];
                        sql += `\n-- 插入${levelName}级权限数据\n`;
                        
                        levelPermissions.forEach((permission, index) => {
                            const fullCode = permissionPrefixInput + permission.code;
                            const id = currentId++;
                            codeToIdMap[permission.code] = id;
                            
                            let parentId = -1;
                            if (level > 1 && permission.parentCode) {
                                parentId = codeToIdMap[permission.parentCode];
                            }
                            
                            sql += `INSERT INTO "public"."permission" ("id", "parent_id", "code", "name", "is_deleted", "gmt_created", "gmt_modified", "tenant_id", "scene") VALUES (${id}, ${parentId}, '${fullCode}', '${permission.name}', 'f', '${now}', '${now}', ${tenantIdValue}, '${sceneType}');\n`;
                        });
                    }
                }
                
                sql += `\n-- 插入角色权限关联数据\n`;
                permissions.forEach(permission => {
                    const fullCode = permissionPrefixInput + permission.code;
                    sql += `INSERT INTO "public"."role_permission" ("role_code", "permission_code", "is_deleted", "biz_data", "gmt_created", "gmt_modified", "tenant_id") VALUES ('${roleCode}', '${fullCode}', 'f', NULL, '${now}', '${now}', ${tenantIdValue});\n`;
                });
                
                sql += `\n`;
            }

            sql += `COMMIT;\n`;

            // 显示SQL
            document.getElementById('sqlOutput').textContent = sql;
            document.getElementById('sqlOutput').style.display = 'block';
            document.getElementById('copyButton').style.display = 'block';
        }

        function copyToClipboard() {
            const sqlOutput = document.getElementById('sqlOutput');
            const textArea = document.createElement('textarea');
            textArea.value = sqlOutput.textContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            
            alert('SQL已复制到剪贴板！');
        }

        // 超管账号页面的权限管理函数
        function addPermissionAdmin() {
            const permissionList = document.getElementById('permissionListAdmin');
            const newPermission = document.createElement('div');
            adminPermissionIdCounter++;
            newPermission.className = 'permission-item level-1';
            newPermission.setAttribute('data-permission-id', 'admin-perm-' + adminPermissionIdCounter);
            newPermission.setAttribute('data-level', '1');
            newPermission.innerHTML = `
                <button class="btn-add-child" onclick="toggleChildPermissionAreaAdmin(this)">+子权限</button>
                <div class="permission-level-display">一级权限</div>
                <input type="text" class="form-control permission-code" placeholder="权限代码 (不含前缀)" oninput="updatePermissionStructureAdmin()" />
                <input type="text" class="form-control permission-name" placeholder="权限名称" oninput="updatePermissionStructureAdmin()" />
                <button type="button" class="btn-plus" onclick="addSameLevelPermissionAdmin(this)" title="添加同级权限">+</button>
                <button type="button" class="btn btn-secondary" onclick="removePermissionAdmin(this)">删除</button>
                
                <div class="child-permissions-area">
                    <div class="child-permissions-list">
                        <!-- 子权限项将在这里动态添加 -->
                    </div>
                    <button type="button" class="btn btn-success btn-small" onclick="addChildPermissionItemAdmin(this)">添加子权限</button>
                </div>
            `;
            permissionList.appendChild(newPermission);
            updatePermissionStructureAdmin();
        }

        function toggleChildPermissionAreaAdmin(button) {
            const permissionItem = button.parentElement;
            const childArea = permissionItem.querySelector('.child-permissions-area');
            
            if (childArea.classList.contains('show')) {
                childArea.classList.remove('show');
                button.textContent = '+子权限';
                button.classList.remove('active');
            } else {
                childArea.classList.add('show');
                button.textContent = '-子权限';
                button.classList.add('active');
            }
            
            updatePermissionStructureAdmin();
        }

        function addChildPermissionItemAdmin(button) {
            const childArea = button.parentElement;
            const parentItem = childArea.parentElement;
            const parentLevel = parseInt(parentItem.getAttribute('data-level') || '1');
            
            if (parentLevel >= 10) {
                alert('已达到最大权限层级（十级）！');
                return;
            }
            
            const childLevel = parentLevel + 1;
            const levelNames = ['', '一级权限', '二级权限', '三级权限', '四级权限', '五级权限', '六级权限', '七级权限', '八级权限', '九级权限', '十级权限'];
            
            const childList = childArea.querySelector('.child-permissions-list');
            const lastChildItem = childList.lastElementChild;
            
            let targetContainer = childList;
            let targetLevel = childLevel;
            
            if (lastChildItem) {
                const findDeepestChild = (item) => {
                    const itemChildArea = item.querySelector('.child-permissions-area');
                    if (itemChildArea) {
                        const itemChildList = itemChildArea.querySelector('.child-permissions-list');
                        if (itemChildList && itemChildList.children.length > 0) {
                            const lastChild = itemChildList.lastElementChild;
                            return findDeepestChild(lastChild);
                        }
                    }
                    return item;
                };
                
                const deepestChild = findDeepestChild(lastChildItem);
                const deepestLevel = parseInt(deepestChild.getAttribute('data-level'));
                
                if (deepestLevel < 10) {
                    const deepestChildArea = deepestChild.querySelector('.child-permissions-area');
                    if (deepestChildArea) {
                        targetContainer = deepestChildArea.querySelector('.child-permissions-list');
                        targetLevel = deepestLevel + 1;
                        deepestChildArea.classList.add('show');
                    }
                }
            }
            
            const childItem = document.createElement('div');
            adminPermissionIdCounter++;
            childItem.className = 'child-permission-item';
            childItem.setAttribute('data-permission-id', 'admin-child-perm-' + adminPermissionIdCounter);
            childItem.setAttribute('data-level', targetLevel.toString());
            
            childItem.innerHTML = `
                <div class="permission-level-display">${levelNames[targetLevel]}</div>
                <input type="text" class="form-control permission-code" placeholder="权限代码 (不含前缀)" oninput="updatePermissionStructureAdmin()" />
                <input type="text" class="form-control permission-name" placeholder="权限名称" oninput="updatePermissionStructureAdmin()" />
                <button type="button" class="btn-plus" onclick="addSameLevelPermissionAdmin(this)" title="添加同级权限">+</button>
                <button type="button" class="btn btn-secondary btn-small" onclick="removeChildPermissionItemAdmin(this)">删除</button>
            `;
            
            if (targetLevel < 10) {
                const childPermissionArea = document.createElement('div');
                childPermissionArea.className = 'child-permissions-area';
                childPermissionArea.innerHTML = `
                    <div class="child-permissions-list">
                        <!-- 子权限项将在这里动态添加 -->
                    </div>
                    <button type="button" class="btn btn-success btn-small" onclick="addChildPermissionItemAdmin(this)">添加子权限</button>
                `;
                childItem.appendChild(childPermissionArea);
            }
            
            targetContainer.appendChild(childItem);
            childArea.classList.add('show');
            updatePermissionStructureAdmin();
        }

        function addSameLevelPermissionAdmin(button) {
            const currentItem = button.parentElement;
            const currentLevel = parseInt(currentItem.getAttribute('data-level') || '1');
            const levelNames = ['', '一级权限', '二级权限', '三级权限', '四级权限', '五级权限', '六级权限', '七级权限', '八级权限', '九级权限', '十级权限'];
            
            if (currentLevel === 1) {
                addPermissionAdmin();
            } else {
                const parentArea = currentItem.parentElement;
                const childItem = document.createElement('div');
                adminPermissionIdCounter++;
                childItem.className = 'child-permission-item';
                childItem.setAttribute('data-permission-id', 'admin-child-perm-' + adminPermissionIdCounter);
                childItem.setAttribute('data-level', currentLevel.toString());
                
                childItem.innerHTML = `
                    <div class="permission-level-display">${levelNames[currentLevel]}</div>
                    <input type="text" class="form-control permission-code" placeholder="权限代码 (不含前缀)" oninput="updatePermissionStructureAdmin()" />
                    <input type="text" class="form-control permission-name" placeholder="权限名称" oninput="updatePermissionStructureAdmin()" />
                    <button type="button" class="btn-plus" onclick="addSameLevelPermissionAdmin(this)" title="添加同级权限">+</button>
                    <button type="button" class="btn btn-secondary btn-small" onclick="removeChildPermissionItemAdmin(this)">删除</button>
                `;
                
                if (currentLevel < 10) {
                    const childPermissionArea = document.createElement('div');
                    childPermissionArea.className = 'child-permissions-area';
                    childPermissionArea.innerHTML = `
                        <div class="child-permissions-list">
                            <!-- 子权限项将在这里动态添加 -->
                        </div>
                        <button type="button" class="btn btn-success btn-small" onclick="addChildPermissionItemAdmin(this)">添加子权限</button>
                    `;
                    childItem.appendChild(childPermissionArea);
                }
                
                parentArea.appendChild(childItem);
            }
            
            updatePermissionStructureAdmin();
        }

        function removeChildPermissionItemAdmin(button) {
            const childItem = button.parentElement;
            childItem.remove();
            updatePermissionStructureAdmin();
        }

        function removePermissionAdmin(button) {
            const permissionList = document.getElementById('permissionListAdmin');
            if (permissionList.children.length > 1) {
                const permissionItem = button.parentElement;
                permissionItem.remove();
                updatePermissionStructureAdmin();
            } else {
                alert('至少需要保留一个权限配置项');
            }
        }

        function updatePermissionStructureAdmin() {
            // 这个函数现在主要用于触发界面更新
        }

        function getAllPermissionsAdmin() {
            const permissions = [];
            
            function extractPermissions(container, parentCode = null, level = 1) {
                const items = container.children;
                
                for (let item of items) {
                    if (item.classList.contains('permission-item') || item.classList.contains('child-permission-item')) {
                        const code = item.querySelector('.permission-code')?.value.trim();
                        const name = item.querySelector('.permission-name')?.value.trim();
                        
                        if (code && name) {
                            const permission = {
                                code,
                                name,
                                level: level.toString(),
                                parentCode: parentCode
                            };
                            permissions.push(permission);
                            
                            const childArea = item.querySelector('.child-permissions-area.show');
                            if (childArea) {
                                const childList = childArea.querySelector('.child-permissions-list');
                                if (childList) {
                                    extractPermissions(childList, code, level + 1);
                                }
                            }
                        }
                    }
                }
            }
            
            const permissionList = document.getElementById('permissionListAdmin');
            extractPermissions(permissionList, null, 1);
            
            return permissions;
        }

        // 页面加载时初始化
        window.onload = function() {
            // 页面默认为空，不添加任何示例权限
        };
    </script>
</body>
</html> 