package pers.amos.service.shortkey;

/**
 * 短链接服务接口
 * 
 * <AUTHOR>
 */
public interface ShortUrlService {
    
    /**
     * 创建短链接
     * 
     * @param longUrl 原始长链接
     * @param customCode 自定义短链接码
     * @param expireDays 过期天数
     * @param description 描述
     * @param userId 用户ID
     * @return 短链接DTO
     */
    ShortUrlDTO createShortUrl(String longUrl, String customCode, Integer expireDays, String description, Long userId);
    
    /**
     * 根据短链接码获取长链接
     * 
     * @param shortCode 短链接码
     * @return 长链接
     */
    String getLongUrl(String shortCode);

    /**
     * 根据ID删除短链接
     * 
     * @param id 短链接ID
     * @return 是否成功
     */
    boolean deleteShortUrl(Long id);
    
    /**
     * 根据短链接码删除短链接
     * 
     * @param shortCode 短链接码
     * @return 是否成功
     */
    boolean deleteByShortCode(String shortCode);
} 