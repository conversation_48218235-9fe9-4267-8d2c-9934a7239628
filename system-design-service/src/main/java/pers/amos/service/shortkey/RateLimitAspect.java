package pers.amos.service.shortkey;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;

/**
 * 限流切面
 * 
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class RateLimitAspect {

    @Autowired
    private RedisRateLimiter rateLimiter;
    
    @Around("@annotation(pers.amos.service.shortkey.RateLimit)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        // 获取限流注解
        RateLimit rateLimit = method.getAnnotation(RateLimit.class);
        
        // 获取当前请求的IP
        String ip = getIpAddress();
        
        // 构造限流键
        String methodName = signature.getDeclaringTypeName() + "." + method.getName();
        String key = rateLimit.prefix() + methodName + ":" + ip;
        
        // 判断是否允许请求通过
        boolean allowed = rateLimiter.isAllowed(key, rateLimit.limit(), rateLimit.window());
        
        if (!allowed) {
            // 超过限流阈值，直接拒绝请求
            log.warn("请求被限流: method={}, ip={}, limit={}, window={}s", 
                    methodName, ip, rateLimit.limit(), rateLimit.window());
            throw new RuntimeException("请求过于频繁，请稍后再试");
        }
        
        // 限流检查通过，继续执行原方法
        return joinPoint.proceed();
    }
    
    /**
     * 获取请求IP
     */
    private String getIpAddress() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String ip = request.getHeader("X-Forwarded-For");
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("Proxy-Client-IP");
                }
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getHeader("WL-Proxy-Client-IP");
                }
                if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
                    ip = request.getRemoteAddr();
                }
                
                // 多个代理的情况，第一个IP为客户端真实IP
                if (ip != null && ip.indexOf(",") > 0) {
                    ip = ip.substring(0, ip.indexOf(","));
                }
                
                return ip;
            }
        } catch (Exception e) {
            log.error("获取请求IP地址异常", e);
        }
        
        return "unknown";
    }
} 