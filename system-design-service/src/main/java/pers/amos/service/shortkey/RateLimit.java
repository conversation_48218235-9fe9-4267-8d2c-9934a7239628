package pers.amos.service.shortkey;

import java.lang.annotation.*;

/**
 * 接口限流注解
 * 
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RateLimit {
    
    /**
     * 限流阈值
     */
    int limit() default 100;
    
    /**
     * 时间窗口(秒)
     */
    int window() default 1;
    
    /**
     * 限流键前缀
     */
    String prefix() default "rate:limit:";
} 