package pers.amos.service.shortkey;

import cn.hutool.core.date.DateUtil;
import com.github.benmanes.caffeine.cache.Cache;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;
import pers.amos.dal.entity.ShortUrlDO;
import pers.amos.dal.mapper.ShortUrlMapper;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 短链接服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ShortUrlServiceImpl implements ShortUrlService {

    @Resource
    private ShortUrlMapper shortUrlMapper;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private Cache<String, String> shortUrlCache;

    @Resource
    private ShortUrlUtil shortUrlUtil;

    @Value("${short-url.domain:http://localhost:8080}")
    private String domain;

    private static final String REDIS_CACHE_KEY_PREFIX = "short_url:";
    private static final String REDIS_LOCK_KEY_PREFIX = "short_url:lock:";
    private static final int REDIS_CACHE_EXPIRATION = 7; // 缓存过期时间，单位天
    private static final int LOCK_EXPIRE_SECONDS = 10; // 分布式锁过期时间，单位秒

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RateLimit(limit = 5000, window = 1) // 限制每秒创建短链接的速率
    public ShortUrlDTO createShortUrl(String longUrl, String customCode, Integer expireDays, String description, Long userId) {
        // 计算长链接的MD5值
        String longUrlMd5 = DigestUtils.md5DigestAsHex(longUrl.getBytes());

        boolean locked = false;
        // 为长链接生成唯一key，用于加锁防止并发创建相同长链接
        String lockKey = REDIS_LOCK_KEY_PREFIX + "long:" + longUrlMd5;
        ShortUrlDO existingUrlByMd5;

        try {
            // 获取分布式锁
            locked = Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(lockKey, "1", LOCK_EXPIRE_SECONDS, TimeUnit.SECONDS));
            if (!locked) {
                // 获取锁失败，可能其他线程正在处理相同的长链接
                // 短暂等待后重新查询
                Thread.sleep(100);
                existingUrlByMd5 = shortUrlMapper.findByLongUrlMd5(longUrlMd5);
                if (existingUrlByMd5 != null && existingUrlByMd5.getIsDeleted() == 0 && longUrl.equals(existingUrlByMd5.getLongUrl())) {
                    ShortUrlDTO dto = convertToDto(existingUrlByMd5);
                    log.info("并发情况下，长链接已存在短链接: {}", dto.getShortUrl());
                    return dto;
                }

                // 如果仍然找不到，说明并发创建失败，返回错误
                throw new RuntimeException("系统繁忙，请稍后重试");
            }

            // 获取锁成功，开始处理
            String shortCode;
            if (StringUtils.isNotBlank(customCode)) {
                // 自定义短链码，检查是否已存在
                ShortUrlDO existingUrl = shortUrlMapper.findByShortCode(customCode);
                if (existingUrl != null) {
                    throw new RuntimeException("自定义短链接码已存在");
                }
                shortCode = customCode;
            } else {
                // 系统生成短链码 - 使用雪花算法保证唯一性
                shortCode = shortUrlUtil.generateShortCode();
            }

            // 计算过期时间
            Date expireTime = null;
            if (expireDays != null && expireDays > 0) {
                expireTime = DateUtil.offsetDay(new Date(), expireDays);
            }

            // 保存到数据库
            ShortUrlDO shortUrlDO = buildShortUrl(longUrl, description, userId, shortCode, longUrlMd5, expireTime);

            try {
                shortUrlMapper.insert(shortUrlDO);
            } catch (Exception e) {
                // 数据库唯一约束异常处理
                log.error("短链码插入失败，可能是并发创建导致，shortCode={}", shortCode, e);

                // 出现异常时再次查询，看是否已被其他线程创建
                existingUrlByMd5 = shortUrlMapper.findByLongUrlMd5(longUrlMd5);
                if (existingUrlByMd5 != null && existingUrlByMd5.getIsDeleted() == 0 && longUrl.equals(existingUrlByMd5.getLongUrl())) {
                    ShortUrlDTO dto = convertToDto(existingUrlByMd5);
                    log.info("并发创建后，长链接已存在短链接: {}", dto.getShortUrl());
                    return dto;
                }

                throw new RuntimeException("创建短链接失败，请稍后重试");
            }

            // 缓存到Redis和本地缓存
            cacheShortUrl(shortCode, longUrl, expireTime);

            // 转换为DTO返回
            return convertToDto(shortUrlDO);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("创建短链接被中断");
        } finally {
            // 确保锁释放
            if (locked) {
                redisTemplate.delete(lockKey);
            }
        }
    }

    private static ShortUrlDO buildShortUrl(String longUrl, String description, Long userId, String shortCode, String longUrlMd5, Date expireTime) {
        ShortUrlDO shortUrlDO = new ShortUrlDO();
        shortUrlDO.setShortCode(shortCode);
        shortUrlDO.setLongUrl(longUrl);
        shortUrlDO.setLongUrlMd5(longUrlMd5); // 保存MD5值
        shortUrlDO.setUserId(userId);
        shortUrlDO.setDescription(description);
        shortUrlDO.setExpireTime(expireTime);
        shortUrlDO.setCreateTime(new Date());
        shortUrlDO.setUpdateTime(new Date());
        shortUrlDO.setIsDeleted(0);
        return shortUrlDO;
    }

    @Override
    public String getLongUrl(String shortCode) {
        if (StringUtils.isBlank(shortCode)) {
            return null;
        }

        // 1. 先从本地缓存获取，性能最高（纳秒级别）
        String longUrl = shortUrlCache.getIfPresent(shortCode);
        if (longUrl != null) {
            // 命中本地缓存，直接返回
            return longUrl;
        }

        // 2. 从Redis缓存获取（毫秒级别）
        String cacheKey = REDIS_CACHE_KEY_PREFIX + shortCode;
        longUrl = redisTemplate.opsForValue().get(cacheKey);

        if (longUrl != null) {
            // 命中Redis缓存，回填本地缓存
            shortUrlCache.put(shortCode, longUrl);
            return longUrl;
        }

        // 计算长链接的MD5值
        String longUrlMd5 = DigestUtils.md5DigestAsHex(longUrl.getBytes());

        boolean locked = false;
        // 为长链接生成唯一key，用于加锁防止并发创建相同长链接
        String lockKey = REDIS_LOCK_KEY_PREFIX + "long:" + longUrlMd5;

        try {
            locked = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", LOCK_EXPIRE_SECONDS, TimeUnit.SECONDS);
            if (locked) {
                // 3. 缓存未命中，从数据库获取（可能几十毫秒）
                ShortUrlDO shortUrlDO = shortUrlMapper.findByShortCode(shortCode);
                if (shortUrlDO == null) {
                    return null;
                }

                // 检查是否过期
                if (shortUrlDO.getExpireTime() != null && shortUrlDO.getExpireTime().before(new Date())) {
                    return null;
                }

                // 从数据库获取到长链接
                longUrl = shortUrlDO.getLongUrl();

                // 添加到缓存（缓存回写）
                cacheShortUrl(shortCode, longUrl, shortUrlDO.getExpireTime());
            }
        } finally {
            // 如果获取了锁，需要释放锁
            if (locked) {
                try {
                    redisTemplate.delete(lockKey);
                } catch (Exception e) {
                    log.error("释放Redis锁失败, lockKey: {}", lockKey, e);
                }
            }
        }

        return longUrl;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RateLimit(limit = 1000, window = 1) // 限制每秒删除操作
    public boolean deleteShortUrl(Long id) {
        if (id == null) {
            return false;
        }

        ShortUrlDO shortUrlDO = shortUrlMapper.selectById(id);
        if (shortUrlDO == null) {
            return false;
        }

        // 软删除短链接
        shortUrlDO.setIsDeleted(1);
        shortUrlDO.setUpdateTime(new Date());
        int rows = shortUrlMapper.updateById(shortUrlDO);

        // 删除缓存
        if (rows > 0) {
            String shortCode = shortUrlDO.getShortCode();
            // 删除Redis缓存
            deleteCache(shortCode);
            return true;
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByShortCode(String shortCode) {
        if (StringUtils.isBlank(shortCode)) {
            return false;
        }

        ShortUrlDO shortUrlDO = shortUrlMapper.findByShortCode(shortCode);
        if (shortUrlDO == null) {
            return false;
        }

        return deleteShortUrl(shortUrlDO.getId());
    }

    /**
     * 缓存短链接到Redis和本地缓存
     *
     * @param shortCode  短链接码
     * @param longUrl    长链接
     * @param expireTime 过期时间
     */
    private void cacheShortUrl(String shortCode, String longUrl, Date expireTime) {
        // 设置Redis缓存
        String cacheKey = REDIS_CACHE_KEY_PREFIX + shortCode;

        // 计算过期时间
        long expireSeconds;
        if (expireTime != null) {
            // 设置过期时间，不超过当前时间+7天
            Date maxExpireTime = DateUtil.offsetDay(new Date(), REDIS_CACHE_EXPIRATION);
            Date actualExpireTime = expireTime.before(maxExpireTime) ? expireTime : maxExpireTime;

            expireSeconds = (actualExpireTime.getTime() - System.currentTimeMillis()) / 1000;
            if (expireSeconds <= 0) {
                return; // 已过期，不需要缓存
            }
        } else {
            // 没有过期时间，使用默认缓存过期时间
            expireSeconds = TimeUnit.DAYS.toSeconds(REDIS_CACHE_EXPIRATION);
        }

        // 设置短链到长链的映射缓存
        redisTemplate.opsForValue().set(cacheKey, longUrl, expireSeconds, TimeUnit.SECONDS);

        // 设置本地缓存
        shortUrlCache.put(shortCode, longUrl);
    }

    /**
     * 同时删除短链接的Redis和本地缓存
     *
     * @param shortCode 短链接码
     */
    private void deleteCache(String shortCode) {
        // 删除Redis缓存
        redisTemplate.delete(REDIS_CACHE_KEY_PREFIX + shortCode);
        // 删除本地缓存
        shortUrlCache.invalidate(shortCode);
    }

    /**
     * 将DO转换为DTO
     *
     * @param shortUrlDO 实体对象
     * @return DTO对象
     */
    private ShortUrlDTO convertToDto(ShortUrlDO shortUrlDO) {
        if (shortUrlDO == null) {
            return null;
        }

        ShortUrlDTO dto = new ShortUrlDTO();
        BeanUtils.copyProperties(shortUrlDO, dto);
        dto.setShortUrl(domain + "/" + shortUrlDO.getShortCode());

        return dto;
    }
} 