package pers.amos.service.shortkey;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短链接DTO
 * 
 * <AUTHOR>
 */
@Data
public class ShortUrlDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 短链接码
     */
    private String shortCode;
    
    /**
     * 完整短链接
     */
    private String shortUrl;
    
    /**
     * 原始长链接
     */
    private String longUrl;
    
    /**
     * 创建用户ID
     */
    private Long userId;
    
    /**
     * 过期时间
     */
    private Date expireTime;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
} 