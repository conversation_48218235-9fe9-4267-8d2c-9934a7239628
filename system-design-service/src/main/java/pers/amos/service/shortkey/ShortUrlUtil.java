package pers.amos.service.shortkey;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;
import pers.amos.service.leaf.service.LeafService;

import java.util.Random;

/**
 * 短链接生成工具类
 * 支持两种短链生成方式：
 * 1. 基于Leaf分布式ID生成器生成的唯一ID
 * 2. 基于MD5+随机盐的方式（传统方式）
 * 
 * <AUTHOR>
 */
@Component
public class ShortUrlUtil {
    
    private static final String CHARS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final int CHARS_LENGTH = CHARS.length();
    private static final int SHORT_URL_LENGTH = 6;
    
    private static final Random RANDOM = new Random();
    
    @Autowired
    private LeafService leafService;
    
    /**
     * 使用Leaf分布式ID生成器生成短链接码
     * 优势：性能高，无需查询数据库判断重复
     */
    public String generateShortCode() {
        // 使用Leaf生成分布式唯一ID
        long id = leafService.nextId("shortUrl");
        return encodeToBase62(id);
    }
    
    /**
     * 使用传统MD5+盐方式生成短链接码
     * 适合自定义场景，或ID生成器不可用时
     * 
     * @param longUrl 长链接
     * @return 短链接码
     */
    public String generateShortCodeByMd5(String longUrl) {
        // 生成当前时间戳和随机盐
        String timestamp = String.valueOf(System.currentTimeMillis());
        String salt = generateSalt(6);
        
        // 组合生成MD5
        String md5 = DigestUtils.md5DigestAsHex((longUrl + timestamp + salt).getBytes());
        
        // 取MD5的前8位
        String hex = md5.substring(0, 8);
        // 转为10进制数字
        long num = Long.parseLong(hex, 16);
        
        // 转为62进制
        return encodeToBase62(num);
    }
    
    /**
     * 10进制转62进制
     * 
     * @param num 10进制数字
     * @return 62进制字符串
     */
    public static String encodeToBase62(long num) {
        StringBuilder sb = new StringBuilder();
        while (num > 0) {
            sb.append(CHARS.charAt((int) (num % CHARS_LENGTH)));
            num = num / CHARS_LENGTH;
        }
        
        // 补齐长度
        String shortCode = sb.reverse().toString();
        if (shortCode.length() < SHORT_URL_LENGTH) {
            shortCode = StringUtils.leftPad(shortCode, SHORT_URL_LENGTH, '0');
        } else if (shortCode.length() > SHORT_URL_LENGTH) {
            shortCode = shortCode.substring(0, SHORT_URL_LENGTH);
        }
        
        return shortCode;
    }
    
    /**
     * 生成随机盐
     * 
     * @param length 盐长度
     * @return 随机盐
     */
    private static String generateSalt(int length) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            sb.append(CHARS.charAt(RANDOM.nextInt(CHARS_LENGTH)));
        }
        return sb.toString();
    }
} 