package pers.amos.service.shortkey;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.scripting.support.ResourceScriptSource;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * Redis分布式限流器
 * 使用Lua脚本确保限流操作的原子性
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisRateLimiter {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    private DefaultRedisScript<Long> rateLimiterScript;
    
    @PostConstruct
    public void init() {
        rateLimiterScript = new DefaultRedisScript<>();
        rateLimiterScript.setScriptSource(new ResourceScriptSource(
                new ClassPathResource("scripts/rate_limiter.lua")));
        rateLimiterScript.setResultType(Long.class);
    }
    
    /**
     * 判断请求是否允许通过
     * 
     * @param key 限流键
     * @param limit 时间窗口内的最大请求数
     * @param windowSeconds 时间窗口（秒）
     * @return 是否允许请求通过
     */
    public boolean isAllowed(String key, int limit, int windowSeconds) {
        try {
            List<String> keys = Collections.singletonList(key);
            Long result = stringRedisTemplate.execute(
                    rateLimiterScript,
                    keys,
                    String.valueOf(limit),
                    String.valueOf(windowSeconds)
            );
            
            return result != null && result == 1L;
        } catch (Exception e) {
            // 发生异常时，默认放行，避免限流器故障导致整个服务不可用
            log.error("限流器异常，key={}, limit={}, window={}", key, limit, windowSeconds, e);
            return true;
        }
    }
} 