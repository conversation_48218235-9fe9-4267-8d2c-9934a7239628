package pers.amos.service.bulletcomments.server;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.web.bind.annotation.*;
import pers.amos.service.bulletcomments.model.BulletCommentMessage;
import pers.amos.service.bulletcomments.mq.BulletMessageProducer;

/**
 * 弹幕REST API控制器
 * 提供HTTP接口发送弹幕消息
 */
@RestController
@RequestMapping("/api/bullet-comments")
@Slf4j
@RequiredArgsConstructor
public class BulletCommentRestController {

    private final SimpMessagingTemplate messagingTemplate;
    private final BulletMessageProducer bulletMessageProducer;

    /**
     * 向房间广播消息
     */
    @PostMapping("/broadcast/{roomId}")
    public String broadcast(@PathVariable String roomId, @RequestParam String message) {
        BulletCommentMessage bulletMessage = new BulletCommentMessage();
        bulletMessage.setType(BulletCommentMessage.MessageType.SYSTEM);
        bulletMessage.setUserId("admin");
        bulletMessage.setUsername("管理员");
        bulletMessage.setRoomId(roomId);
        bulletMessage.setContent(message);
        bulletMessage.setTimestamp(System.currentTimeMillis());

        // 使用消息生产者发送消息到MQ
        bulletMessageProducer.sendPriorityBulletMessage(bulletMessage);

        // 同时直接发送到WebSocket
        messagingTemplate.convertAndSend("/topic/room/" + roomId, bulletMessage);

        return "消息已广播到房间: " + roomId;
    }

    /**
     * 向特定用户发送私信消息
     */
    @PostMapping("/private/{roomId}/{userId}")
    public String sendPrivateMessage(@PathVariable String roomId,
                                     @PathVariable String userId,
                                     @RequestParam String message) {
        BulletCommentMessage bulletMessage = new BulletCommentMessage();
        bulletMessage.setType(BulletCommentMessage.MessageType.CHAT);
        bulletMessage.setUserId("admin");
        bulletMessage.setUsername("管理员");
        bulletMessage.setRoomId(roomId);
        bulletMessage.setContent(message);
        bulletMessage.setTimestamp(System.currentTimeMillis());

        // 发送私信消息
        messagingTemplate.convertAndSendToUser(userId, "/queue/private", bulletMessage);

        return "私信消息已发送给用户: " + userId;
    }

    /**
     * 发送系统通知
     */
    @PostMapping("/notification")
    public String sendNotification(@RequestParam String message) {
        BulletCommentMessage notification = new BulletCommentMessage();
        notification.setType(BulletCommentMessage.MessageType.SYSTEM);
        notification.setUserId("system");
        notification.setUsername("系统通知");
        notification.setContent(message);
        notification.setTimestamp(System.currentTimeMillis());

        // 发送系统通知
        messagingTemplate.convertAndSend("/topic/notifications", notification);

        return "系统通知已发送";
    }
} 