package pers.amos.service.bulletcomments.server;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import pers.amos.service.bulletcomments.model.BulletCommentMessage;
import pers.amos.service.bulletcomments.mq.BulletMessageProducer;

/**
 * 弹幕控制器
 * 处理WebSocket消息，实现生产者消费者模式
 */
@Controller
@Slf4j
@RequiredArgsConstructor
public class BulletCommentController {

    private final SimpMessagingTemplate messagingTemplate;
    private final BulletMessageProducer bulletMessageProducer;

    /**
     * 处理发送到房间的消息
     * 客户端发送消息到 /app/room/{roomId}/send
     * 消息将被广播到 /topic/room/{roomId}
     */
    @MessageMapping("/room/{roomId}/send")
    @SendTo("/topic/room/{roomId}")
    public BulletCommentMessage sendMessage(@DestinationVariable String roomId,
                                            @Payload BulletCommentMessage message,
                                            SimpMessageHeaderAccessor headerAccessor) {
        // 设置消息房间ID
        message.setRoomId(roomId);

        // 设置消息时间戳
        if (message.getTimestamp() == null) {
            message.setTimestamp(System.currentTimeMillis());
        }

        // 如果消息类型为空，设置为聊天消息
        if (message.getType() == null) {
            message.setType(BulletCommentMessage.MessageType.CHAT);
        }

        // 记录日志
        log.info("房间[{}]收到来自用户[{}]的消息: {}", roomId, message.getUsername(), message.getContent());

        // 使用消息生产者发送消息到MQ
        bulletMessageProducer.sendBulletMessageAsync(message);

        return message;
    }

    /**
     * 处理用户加入房间
     * 客户端发送消息到 /app/room/{roomId}/join
     */
    @MessageMapping("/room/{roomId}/join")
    @SendTo("/topic/room/{roomId}")
    public BulletCommentMessage addUser(@DestinationVariable String roomId,
                                        @Payload BulletCommentMessage message,
                                        SimpMessageHeaderAccessor headerAccessor) {
        // 在WebSocket会话中保存用户信息
        headerAccessor.getSessionAttributes().put("username", message.getUsername());
        headerAccessor.getSessionAttributes().put("userId", message.getUserId());
        headerAccessor.getSessionAttributes().put("roomId", roomId);

        // 设置消息类型为JOIN
        message.setType(BulletCommentMessage.MessageType.JOIN);
        message.setRoomId(roomId);
        message.setContent(message.getUsername() + " 加入了直播间");
        message.setTimestamp(System.currentTimeMillis());

        log.info("用户[{}]({})加入房间[{}]", message.getUsername(), message.getUserId(), roomId);

        // 使用消息生产者发送消息到MQ
        bulletMessageProducer.sendPriorityBulletMessage(message);

        return message;
    }

    /**
     * 向特定用户发送私信消息
     */
    public void sendPrivateMessage(String roomId, String userId, BulletCommentMessage message) {
        message.setTimestamp(System.currentTimeMillis());
        messagingTemplate.convertAndSendToUser(userId, "/queue/private", message);
        log.info("已向用户[{}]发送私信: {}", userId, message.getContent());
    }

    /**
     * 向房间广播系统消息
     */
    public void broadcastSystemMessage(String roomId, String content) {
        BulletCommentMessage message = new BulletCommentMessage();
        message.setType(BulletCommentMessage.MessageType.SYSTEM);
        message.setUserId("system");
        message.setUsername("系统消息");
        message.setRoomId(roomId);
        message.setContent(content);
        message.setTimestamp(System.currentTimeMillis());

        messagingTemplate.convertAndSend("/topic/room/" + roomId, message);
        log.info("已向房间[{}]广播系统消息: {}", roomId, content);
    }
} 