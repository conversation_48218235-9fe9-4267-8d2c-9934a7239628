package pers.amos.service.bulletcomments.mq;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.annotation.SelectorType;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Component;
import pers.amos.service.bulletcomments.model.BulletCommentMessage;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 弹幕消息消费者
 * 使用RocketMQ实现消息的解耦和削峰
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BulletMessageConsumer {

    /**
     * WebSocket消息模板
     */
    private final SimpMessagingTemplate messagingTemplate;
    
    /**
     * 消息处理统计
     */
    private final Map<String, AtomicLong> roomMessageCounter = new ConcurrentHashMap<>();
    
    /**
     * 消息处理线程池
     * 避免RocketMQ消费线程阻塞，提高吞吐量
     */
    private final Executor messageExecutor = new ThreadPoolExecutor(
            10, // 核心线程数
            50, // 最大线程数
            60L, // 空闲时间
            TimeUnit.SECONDS, // 时间单位
            new LinkedBlockingQueue<>(10000), // 队列容量
            r -> new Thread(r, "bullet-message-processor"), // 线程工厂
            new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略：调用者运行
    );
    
    /**
     * 普通弹幕消息监听器
     * 处理普通优先级的弹幕消息
     */
    //@Component
    @RocketMQMessageListener(
            topic = "BULLET_COMMENT_TOPIC",
            consumerGroup = "bullet-consumer-group",
            selectorType = SelectorType.TAG,
            selectorExpression = "*", // 监听所有房间
            consumeThreadMax = 20 // 最大消费线程数
    )
    public class NormalBulletMessageListener implements RocketMQListener<String> {
        
        @Override
        public void onMessage(String messageJson) {
            // 使用线程池异步处理消息，避免阻塞RocketMQ的消费线程
            messageExecutor.execute(() -> {
                try {
                    BulletCommentMessage message = JSONUtil.toBean(messageJson, BulletCommentMessage.class);
                    String roomId = message.getRoomId();
                    
                    // 发送消息到WebSocket Topic
                    messagingTemplate.convertAndSend("/topic/room/" + roomId, message);
                    
                    // 更新房间消息计数
                    roomMessageCounter.computeIfAbsent(roomId, k -> new AtomicLong())
                            .incrementAndGet();
                    
                    // 打印日志（每10000条打印一次，避免日志过多）
                    long total = roomMessageCounter.get(roomId).get();
                    if (total % 10000 == 0) {
                        log.info("房间[{}]弹幕消息处理累计: {}", roomId, total);
                    }
                } catch (Exception e) {
                    log.error("处理弹幕消息失败", e);
                }
            });
        }
    }
    
    /**
     * 高优先级弹幕消息监听器
     * 处理付费弹幕等优先级高的消息
     */
    //@Component
    @RocketMQMessageListener(
            topic = "BULLET_PRIORITY_TOPIC",
            consumerGroup = "bullet-priority-consumer-group",
            selectorType = SelectorType.TAG,
            selectorExpression = "*",
            consumeThreadMax = 10
    )
    public class PriorityBulletMessageListener implements RocketMQListener<String> {
        
        @Override
        public void onMessage(String messageJson) {
            try {
                BulletCommentMessage message = JSONUtil.toBean(messageJson, BulletCommentMessage.class);
                String roomId = message.getRoomId();
                
                // 高优先级消息，直接在当前线程处理，确保及时投递
                messagingTemplate.convertAndSend("/topic/room/" + roomId, message);
                
                log.info("高优先级弹幕消息已处理: roomId={}, userId={}", roomId, message.getUserId());
            } catch (Exception e) {
                log.error("处理高优先级弹幕消息失败", e);
            }
        }
    }
    
    /**
     * 获取房间消息处理数量
     *
     * @param roomId 房间ID
     * @return 消息数量
     */
    public long getRoomMessageCount(String roomId) {
        AtomicLong counter = roomMessageCounter.get(roomId);
        return counter != null ? counter.get() : 0;
    }
    
    /**
     * 获取总消息处理数量
     *
     * @return 总消息数量
     */
    public long getTotalMessageCount() {
        return roomMessageCounter.values().stream()
                .mapToLong(AtomicLong::get)
                .sum();
    }
} 