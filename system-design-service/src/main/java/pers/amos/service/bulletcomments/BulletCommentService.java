package pers.amos.service.bulletcomments;

import pers.amos.service.bulletcomments.model.BulletCommentMessage;

/**
 * 弹幕服务接口
 */
public interface BulletCommentService {
    
    /**
     * 发送弹幕消息到直播间
     * 
     * @param message 弹幕消息
     * @return 是否发送成功
     */
    boolean sendMessage(BulletCommentMessage message);
    
    /**
     * 获取直播间当前在线人数
     * 
     * @param roomId 直播间ID
     * @return 在线人数
     */
    int getRoomOnlineCount(String roomId);
    
    /**
     * 向指定用户发送私信消息
     * 
     * @param roomId 直播间ID
     * @param targetUserId 目标用户ID
     * @param message 弹幕消息
     * @return 是否发送成功
     */
    boolean sendPrivateMessage(String roomId, String targetUserId, BulletCommentMessage message);
} 