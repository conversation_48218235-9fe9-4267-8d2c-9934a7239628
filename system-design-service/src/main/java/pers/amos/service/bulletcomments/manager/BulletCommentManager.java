package pers.amos.service.bulletcomments.manager;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import pers.amos.service.bulletcomments.mq.BulletMessageProducer;
import pers.amos.service.bulletcomments.model.BulletCommentMessage;

/**
 * 弹幕消息管理器
 * 负责通过Redis发送和接收弹幕消息，同时支持RocketMQ分发广播消息
 * 实现消息削峰和系统解耦
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BulletCommentManager {

    /**
     * Redis操作模板
     */
    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * 弹幕消息生产者
     */
    private final BulletMessageProducer bulletMessageProducer;

    /**
     * 发送弹幕消息
     * 针对不同类型的弹幕采用不同的发送策略，实现削峰
     * 
     * @param message 弹幕消息
     */
    public void sendMessage(BulletCommentMessage message) {
        try {
            // 判断是否为高优先级消息（例如付费弹幕、管理员消息等）
            if (isHighPriorityMessage(message)) {
                // 高优先级消息直接同步发送，确保及时投递
                boolean success = bulletMessageProducer.sendPriorityBulletMessage(message);
                
                if (!success) {
                    // 降级处理：如果RocketMQ发送失败，尝试使用Redis发送
                    sendMessageByRedis(message);
                }
            } else {
                // 普通消息，使用异步方式发送，实现削峰
                boolean success = bulletMessageProducer.sendBulletMessageAsync(message);
                
                if (!success) {
                    // 降级处理：如果RocketMQ发送失败，尝试使用Redis发送
                    sendMessageByRedis(message);
                }
            }
        } catch (Exception e) {
            log.error("发送弹幕消息失败", e);
            
            // 降级处理：如果RocketMQ发送失败，尝试使用Redis发送
            sendMessageByRedis(message);
        }
    }
    
    /**
     * 判断是否为高优先级消息
     * 可根据业务需求自定义判断逻辑
     */
    private boolean isHighPriorityMessage(BulletCommentMessage message) {
        // 示例：判断是否为管理员消息或系统消息
        return "admin".equals(message.getUserId()) || 
               (message.getContent() != null && message.getContent().startsWith("系统消息"));
    }
    
    /**
     * 通过Redis发送弹幕消息
     */
    private void sendMessageByRedis(BulletCommentMessage message) {
        try {
            String channel = "bulletComment:channel:" + message.getRoomId();
            String messageJson = JSONUtil.toJsonStr(message);
            
            // 发布消息到Redis
            redisTemplate.convertAndSend(channel, messageJson);
            
            log.warn("RocketMQ发送失败，已降级使用Redis发送消息: {}", messageJson);
        } catch (Exception e) {
            log.error("Redis降级发送也失败", e);
        }
    }
} 