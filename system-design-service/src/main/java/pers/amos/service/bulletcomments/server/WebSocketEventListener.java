package pers.amos.service.bulletcomments.server;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.SimpMessageSendingOperations;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;
import pers.amos.service.bulletcomments.model.BulletCommentMessage;

/**
 * WebSocket事件监听器
 * 处理WebSocket连接和断开事件
 */
@Component
@Slf4j
public class WebSocketEventListener {

    @Resource
    private SimpMessageSendingOperations messagingTemplate;

    /**
     * 处理WebSocket连接事件
     */
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        log.info("收到新的WebSocket连接");
    }

    /**
     * 处理WebSocket断开连接事件
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        
        // 从会话中获取用户信息
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        String userId = (String) headerAccessor.getSessionAttributes().get("userId");
        String roomId = (String) headerAccessor.getSessionAttributes().get("roomId");

        if (username != null && roomId != null) {
            log.info("用户[{}]({})断开连接，房间[{}]", username, userId, roomId);
            
            // 创建离开消息
            BulletCommentMessage message = new BulletCommentMessage();
            message.setType(BulletCommentMessage.MessageType.LEAVE);
            message.setUserId(userId);
            message.setUsername(username);
            message.setRoomId(roomId);
            message.setContent(username + " 离开了直播间");
            message.setTimestamp(System.currentTimeMillis());

            // 发送消息到房间Topic
            messagingTemplate.convertAndSend("/topic/room/" + roomId, message);
        }
    }
} 