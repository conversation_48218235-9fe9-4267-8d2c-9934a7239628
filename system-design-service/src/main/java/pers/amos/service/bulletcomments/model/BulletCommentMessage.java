package pers.amos.service.bulletcomments.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 弹幕消息实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BulletCommentMessage {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户昵称
     */
    private String username;
    
    /**
     * 直播间ID
     */
    private String roomId;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息颜色（前端显示用）
     */
    private String color;
    
    /**
     * 消息发送时间
     */
    private Long timestamp;
    
    /**
     * 消息类型
     */
    private MessageType type;
    
    /**
     * 消息类型枚举
     */
    public enum MessageType {
        CHAT,   // 普通聊天消息
        JOIN,   // 用户加入
        LEAVE,  // 用户离开
        SYSTEM  // 系统消息
    }
    
    /**
     * 快速构造方法
     */
    public BulletCommentMessage(String content, String username, MessageType type) {
        this.content = content;
        this.username = username;
        this.type = type;
        this.timestamp = System.currentTimeMillis();
    }
} 