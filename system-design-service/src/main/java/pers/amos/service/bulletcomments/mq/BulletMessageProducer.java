package pers.amos.service.bulletcomments.mq;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;
import pers.amos.service.bulletcomments.model.BulletCommentMessage;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 弹幕消息生产者
 * 负责将弹幕消息发送到RocketMQ，实现系统解耦和流量削峰
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BulletMessageProducer {

    /**
     * RocketMQ模板
     */
    private final RocketMQTemplate rocketMQTemplate;
    
    /**
     * WebSocket消息模板
     */
    private final SimpMessagingTemplate simpMessagingTemplate;
    
    /**
     * 弹幕消息Topic
     */
    private static final String BULLET_TOPIC = "BULLET_COMMENT_TOPIC";
    
    /**
     * 高优先级弹幕消息Topic（付费弹幕等）
     */
    private static final String BULLET_PRIORITY_TOPIC = "BULLET_PRIORITY_TOPIC";
    
    /**
     * 消息计数器（用于统计）
     */
    private final AtomicLong messageCounter = new AtomicLong(0);
    
    /**
     * 异步发送弹幕消息（适用于普通弹幕，实现削峰）
     *
     * @param message 弹幕消息
     * @return 是否成功提交到消息队列
     */
    public boolean sendBulletMessageAsync(BulletCommentMessage message) {
        try {
            // 转换为JSON
            String messageJson = JSONUtil.toJsonStr(message);
            
            // 构建目标，格式为：topicName:tags
            String destination = BULLET_TOPIC + ":" + message.getRoomId();
            
            // 构建消息，设置消息键为发送者ID
            org.springframework.messaging.Message<String> msg = MessageBuilder
                    .withPayload(messageJson)
                    .setHeader("KEYS", message.getUserId())
                    .build();
            
            // 异步发送消息
            rocketMQTemplate.asyncSend(destination, msg, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    // 成功计数
                    long count = messageCounter.incrementAndGet();
                    if (count % 10000 == 0) {
                        log.info("弹幕消息发送累计: {}, 最近结果: {}", count, sendResult);
                    } else {
                        log.debug("弹幕消息发送成功: {}", sendResult);
                    }
                    
                    // 发送消息到WebSocket Topic
                    simpMessagingTemplate.convertAndSend("/topic/room/" + message.getRoomId(), message);
                }
                
                @Override
                public void onException(Throwable throwable) {
                    log.error("弹幕消息发送异常: {}:{}", message.getRoomId(), message.getUserId(), throwable);
                    
                    // 降级处理：直接通过WebSocket发送
                    simpMessagingTemplate.convertAndSend("/topic/room/" + message.getRoomId(), message);
                }
            });
            
            return true;
        } catch (Exception e) {
            log.error("提交弹幕消息失败: {}:{}", message.getRoomId(), message.getUserId(), e);
            
            // 降级处理：直接通过WebSocket发送
            simpMessagingTemplate.convertAndSend("/topic/room/" + message.getRoomId(), message);
            
            return false;
        }
    }
    
    /**
     * 同步发送高优先级弹幕消息（适用于付费弹幕、管理员消息等）
     *
     * @param message 弹幕消息
     * @return 是否成功发送
     */
    public boolean sendPriorityBulletMessage(BulletCommentMessage message) {
        try {
            // 转换为JSON
            String messageJson = JSONUtil.toJsonStr(message);
            
            // 构建目标，使用优先级Topic
            String destination = BULLET_PRIORITY_TOPIC + ":" + message.getRoomId();
            
            // 构建消息，设置消息键为发送者ID
            org.springframework.messaging.Message<String> msg = MessageBuilder
                    .withPayload(messageJson)
                    .setHeader("KEYS", message.getUserId())
                    .build();
            
            // 同步发送，确保高优先级消息的可靠投递
            SendResult result = rocketMQTemplate.syncSend(destination, msg);
            log.info("高优先级弹幕消息发送成功: {}:{}, 结果: {}", message.getRoomId(), message.getUserId(), result);
            
            // 发送消息到WebSocket Topic
            simpMessagingTemplate.convertAndSend("/topic/room/" + message.getRoomId(), message);
            
            return true;
        } catch (Exception e) {
            log.error("高优先级弹幕消息发送失败: {}:{}", message.getRoomId(), message.getUserId(), e);
            
            // 降级处理：直接通过WebSocket发送
            simpMessagingTemplate.convertAndSend("/topic/room/" + message.getRoomId(), message);
            
            return false;
        }
    }
    
    /**
     * 发送私信消息
     *
     * @param roomId 房间ID
     * @param targetUserId 目标用户ID
     * @param message 消息内容
     */
    public void sendPrivateMessage(String roomId, String targetUserId, BulletCommentMessage message) {
        try {
            message.setRoomId(roomId);
            if (message.getTimestamp() == null) {
                message.setTimestamp(System.currentTimeMillis());
            }
            
            // 直接通过WebSocket发送私信
            simpMessagingTemplate.convertAndSendToUser(targetUserId, "/queue/private", message);
            
            log.info("已向用户[{}]发送私信: {}", targetUserId, message.getContent());
        } catch (Exception e) {
            log.error("发送私信消息失败: {}", e.getMessage());
        }
    }
    
    /**
     * 获取已发送消息计数
     */
    public long getMessageCount() {
        return messageCounter.get();
    }
} 