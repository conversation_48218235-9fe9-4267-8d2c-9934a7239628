package pers.amos.service.bulletcomments.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import pers.amos.service.bulletcomments.BulletCommentService;
import pers.amos.service.bulletcomments.model.BulletCommentMessage;
import pers.amos.service.bulletcomments.mq.BulletMessageProducer;

/**
 * 弹幕服务实现类
 * 使用生产者/消费者模式实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BulletCommentServiceImpl implements BulletCommentService {
    
    /**
     * 弹幕消息生产者
     */
    private final BulletMessageProducer bulletMessageProducer;
    
    /**
     * WebSocket消息模板
     */
    private final SimpMessagingTemplate messagingTemplate;
    
    /**
     * Redis操作模板
     */
    private final RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 发送弹幕消息到直播间
     *
     * @param message 弹幕消息
     * @return 是否发送成功
     */
    @Override
    public boolean sendMessage(BulletCommentMessage message) {
        try {
            if (message.getTimestamp() == null) {
                message.setTimestamp(System.currentTimeMillis());
            }
            
            // 如果消息类型为空，设置为聊天消息
            if (message.getType() == null) {
                message.setType(BulletCommentMessage.MessageType.CHAT);
            }
            
            // 使用消息生产者发送消息
            return bulletMessageProducer.sendBulletMessageAsync(message);
        } catch (Exception e) {
            log.error("发送弹幕消息失败", e);
            return false;
        }
    }
    
    /**
     * 获取直播间当前在线人数
     *
     * @param roomId 直播间ID
     * @return 在线人数
     */
    @Override
    public int getRoomOnlineCount(String roomId) {
        String roomCountKey = "bulletComment:roomCount:" + roomId;
        Object count = redisTemplate.opsForValue().get(roomCountKey);
        return count != null ? Integer.parseInt(count.toString()) : 0;
    }
    
    /**
     * 向指定用户发送私信消息
     *
     * @param roomId 直播间ID
     * @param targetUserId 目标用户ID
     * @param message 弹幕消息
     * @return 是否发送成功
     */
    @Override
    public boolean sendPrivateMessage(String roomId, String targetUserId, BulletCommentMessage message) {
        try {
            if (message.getTimestamp() == null) {
                message.setTimestamp(System.currentTimeMillis());
            }
            
            // 设置房间ID
            message.setRoomId(roomId);
            
            // 如果消息类型为空，设置为聊天消息
            if (message.getType() == null) {
                message.setType(BulletCommentMessage.MessageType.CHAT);
            }
            
            // 使用WebSocket消息模板发送私信
            messagingTemplate.convertAndSendToUser(targetUserId, "/queue/private", message);
            
            return true;
        } catch (Exception e) {
            log.error("发送私信消息失败", e);
            return false;
        }
    }
} 