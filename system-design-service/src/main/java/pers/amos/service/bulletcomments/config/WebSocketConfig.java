package pers.amos.service.bulletcomments.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

/**
 * WebSocket配置类
 * 使用STOMP协议实现消息订阅发布模式
 */
@Configuration
@EnableWebSocketMessageBroker
@Slf4j
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

    /**
     * 配置消息代理
     */
    @Override
    public void configureMessageBroker(MessageBrokerRegistry registry) {
        // 配置消息代理的前缀，客户端订阅消息的目标地址前缀
        // /topic 用于广播消息，/queue 用于点对点消息
        registry.enableSimpleBroker("/topic", "/queue");
        
        // 配置客户端发送消息的目标前缀
        registry.setApplicationDestinationPrefixes("/app");
        
        // 配置点对点消息的用户目标前缀
        registry.setUserDestinationPrefix("/user");
    }

    /**
     * 注册STOMP端点
     */
    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        // 注册STOMP端点，允许使用SockJS
        registry.addEndpoint("/ws-bullet-comments")
                .setAllowedOriginPatterns("*")
                .withSockJS();
        
        log.info("WebSocket STOMP端点配置完成");
    }
} 