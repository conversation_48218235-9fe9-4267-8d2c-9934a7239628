package pers.amos.service.sentinel.config;

import com.alibaba.csp.sentinel.annotation.aspectj.SentinelResourceAspect;
import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRuleManager;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRule;
import com.alibaba.csp.sentinel.slots.block.flow.FlowRuleManager;
import com.alibaba.fastjson.JSON;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
@Slf4j
public class SentinelConfig {

    /**
     * 注册Sentinel AOP切面
     */
    @Bean
    public SentinelResourceAspect sentinelResourceAspect() {
        return new SentinelResourceAspect();
    }

    @PostConstruct
    public void init() {
        // 初始化流控规则
        initFlowRules();
        // 初始化熔断规则
        initDegradeRules();
        
        // 输出规则加载情况
        log.info("已成功加载流控规则: {}", JSON.toJSONString(FlowRuleManager.getRules()));
        log.info("已成功加载熔断规则: {}", JSON.toJSONString(DegradeRuleManager.getRules()));
    }

    /**
     * 初始化流控规则
     */
    private void initFlowRules() {
        List<FlowRule> rules = new ArrayList<>();
        
        // 创建流控规则1
        FlowRule rule1 = new FlowRule();
        rule1.setResource("getUserById"); // 资源名，可以是方法名
        rule1.setGrade(RuleConstant.FLOW_GRADE_QPS); // 基于QPS限流
        rule1.setCount(5); // 每秒允许5个请求
        rules.add(rule1);
        
        // 创建流控规则2
        FlowRule rule2 = new FlowRule();
        rule2.setResource("listUsers");
        rule2.setGrade(RuleConstant.FLOW_GRADE_QPS);
        rule2.setCount(2); // 每秒允许2个请求
        rules.add(rule2);
        
        // 加载规则
        FlowRuleManager.loadRules(rules);
    }

    /**
     * 初始化熔断规则
     */
    private void initDegradeRules() {
        List<DegradeRule> rules = new ArrayList<>();
        
        // 创建熔断规则1 - 异常比例
        DegradeRule rule1 = new DegradeRule();
        rule1.setResource("createOrder");
        rule1.setGrade(RuleConstant.DEGRADE_GRADE_EXCEPTION_RATIO); // 异常比例
        rule1.setCount(0.5); // 异常比例阈值，超过50%触发熔断
        rule1.setTimeWindow(10); // 熔断时长，单位为秒
        rule1.setMinRequestAmount(5); // 熔断触发的最小请求数
        rules.add(rule1);
        
        // 创建熔断规则2 - 慢调用比例
        DegradeRule rule2 = new DegradeRule();
        rule2.setResource("getOrderDetails");
        rule2.setGrade(RuleConstant.DEGRADE_GRADE_RT); // 慢调用比例
        rule2.setCount(100); // 慢调用RT阈值，单位是毫秒
        rule2.setTimeWindow(10); // 熔断时长，单位为秒
        rule2.setSlowRatioThreshold(0.5); // 慢调用比例阈值，超过50%触发熔断
        rules.add(rule2);
        
        // 加载规则
        DegradeRuleManager.loadRules(rules);
    }
} 