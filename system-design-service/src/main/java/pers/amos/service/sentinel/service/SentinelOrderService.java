package pers.amos.service.sentinel.service;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class SentinelOrderService {

    /**
     * 模拟创建订单，演示异常比例熔断
     */
    @SentinelResource(value = "createOrder", blockHandler = "createOrderBlockHandler", fallback = "createOrderFallback")
    public Map<String, Object> createOrder(String productId, Integer quantity) {
        // 模拟异常情况，70%的概率发生异常
        throw new RuntimeException("模拟订单创建异常");

//        Map<String, Object> order = new HashMap<>();
//        order.put("orderId", System.currentTimeMillis());
//        order.put("productId", productId);
//        order.put("quantity", quantity);
//        order.put("status", "created");
//        return order;
    }

    /**
     * 熔断降级处理方法
     */
    public Map<String, Object> createOrderBlockHandler(String productId, Integer quantity, BlockException e) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 429);
        result.put("message", "订单创建被熔断，请稍后重试");
        return result;
    }

    /**
     * 异常回退处理方法
     */
    public Map<String, Object> createOrderFallback(String productId, Integer quantity, Throwable e) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 500);
        result.put("message", "订单创建异常，请稍后重试");
        result.put("exception", e.getMessage());
        return result;
    }

    /**
     * 模拟获取订单详情，演示慢调用熔断
     */
    @SentinelResource(value = "getOrderDetails", blockHandler = "getOrderDetailsBlockHandler")
    public Map<String, Object> getOrderDetails(String orderId) {
        // 模拟慢调用，80%的概率是慢调用
        if (Math.random() < 0.8) {
            try {
                // 模拟耗时操作
                TimeUnit.MILLISECONDS.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        Map<String, Object> order = new HashMap<>();
        order.put("orderId", orderId);
        order.put("productId", "P" + orderId.substring(0, 3));
        order.put("quantity", 2);
        order.put("status", "paid");
        order.put("createTime", System.currentTimeMillis() - 100000);
        return order;
    }

    /**
     * 熔断降级处理方法
     */
    public Map<String, Object> getOrderDetailsBlockHandler(String orderId, BlockException e) {
        Map<String, Object> result = new HashMap<>();
        result.put("code", 429);
        result.put("message", "获取订单详情被熔断，请稍后重试");
        return result;
    }
} 