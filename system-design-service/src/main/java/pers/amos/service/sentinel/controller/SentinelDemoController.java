package pers.amos.service.sentinel.controller;

import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import pers.amos.service.sentinel.service.SentinelOrderService;
import pers.amos.service.sentinel.service.SentinelUserService;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/sentinel")
public class SentinelDemoController {

    @Resource
    private SentinelUserService sentinelUserService;

    @Resource
    private SentinelOrderService sentinelOrderService;

    /**
     * 测试限流 - 获取用户信息
     */
    @GetMapping("/user/{id}")
    public Map<String, Object> getUser(@PathVariable Long id) {
        return sentinelUserService.getUserById(id);
    }

    /**
     * 测试限流 - 获取用户列表
     */
    @GetMapping("/users")
    public List<Map<String, Object>> listUsers() {
        return sentinelUserService.listUsers();
    }

    /**
     * 测试熔断 - 创建订单
     */
    @PostMapping("/order")
    public Map<String, Object> createOrder(@RequestParam String productId, @RequestParam Integer quantity) {
        return sentinelOrderService.createOrder(productId, quantity);
    }

    /**
     * 测试熔断 - 获取订单详情
     */
    @GetMapping("/order/{orderId}")
    public Map<String, Object> getOrderDetails(@PathVariable String orderId) {
        return sentinelOrderService.getOrderDetails(orderId);
    }
} 