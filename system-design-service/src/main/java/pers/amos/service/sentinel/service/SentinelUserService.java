package pers.amos.service.sentinel.service;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import org.springframework.stereotype.Service;
import pers.amos.util.exception.BizException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SentinelUserService {

    /**
     * 模拟根据ID获取用户信息
     * 使用 @SentinelResource 注解标记资源
     */
    @SentinelResource(value = "getUserById", blockHandler = "getUserByIdBlockHandler")
    public Map<String, Object> getUserById(Long id) {
        Map<String, Object> user = new HashMap<>();
        user.put("id", id);
        user.put("name", "用户" + id);
        user.put("age", 20 + (id % 20));
        return user;
    }

    /**
     * 限流降级处理方法
     */
    public Map<String, Object> getUserByIdBlockHandler(Long id, BlockException e) {
        throw new BizException("限流了", "限流了");
    }

    /**
     * 模拟获取用户列表
     */
    @SentinelResource(value = "listUsers", blockHandler = "listUsersBlockHandler")
    public List<Map<String, Object>> listUsers() {
        List<Map<String, Object>> users = new ArrayList<>();
        for (long i = 1; i <= 10; i++) {
            Map<String, Object> user = new HashMap<>();
            user.put("id", i);
            user.put("name", "用户" + i);
            user.put("age", 20 + (i % 20));
            users.add(user);
        }
        return users;
    }

    /**
     * 限流降级处理方法
     */
    public List<Map<String, Object>> listUsersBlockHandler(BlockException e) {
        List<Map<String, Object>> emptyList = new ArrayList<>();
        Map<String, Object> error = new HashMap<>();
        error.put("code", 429);
        error.put("message", "获取用户列表被限流，请稍后重试");
        emptyList.add(error);
        return emptyList;
    }
} 