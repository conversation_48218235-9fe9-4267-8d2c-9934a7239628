# SpringBoot 整合 Sentinel 示例

本示例演示了 SpringBoot 整合 Alibaba Sentinel 的使用方法，包括限流和熔断功能。

## 功能特点

1. **限流功能**：
   - 基于 QPS 的限流
   - 通过 `@SentinelResource` 注解实现资源保护
   - 支持自定义限流处理逻辑

2. **熔断功能**：
   - 基于异常比例的熔断
   - 基于慢调用比例的熔断
   - 支持自定义熔断处理逻辑和异常回退处理

3. **Dashboard 整合**：
   - 支持 Sentinel Dashboard 监控和规则配置
   - Dashboard 地址：`localhost:10086`

4. **规则持久化**：
   - 支持将规则持久化到 JSON 文件

## 使用方法

### 启动 Sentinel Dashboard

```bash
# 下载 Sentinel Dashboard
wget https://github.com/alibaba/Sentinel/releases/download/1.8.6/sentinel-dashboard-1.8.6.jar

# 启动 Dashboard
java -Dserver.port=10086 -jar sentinel-dashboard-1.8.6.jar
```

### 测试限流功能

1. 快速连续访问以下接口，触发限流保护：
   - `GET /api/sentinel/user/{id}` - 获取用户信息（限流阈值：5 QPS）
   - `GET /api/sentinel/users` - 获取用户列表（限流阈值：2 QPS）

### 测试熔断功能

1. 访问以下接口，触发熔断保护：
   - `POST /api/sentinel/order?productId=xxx&quantity=1` - 创建订单（异常比例熔断）
   - `GET /api/sentinel/order/{orderId}` - 获取订单详情（慢调用比例熔断）

## 代码结构

- `config/SentinelConfig.java` - Sentinel 配置类
- `service/UserService.java` - 用户服务（演示限流）
- `service/OrderService.java` - 订单服务（演示熔断）
- `controller/SentinelDemoController.java` - 测试接口
- `resources/sentinel.properties` - Sentinel 配置文件
- `resources/sentinel/flow-rules.json` - 流控规则持久化文件
- `resources/sentinel/degrade-rules.json` - 熔断规则持久化文件

## 在 Dashboard 中查看和配置规则

1. 访问 `http://localhost:10086`
2. 登录（默认用户名/密码：sentinel/sentinel）
3. 在左侧菜单中选择"簇点链路"查看资源
4. 在资源上点击"流控"或"降级"按钮配置规则 