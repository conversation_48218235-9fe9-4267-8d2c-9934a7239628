package pers.amos.service.concurrent.threadlocal;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.alibaba.ttl.threadpool.TtlExecutors;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.concurrent.*;

/**
 * <AUTHOR> wong
 * @create 2024/10/16 10:29
 * @Description
 */
@Slf4j
public class ThreadLocalTest {

    @Test
    public void testThreadLocal() throws InterruptedException {
        ThreadLocal<String> initThreadLocal = new ThreadLocal<>();
        initThreadLocal.set("init");
        log.info("在主线程中获取到的值为：{}", initThreadLocal.get());

        // 使用ThreadLocal无法继承主线程中设置的值，下面的值为null
        Thread t1 = new Thread(() -> log.info("t1中获取到的值为：{}", initThreadLocal.get()));
        t1.start();
    }

    @Test
    public void testInheritableThreadLocal() throws Exception {
        // 使用inheritableThreadLocal可以继承主线程中设置的值
        InheritableThreadLocal<String> inheritableThreadLocal = new InheritableThreadLocal<>();
        inheritableThreadLocal.set("inheritable value");

        Thread t2 = new Thread(() -> {
            log.info("t2中获取到的值为：{}", inheritableThreadLocal.get());

            // 下面sleep是为了说明，后面main线程更改threadLocal的值，是不影响子线程中的值的
            ThreadUtil.sleep(2000);
            log.info("t2中获取到的值为：{}", inheritableThreadLocal.get());
        });
        t2.start();

        inheritableThreadLocal.set("重新设置value");
        t2.join();

        // inheritableThreadLocal的缺陷，在线程池中无法生效，因为线程池中的线程是复用的，继承父线程ThreadLocal的逻辑只在新创建线程的时候才会去执行
        // InheritableThreadLocal：它可以将父线程中的 ThreadLocal 值传递给子线程，但 线程池 会复用线程，导致：
        // 1.	复用线程的问题：线程池中的线程是复用的，在线程执行完一个任务后，会被再次用于执行另一个任务。
        // 由于 InheritableThreadLocal 的机制，新任务并不会继承父线程的上下文，只有新创建的线程才会继承，这导致在线程池中无法正常传递上下文信息。
        // 举例：我希望traceId是唯一的，如果用线程池则可能会导致所有线程的traceId都是一样的 当主线程更新traceId的时候，子线程不会刷新
        // 2.	信息污染问题：在线程池中，如果之前任务的 ThreadLocal 信息没有清除干净，新的任务会继承到错误的上下文信息，导致数据污染。
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        for (int i = 0; i < 5; i++) {
            executorService.execute(() -> log.info("线程池：{}，获取到的值为：{}", Thread.currentThread().getName(), inheritableThreadLocal.get()));
        }
    }

    @Test
    public void testTransmittableThreadLocal() {
        //	1.	任务提交时（Before Execute）：通过 Runnable 或 Callable 任务封装上下文信息，将当前线程的 ThreadLocal 值传递给工作线程。
        //	2.	任务执行后（After Execute）：在线程任务执行完毕后，恢复线程池工作线程的 ThreadLocal 值，避免影响后续任务。
        //        与 InheritableThreadLocal 的区别：
        //        1.	InheritableThreadLocal 只传递一次上下文，即创建子线程时传递父线程的 ThreadLocal 值。如果子线程被复用或线程池复用线程，InheritableThreadLocal 就无法保证数据正确传递。
        //        2.	TransmittableThreadLocal 在任务提交时传递上下文，保证在线程池中使用复用线程时，依然可以传递正确的 ThreadLocal 信息。
        TransmittableThreadLocal<String> traceId = new TransmittableThreadLocal<>();
        // 创建一个线程池并包装成 TtlExecutorService
        ExecutorService executorService = TtlExecutors.getTtlExecutorService(Executors.newFixedThreadPool(3));

        // 主线程中设置 traceId
        traceId.set("主线程的 TraceId：" + System.currentTimeMillis());

        // 提交多个任务到线程池中
        for (int i = 0; i < 5; i++) {
            if (i >= 3) {
                traceId.set("主线程的 TraceId：" + "110119");
            }

            executorService.execute(() -> {
                // 获取当前线程中的 traceId 并打印
                System.out.println(Thread.currentThread().getName() + " 获取到的 traceId：" + traceId.get());
            });
        }

        executorService.shutdown();
    }

    @Test
    public void testTransmittableThreadLocalCompareInheritableThreadLocal() {
        TransmittableThreadLocal<String> transmittableTraceId = new TransmittableThreadLocal<>();
        // 创建一个线程池并包装成 TtlExecutorService
        ExecutorService executorService = TtlExecutors.getTtlExecutorService(Executors.newFixedThreadPool(3));

        // 主线程中设置 transmittableTraceId
        transmittableTraceId.set("主线程的 transmittableTraceId：" + System.currentTimeMillis());

        // 提交多个任务到线程池中
        for (int i = 0; i < 5; i++) {
            if (i >= 3) {
                transmittableTraceId.set("主线程的 transmittableTraceId：" + "110119");
            }

            executorService.execute(() -> {
                // 获取当前线程中的 transmittableTraceId 并打印
                System.out.println(Thread.currentThread().getName() + " 获取到的 transmittableTraceId：" + transmittableTraceId.get());
            });
        }

        executorService.shutdown();
        ThreadUtil.sleep(2000);

        InheritableThreadLocal<String> inheritableThreadLocal = new InheritableThreadLocal<>();
        ExecutorService inheritableExecutorService = Executors.newFixedThreadPool(3);

        inheritableThreadLocal.set("主线程的 inheritableThreadLocal：" + System.currentTimeMillis());
        for (int i = 0; i < 5; i++) {
            if (i >= 3) {
                inheritableThreadLocal.set("主线程的 inheritableThreadLocal：" + "110119");
            }

            inheritableExecutorService.execute(() -> System.out.println(Thread.currentThread().getName() + " 获取到的 inheritableThreadLocal：" + inheritableThreadLocal.get()));
        }

        executorService.shutdown();
    }
}
