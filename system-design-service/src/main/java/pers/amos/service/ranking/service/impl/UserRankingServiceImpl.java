package pers.amos.service.ranking.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.integration.redis.util.RedisLockRegistry;
import org.springframework.stereotype.Service;
import pers.amos.service.ranking.model.UserRankEntry;
import pers.amos.service.ranking.service.UserRankingService;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 * 用户排行榜服务实现
 * 集群高性能版，使用Redis分布式锁保证多实例协调
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserRankingServiceImpl implements UserRankingService {

    @Autowired
    @Qualifier("stringRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private RedisLockRegistry redisLockRegistry;

    // Redis键前缀
    private static final String LEADERBOARD_KEY_PREFIX = "leaderboard:";
    private static final String VERSION_KEY_PREFIX = "leaderboard:version:";
    private static final String USER_INFO_KEY_PREFIX = "leaderboard:user:";
    private static final String LOCK_PREFIX = "leaderboard:lock:";

    // 本地缓存（L1缓存）
    private Cache<String, List<UserRankEntry>> topNCache;
    private Cache<String, UserRankEntry> userRankCache;
    private Cache<String, Long> versionCache;

    // Lua脚本用于原子性批量更新分数
    private final String batchUpdateScoreLuaScript = 
            "local leaderboardKey = KEYS[1]\n" +
            "local versionKey = KEYS[2]\n" +
            "local count = 0\n" +
            "for i=1,#ARGV,2 do\n" +
            "    local userId = ARGV[i]\n" +
            "    local score = ARGV[i+1]\n" +
            "    redis.call('ZADD', leaderboardKey, score, userId)\n" +
            "    count = count + 1\n" +
            "end\n" +
            "redis.call('INCR', versionKey)\n" +
            "return count";

    @PostConstruct
    public void init() {
        // 初始化本地缓存
        topNCache = Caffeine.newBuilder()
                .maximumSize(200) 
                .expireAfterWrite(2, TimeUnit.SECONDS)
                .build();

        userRankCache = Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(2, TimeUnit.SECONDS)
                .build();

        versionCache = Caffeine.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(1, TimeUnit.SECONDS)
                .build();

        log.info("集群版用户排行榜服务初始化完成");
    }

    // 获取排行榜对应的Redis键
    private String getLeaderboardKey(String leaderboardName) {
        return LEADERBOARD_KEY_PREFIX + leaderboardName;
    }

    // 获取版本号对应的Redis键
    private String getVersionKey(String leaderboardName) {
        return VERSION_KEY_PREFIX + leaderboardName;
    }

    // 获取用户信息对应的Redis键
    private String getUserInfoKey(String userId) {
        return USER_INFO_KEY_PREFIX + userId;
    }
    
    // 获取分布式锁的键
    private String getLockKey(String leaderboardName, String operation) {
        return LOCK_PREFIX + leaderboardName + ":" + operation;
    }

    /**
     * 更新用户分数（写操作）
     */
    @Override
    public boolean updateScore(String leaderboardName, String userId, double score, String username) {
        if (leaderboardName == null || userId == null) {
            return false;
        }

        String leaderboardKey = getLeaderboardKey(leaderboardName);
        String versionKey = getVersionKey(leaderboardName);
        String lockKey = getLockKey(leaderboardName, "write");
        
        Lock writeLock = redisLockRegistry.obtain(lockKey);
        boolean locked = false;
        try {
            // 尝试获取分布式锁，最多等待200毫秒
            locked = writeLock.tryLock(200, TimeUnit.MILLISECONDS);
            if (!locked) {
                log.warn("无法获取排行榜[{}]的写锁，更新操作被跳过", leaderboardName);
                return false;
            }
            
            // 1. 更新用户分数（使用Redis原子操作）
            Boolean added = redisTemplate.opsForZSet().add(leaderboardKey, userId, score);
            if (added == null || !added) {
                // 如果分数没有变化，Redis会返回false，不一定是错误
                Double oldScore = redisTemplate.opsForZSet().score(leaderboardKey, userId);
                if (oldScore != null && Math.abs(oldScore - score) < 0.000001) {
                    return true; // 分数相同，视为成功
                }
            }

            // 2. 如果提供了用户名，更新用户信息
            if (username != null && !username.isEmpty()) {
                redisTemplate.opsForHash().put(getUserInfoKey(userId), "username", username);
            }

            // 3. 更新全局版本号（使所有实例的缓存失效）
            Long newVersion = redisTemplate.opsForValue().increment(versionKey);
            
            // 4. 更新本地版本号缓存
            if (newVersion != null) {
                versionCache.put(versionKey, newVersion);
            }
            
            // 5. 清除相关本地缓存
            invalidateUserCaches(leaderboardName, userId);
            
            return true;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取排行榜[{}]的写锁时被中断", leaderboardName);
            return false;
        } catch (Exception e) {
            log.error("更新用户[{}]分数时发生错误，排行榜[{}]", userId, leaderboardName, e);
            return false;
        } finally {
            if (locked) {
                writeLock.unlock();
            }
        }
    }

    /**
     * 批量更新用户分数（写操作）
     */
    @Override
    public int batchUpdateScore(String leaderboardName, Map<String, Double> scoreMap) {
        if (leaderboardName == null || scoreMap == null || scoreMap.isEmpty()) {
            return 0;
        }

        String leaderboardKey = getLeaderboardKey(leaderboardName);
        String versionKey = getVersionKey(leaderboardName);
        String lockKey = getLockKey(leaderboardName, "write");
        
        Lock writeLock = redisLockRegistry.obtain(lockKey);
        boolean locked = false;
        try {
            // 尝试获取分布式锁，最多等待500毫秒
            locked = writeLock.tryLock(500, TimeUnit.MILLISECONDS);
            if (!locked) {
                log.warn("无法获取排行榜[{}]的写锁，批量更新操作被跳过", leaderboardName);
                return 0;
            }
            
            int size = scoreMap.size();
            
            if (size <= 50) {
                // 小批量更新，直接使用ZSetOperations
                Set<ZSetOperations.TypedTuple<String>> tuples = new HashSet<>(size);
                for (Map.Entry<String, Double> entry : scoreMap.entrySet()) {
                    tuples.add(new DefaultTypedTuple<>(entry.getKey(), entry.getValue()));
                }
                
                Long addedCount = redisTemplate.opsForZSet().add(leaderboardKey, tuples);
                
                // 更新版本号
                Long newVersion = redisTemplate.opsForValue().increment(versionKey);
                if (newVersion != null) {
                    versionCache.put(versionKey, newVersion);
                }
                
                // 清除相关缓存
                invalidateAllCaches(leaderboardName);
                
                return addedCount != null ? addedCount.intValue() : 0;
            } else {
                // 大批量更新，使用Lua脚本确保原子性
                List<String> keys = Arrays.asList(leaderboardKey, versionKey);
                List<String> args = new ArrayList<>(size * 2);
                
                for (Map.Entry<String, Double> entry : scoreMap.entrySet()) {
                    args.add(entry.getKey());
                    args.add(String.valueOf(entry.getValue()));
                }
                
                DefaultRedisScript<Long> script = new DefaultRedisScript<>();
                script.setScriptText(batchUpdateScoreLuaScript);
                script.setResultType(Long.class);
                
                Long result = redisTemplate.execute(script, keys, args.toArray());
                
                // 使所有相关缓存失效
                invalidateAllCaches(leaderboardName);
                
                return result != null ? result.intValue() : 0;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取排行榜[{}]的写锁时被中断", leaderboardName);
            return 0;
        } catch (Exception e) {
            log.error("批量更新排行榜[{}]分数时发生错误", leaderboardName, e);
            return 0;
        } finally {
            if (locked) {
                writeLock.unlock();
            }
        }
    }

    /**
     * 获取用户排名（读操作）
     */
    @Override
    public UserRankEntry getUserRank(String leaderboardName, String userId) {
        if (leaderboardName == null || userId == null) {
            return null;
        }

        String leaderboardKey = getLeaderboardKey(leaderboardName);
        String cacheKey = leaderboardName + ":" + userId;

        // 从本地缓存获取
        UserRankEntry cachedEntry = userRankCache.getIfPresent(cacheKey);
        if (cachedEntry != null) {
            // 检查版本号是否一致
            if (isVersionConsistent(leaderboardName)) {
                return cachedEntry;
            }
        }

        // 本地缓存不存在或版本不一致，需要查询Redis
        // 读操作不需要分布式锁，Redis的读操作是原子的
        try {
            // 获取用户分数
            Double score = redisTemplate.opsForZSet().score(leaderboardKey, userId);
            if (score == null) {
                return null; // 用户不在榜上
            }

            // 获取用户排名（使用ZREVRANK获取，高分在前）
            Long rank = redisTemplate.opsForZSet().reverseRank(leaderboardKey, userId);
            if (rank == null) {
                return null; // 由于某些原因无法获取排名
            }

            // 获取用户名
            String username = (String) redisTemplate.opsForHash().get(getUserInfoKey(userId), "username");

            // 构建结果对象
            UserRankEntry entry = UserRankEntry.builder()
                    .userId(userId)
                    .username(username)
                    .score(score)
                    .rank(rank + 1) // Redis排名从0开始，转为从1开始
                    .build();

            // 更新本地缓存
            userRankCache.put(cacheKey, entry);

            return entry;
        } catch (Exception e) {
            log.error("获取用户[{}]排名时发生错误，排行榜[{}]", userId, leaderboardName, e);
            return null;
        }
    }

    /**
     * 获取TopN排行榜（读操作）
     */
    @Override
    public List<UserRankEntry> getTopN(String leaderboardName, int n) {
        if (leaderboardName == null || n <= 0) {
            return Collections.emptyList();
        }

        String cacheKey = leaderboardName + ":top:" + n;

        // 从本地缓存获取
        List<UserRankEntry> cachedList = topNCache.getIfPresent(cacheKey);
        if (cachedList != null) {
            // 检查版本号是否一致
            if (isVersionConsistent(leaderboardName)) {
                return cachedList;
            }
        }

        // 本地缓存不存在或版本不一致，需要查询Redis
        return getRankRange(leaderboardName, 0, n - 1);
    }

    /**
     * 获取指定排名范围的用户（读操作）
     */
    @Override
    public List<UserRankEntry> getRankRange(String leaderboardName, int startRank, int endRank) {
        if (leaderboardName == null || startRank < 0 || endRank < startRank) {
            return Collections.emptyList();
        }

        String leaderboardKey = getLeaderboardKey(leaderboardName);
        String cacheKey = leaderboardName + ":range:" + startRank + ":" + endRank;

        // 检查本地缓存
        List<UserRankEntry> cachedList = topNCache.getIfPresent(cacheKey);
        if (cachedList != null && isVersionConsistent(leaderboardName)) {
            return cachedList;
        }

        // 从Redis获取数据
        try {
            // 使用ZREVRANGE，确保分数高的在前面
            Set<ZSetOperations.TypedTuple<String>> rangeWithScores = 
                    redisTemplate.opsForZSet().reverseRangeWithScores(leaderboardKey, startRank, endRank);
            
            if (rangeWithScores == null || rangeWithScores.isEmpty()) {
                return Collections.emptyList();
            }

            // 收集所有用户ID，用于批量获取用户名
            Set<String> userIds = rangeWithScores.stream()
                    .map(ZSetOperations.TypedTuple::getValue)
                    .collect(Collectors.toSet());

            // 批量获取用户信息
            Map<String, String> usernames = batchGetUsernames(userIds);

            // 组装结果
            List<UserRankEntry> result = new ArrayList<>();
            long currentRank = startRank + 1; // 转换为1-based排名
            
            for (ZSetOperations.TypedTuple<String> tuple : rangeWithScores) {
                String userId = tuple.getValue();
                Double score = tuple.getScore();
                
                UserRankEntry entry = UserRankEntry.builder()
                        .userId(userId)
                        .username(usernames.getOrDefault(userId, "未知用户"))
                        .score(score)
                        .rank(currentRank++)
                        .build();
                
                result.add(entry);
            }

            // 更新缓存
            topNCache.put(cacheKey, result);
            
            // 如果是Top N查询，同时更新TopN缓存
            if (startRank == 0) {
                String topNCacheKey = leaderboardName + ":top:" + (endRank + 1);
                topNCache.put(topNCacheKey, result);
            }

            return result;
        } catch (Exception e) {
            log.error("获取排行榜[{}]范围[{}-{}]时发生错误", leaderboardName, startRank, endRank, e);
            return Collections.emptyList();
        }
    }

    /**
     * 刷新排行榜缓存和预热
     * 在集群环境下，通过分布式锁确保只有一个节点执行预热
     */
    @Override
    public void refreshLeaderboard(String leaderboardName) {
        if (leaderboardName == null) {
            return;
        }

        String versionKey = getVersionKey(leaderboardName);
        String lockKey = getLockKey(leaderboardName, "refresh");
        
        Lock refreshLock = redisLockRegistry.obtain(lockKey);
        boolean locked = false;
        try {
            // 尝试获取分布式锁，最多等待200毫秒
            locked = refreshLock.tryLock(200, TimeUnit.MILLISECONDS);
            if (!locked) {
                log.debug("其他节点正在刷新排行榜[{}]，跳过本次刷新", leaderboardName);
                return;
            }
            
            // 1. 增加版本号
            Long newVersion = redisTemplate.opsForValue().increment(versionKey);
            
            // 2. 更新版本号缓存
            if (newVersion != null) {
                versionCache.put(versionKey, newVersion);
            }
            
            // 3. 使本地缓存失效
            invalidateAllCaches(leaderboardName);
            
            // 4. 预热TopN缓存（获得锁的节点执行预热）
            preheatTopNCache(leaderboardName);
            
            log.debug("排行榜[{}]刷新完成，新版本: {}", leaderboardName, newVersion);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("获取排行榜[{}]的刷新锁时被中断", leaderboardName);
        } catch (Exception e) {
            log.error("刷新排行榜[{}]时发生错误", leaderboardName, e);
        } finally {
            if (locked) {
                refreshLock.unlock();
            }
        }
    }

    /**
     * 获取排行榜当前版本号
     */
    @Override
    public long getLeaderboardVersion(String leaderboardName) {
        if (leaderboardName == null) {
            return 0;
        }

        String versionKey = getVersionKey(leaderboardName);
        
        // 先从本地缓存获取
        Long cachedVersion = versionCache.getIfPresent(versionKey);
        if (cachedVersion != null) {
            return cachedVersion;
        }
        
        // 缓存未命中，从Redis获取
        String versionStr = redisTemplate.opsForValue().get(versionKey);
        long version = versionStr != null ? Long.parseLong(versionStr) : 0;
        
        // 更新本地缓存
        versionCache.put(versionKey, version);
        
        return version;
    }

    /**
     * 预热TopN缓存
     */
    private void preheatTopNCache(String leaderboardName) {
        // 预热Top10和Top100缓存，这是最常用的查询
        try {
            getRankRange(leaderboardName, 0, 9);
            getRankRange(leaderboardName, 0, 99);
            log.debug("排行榜[{}]缓存预热完成", leaderboardName);
        } catch (Exception e) {
            log.warn("排行榜[{}]缓存预热失败", leaderboardName, e);
        }
    }

    /**
     * 使单个用户的相关缓存失效
     */
    private void invalidateUserCaches(String leaderboardName, String userId) {
        // 清除用户排名缓存
        userRankCache.invalidate(leaderboardName + ":" + userId);
        // 同时需要使Top相关缓存失效，因为该用户可能在TopN中
        invalidateTopCaches(leaderboardName);
    }

    /**
     * 使TopN相关的缓存失效
     */
    private void invalidateTopCaches(String leaderboardName) {
        // 过滤所有与此排行榜相关的缓存键并使其失效
        topNCache.asMap().keySet().stream()
                .filter(key -> key.startsWith(leaderboardName + ":"))
                .forEach(topNCache::invalidate);
    }

    /**
     * 使排行榜的所有相关缓存失效
     */
    private void invalidateAllCaches(String leaderboardName) {
        // 清除TopN相关缓存
        invalidateTopCaches(leaderboardName);
        
        // 清除用户排名缓存
        userRankCache.asMap().keySet().stream()
                .filter(key -> key.startsWith(leaderboardName + ":"))
                .forEach(userRankCache::invalidate);
    }

    /**
     * 批量获取用户名
     */
    private Map<String, String> batchGetUsernames(Set<String> userIds) {
        if (userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, String> result = new HashMap<>();
        
        // 为每个用户ID构建Redis键
        List<String> userInfoKeys = userIds.stream()
                .map(this::getUserInfoKey)
                .collect(Collectors.toList());
        
        // 使用管道批量获取
        List<Object> pipelineResults = redisTemplate.executePipelined((RedisConnection connection) -> {
            for (String key : userInfoKeys) {
                connection.hGet(key.getBytes(StandardCharsets.UTF_8), "username".getBytes(StandardCharsets.UTF_8));
            }
            return null;
        });
        
        // 处理结果
        int i = 0;
        for (String userId : userIds) {
            String username = (String) pipelineResults.get(i++);
            result.put(userId, username != null ? username : "未知用户");
        }
        
        return result;
    }

    /**
     * 检查本地缓存的版本是否与Redis一致
     */
    private boolean isVersionConsistent(String leaderboardName) {
        String versionKey = getVersionKey(leaderboardName);
        
        // 获取本地缓存的版本号
        Long cachedVersion = versionCache.getIfPresent(versionKey);
        if (cachedVersion == null) {
            return false; // 本地没有缓存版本号，认为不一致
        }
        
        // 获取Redis的版本号
        String redisVersionStr = redisTemplate.opsForValue().get(versionKey);
        if (redisVersionStr == null) {
            return false; // Redis中没有版本号，认为不一致
        }
        
        long redisVersion = Long.parseLong(redisVersionStr);
        
        // 比较版本号
        if (cachedVersion != redisVersion) {
            // 更新本地版本号缓存
            versionCache.put(versionKey, redisVersion);
            return false;
        }
        
        return true;
    }
} 