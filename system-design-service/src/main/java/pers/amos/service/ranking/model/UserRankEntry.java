package pers.amos.service.ranking.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户排行榜条目
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserRankEntry {
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名称
     */
    private String username;
    
    /**
     * 分数
     */
    private Double score;
    
    /**
     * 排名（从1开始）
     */
    private Long rank;
} 