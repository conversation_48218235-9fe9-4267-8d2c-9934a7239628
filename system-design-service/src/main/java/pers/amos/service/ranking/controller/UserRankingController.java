package pers.amos.service.ranking.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import pers.amos.service.ranking.model.UserRankEntry;
import pers.amos.service.ranking.service.UserRankingService;
import pers.amos.service.ranking.task.UserRankingRefreshTask;

import java.util.List;
import java.util.Map;

/**
 * 用户排行榜控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/ranking")
public class UserRankingController {

    @Autowired
    private UserRankingService userRankingService;
    
    @Autowired
    private UserRankingRefreshTask refreshTask;

    /**
     * 更新用户分数
     */
    @PostMapping("/{leaderboardName}/score")
    public boolean updateScore(
            @PathVariable String leaderboardName,
            @RequestParam String userId,
            @RequestParam double score,
            @RequestParam(required = false) String username) {
        return userRankingService.updateScore(leaderboardName, userId, score, username);
    }

    /**
     * 批量更新用户分数
     */
    @PostMapping("/{leaderboardName}/batch-update")
    public int batchUpdateScore(
            @PathVariable String leaderboardName,
            @RequestBody Map<String, Double> scoreMap) {
        return userRankingService.batchUpdateScore(leaderboardName, scoreMap);
    }

    /**
     * 获取用户排名
     */
    @GetMapping("/{leaderboardName}/user/{userId}")
    public UserRankEntry getUserRank(
            @PathVariable String leaderboardName,
            @PathVariable String userId) {
        return userRankingService.getUserRank(leaderboardName, userId);
    }

    /**
     * 获取TopN排行榜
     */
    @GetMapping("/{leaderboardName}/top")
    public List<UserRankEntry> getTopN(
            @PathVariable String leaderboardName,
            @RequestParam(defaultValue = "10") int n) {
        return userRankingService.getTopN(leaderboardName, n);
    }

    /**
     * 获取指定排名范围的用户
     */
    @GetMapping("/{leaderboardName}/range")
    public List<UserRankEntry> getRankRange(
            @PathVariable String leaderboardName,
            @RequestParam int start,
            @RequestParam int end) {
        return userRankingService.getRankRange(leaderboardName, start, end);
    }

    /**
     * 手动刷新排行榜
     */
    @PostMapping("/{leaderboardName}/refresh")
    public void refreshLeaderboard(@PathVariable String leaderboardName) {
        userRankingService.refreshLeaderboard(leaderboardName);
    }
    
    /**
     * 注册排行榜到定时刷新任务
     */
    @PostMapping("/{leaderboardName}/register")
    public void registerLeaderboard(@PathVariable String leaderboardName) {
        refreshTask.registerLeaderboard(leaderboardName);
    }
    
    /**
     * 取消注册排行榜的定时刷新
     */
    @PostMapping("/{leaderboardName}/unregister")
    public void unregisterLeaderboard(@PathVariable String leaderboardName) {
        refreshTask.unregisterLeaderboard(leaderboardName);
    }
} 