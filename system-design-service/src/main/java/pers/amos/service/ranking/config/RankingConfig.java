package pers.amos.service.ranking.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.integration.redis.util.RedisLockRegistry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 排行榜系统配置
 * 支持集群部署环境
 *
 * <AUTHOR>
 */
@Configuration
@EnableScheduling // 启用定时任务
@EnableAsync // 启用异步处理
public class RankingConfig {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    /**
     * 配置Redis分布式锁注册表
     * 用于集群环境下的分布式协调
     * 
     * @return RedisLockRegistry 实例
     */
    @Bean
    public RedisLockRegistry redisLockRegistry() {
        // registryKey: 用于识别分布式锁的键前缀
        // expireAfter: 锁的默认过期时间(毫秒)，防止死锁
        return new RedisLockRegistry(redisConnectionFactory, "ranking-locks", 30000);
    }

    /**
     * 配置异步任务执行器
     * 用于处理非关键路径操作，如缓存预热、数据同步等
     * 
     * @return 线程池执行器
     */
    @Bean("rankingTaskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(4);
        // 最大线程数
        executor.setMaxPoolSize(8);
        // 队列容量
        executor.setQueueCapacity(100);
        // 线程名前缀
        executor.setThreadNamePrefix("ranking-task-");
        // 拒绝策略：调用者运行，防止任务丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 初始化
        executor.initialize();
        return executor;
    }
} 