package pers.amos.service.ranking.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import pers.amos.service.ranking.service.UserRankingService;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 用户排行榜定时刷新任务
 * 每秒执行一次，确保排行榜数据及时更新
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserRankingRefreshTask {

    @Autowired
    private UserRankingService userRankingService;

    /**
     * 需要定时刷新的排行榜名称集合
     */
    private final Set<String> leaderboardNames = ConcurrentHashMap.newKeySet();

    /**
     * 刷新锁，防止任务并发执行
     */
    private final ReentrantLock refreshLock = new ReentrantLock();

    /**
     * 注册需要定时刷新的排行榜
     *
     * @param leaderboardName 排行榜名称
     */
    public void registerLeaderboard(String leaderboardName) {
        if (leaderboardName != null && !leaderboardName.isEmpty()) {
            leaderboardNames.add(leaderboardName);
            log.info("排行榜[{}]已注册定时刷新", leaderboardName);
        }
    }

    /**
     * 取消注册排行榜的定时刷新
     *
     * @param leaderboardName 排行榜名称
     */
    public void unregisterLeaderboard(String leaderboardName) {
        if (leaderboardName != null) {
            leaderboardNames.remove(leaderboardName);
            log.info("排行榜[{}]已取消定时刷新", leaderboardName);
        }
    }

    /**
     * 定时刷新任务
     * 每秒执行一次，刷新所有注册的排行榜
     */
    @Scheduled(fixedRate = 1000)
    public void refreshLeaderboards() {
        // 尝试获取锁，避免任务重叠执行
        if (!refreshLock.tryLock()) {
            return;
        }

        try {
            // 创建副本避免并发修改异常
            Set<String> copyOfNames = new HashSet<>(leaderboardNames);
            int successCount = 0;

            if (!copyOfNames.isEmpty()) {
                long startTime = System.currentTimeMillis();

                for (String leaderboardName : copyOfNames) {
                    try {
                        userRankingService.refreshLeaderboard(leaderboardName);
                        successCount++;
                    } catch (Exception e) {
                        log.error("刷新排行榜[{}]失败", leaderboardName, e);
                    }
                }

                long duration = System.currentTimeMillis() - startTime;
                if (duration > 500) {
                    // 如果刷新时间超过500ms，记录警告日志
                    log.warn("排行榜刷新耗时过长: {}ms，可能影响性能", duration);
                } else {
                    log.debug("成功刷新{}个排行榜，耗时{}ms", successCount, duration);
                }
            }
        } finally {
            refreshLock.unlock();
        }
    }
} 