package pers.amos.service.ranking.service;

import pers.amos.service.ranking.model.UserRankEntry;

import java.util.List;
import java.util.Map;

/**
 * 用户排行榜服务接口
 *
 * <AUTHOR>
 */
public interface UserRankingService {

    /**
     * 更新用户分数
     *
     * @param leaderboardName 排行榜名称，可支持多个排行榜
     * @param userId 用户ID
     * @param score 用户分数
     * @param username 用户名称（可选）
     * @return 是否更新成功
     */
    boolean updateScore(String leaderboardName, String userId, double score, String username);

    /**
     * 批量更新用户分数
     *
     * @param leaderboardName 排行榜名称
     * @param scoreMap 用户ID到分数的映射
     * @return 更新成功的条目数
     */
    int batchUpdateScore(String leaderboardName, Map<String, Double> scoreMap);

    /**
     * 获取用户排名
     *
     * @param leaderboardName 排行榜名称
     * @param userId 用户ID
     * @return 用户排名条目，如果用户不在榜上则返回null
     */
    UserRankEntry getUserRank(String leaderboardName, String userId);

    /**
     * 获取TopN排行榜
     *
     * @param leaderboardName 排行榜名称
     * @param n 需要获取的数量
     * @return 排行榜列表
     */
    List<UserRankEntry> getTopN(String leaderboardName, int n);

    /**
     * 获取指定排名范围的用户
     *
     * @param leaderboardName 排行榜名称
     * @param startRank 起始排名（从0开始）
     * @param endRank 结束排名
     * @return 指定范围的排行榜列表
     */
    List<UserRankEntry> getRankRange(String leaderboardName, int startRank, int endRank);

    /**
     * 刷新排行榜，使本地缓存失效并预热常用数据
     *
     * @param leaderboardName 排行榜名称
     */
    void refreshLeaderboard(String leaderboardName);

    /**
     * 获取排行榜当前版本号
     *
     * @param leaderboardName 排行榜名称
     * @return 当前版本号
     */
    long getLeaderboardVersion(String leaderboardName);
} 