package pers.amos.service.ranking.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pers.amos.service.ranking.model.UserRankEntry;
import pers.amos.service.ranking.service.UserRankingService;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 排行榜性能基准测试工具
 * 用于测试排行榜系统在高并发场景下的性能
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RankingBenchmarkUtil {

    @Autowired
    private UserRankingService userRankingService;

    private static final String TEST_LEADERBOARD = "benchmark_test";
    private static final int CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;
    private static final int MAX_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 4;
    private static final int USER_COUNT = 10000; // 10000个测试用户
    
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            CORE_POOL_SIZE,
            MAX_POOL_SIZE,
            60L, 
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100000),
            new ThreadFactory() {
                private final AtomicInteger counter = new AtomicInteger(1);
                @Override
                public Thread newThread(Runnable r) {
                    return new Thread(r, "benchmark-thread-" + counter.getAndIncrement());
                }
            },
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    /**
     * 初始化测试数据
     */
    public void initTestData() {
        log.info("开始初始化测试数据...");
        
        // 批量更新用户分数
        Map<String, Double> scoreMap = new HashMap<>(USER_COUNT);
        for (int i = 1; i <= USER_COUNT; i++) {
            String userId = "user_" + i;
            double score = ThreadLocalRandom.current().nextDouble(0, 10000);
            scoreMap.put(userId, score);
        }
        
        int count = userRankingService.batchUpdateScore(TEST_LEADERBOARD, scoreMap);
        log.info("测试数据初始化完成，共写入{}条记录", count);
    }

    /**
     * 测试读取性能
     * 
     * @param threads 并发线程数
     * @param queriesPerThread 每个线程执行的查询次数
     * @param topN 每次查询的TopN数量
     * @return 每秒查询次数(QPS)
     */
    public double testReadPerformance(int threads, int queriesPerThread, int topN) {
        log.info("开始测试读取性能，线程数: {}，每线程查询次数: {}，TopN: {}", threads, queriesPerThread, topN);
        
        CountDownLatch latch = new CountDownLatch(threads);
        AtomicLong totalQueryTime = new AtomicLong(0);
        final int totalQueries = threads * queriesPerThread;

        long startTime = System.currentTimeMillis();
        
        // 启动多个线程进行并发查询
        for (int i = 0; i < threads; i++) {
            executor.execute(() -> {
                try {
                    for (int j = 0; j < queriesPerThread; j++) {
                        long queryStart = System.nanoTime();
                        List<UserRankEntry> result = userRankingService.getTopN(TEST_LEADERBOARD, topN);
                        long queryTime = System.nanoTime() - queryStart;
                        totalQueryTime.addAndGet(queryTime);
                        
                        // 简单校验结果，不影响性能测试
                        if (result.isEmpty() && j == 0) {
                            log.warn("查询结果为空，请检查测试数据是否正确初始化");
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            // 等待所有线程完成
            latch.await();
            long duration = System.currentTimeMillis() - startTime;
            double qps = totalQueries * 1000.0 / duration;
            double avgQueryTimeMs = (totalQueryTime.get() / 1_000_000.0) / totalQueries;
            
            log.info("读取性能测试完成: 总查询次数: {}, 总耗时: {}ms, QPS: {}, 平均查询耗时: {}ms", 
                    totalQueries, duration, String.format("%.2f", qps), String.format("%.3f", avgQueryTimeMs));
            
            return qps;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("性能测试被中断", e);
            return 0;
        }
    }

    /**
     * 测试写入性能
     * 
     * @param threads 并发线程数
     * @param updatesPerThread 每个线程执行的更新次数
     * @param batchSize 每次批量更新的数量
     * @return 每秒更新次数(UPS)
     */
    public double testWritePerformance(int threads, int updatesPerThread, int batchSize) {
        log.info("开始测试写入性能，线程数: {}，每线程更新次数: {}，批量大小: {}", threads, updatesPerThread, batchSize);
        
        CountDownLatch latch = new CountDownLatch(threads);
        AtomicLong totalUpdateTime = new AtomicLong(0);
        final int totalUpdates = threads * updatesPerThread;
        final int totalRecords = totalUpdates * batchSize;

        long startTime = System.currentTimeMillis();
        
        // 启动多个线程进行并发更新
        for (int i = 0; i < threads; i++) {
            final int threadIdx = i;
            executor.execute(() -> {
                try {
                    for (int j = 0; j < updatesPerThread; j++) {
                        // 构建批量更新数据
                        Map<String, Double> batchUpdate = new HashMap<>(batchSize);
                        for (int k = 0; k < batchSize; k++) {
                            int userIdx = ThreadLocalRandom.current().nextInt(USER_COUNT) + 1;
                            String userId = "user_" + userIdx;
                            double score = ThreadLocalRandom.current().nextDouble(0, 10000);
                            batchUpdate.put(userId, score);
                        }
                        
                        long updateStart = System.nanoTime();
                        int updated = userRankingService.batchUpdateScore(TEST_LEADERBOARD, batchUpdate);
                        long updateTime = System.nanoTime() - updateStart;
                        totalUpdateTime.addAndGet(updateTime);
                        
                        if (updated == 0 && j == 0) {
                            log.warn("更新失败，没有记录被更新");
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        try {
            // 等待所有线程完成
            latch.await();
            long duration = System.currentTimeMillis() - startTime;
            double ups = totalUpdates * 1000.0 / duration;
            double rps = totalRecords * 1000.0 / duration;
            double avgUpdateTimeMs = (totalUpdateTime.get() / 1_000_000.0) / totalUpdates;
            
            log.info("写入性能测试完成: 总批次: {}, 总记录: {}, 总耗时: {}ms, 批次/秒: {}, 记录/秒: {}, 平均批次耗时: {}ms", 
                    totalUpdates, totalRecords, duration, String.format("%.2f", ups), 
                    String.format("%.2f", rps), String.format("%.3f", avgUpdateTimeMs));
            
            return rps;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("性能测试被中断", e);
            return 0;
        }
    }

    /**
     * 测试混合读写性能，模拟真实场景
     * 
     * @param readThreads 读取线程数
     * @param writeThreads 写入线程数
     * @param duration 测试持续时间(秒)
     */
    public void testMixedPerformance(int readThreads, int writeThreads, int duration) {
        log.info("开始测试混合读写性能，读线程: {}，写线程: {}，持续时间: {}秒", readThreads, writeThreads, duration);
        
        final CyclicBarrier barrier = new CyclicBarrier(readThreads + writeThreads + 1);
        final AtomicLong readCount = new AtomicLong(0);
        final AtomicLong writeCount = new AtomicLong(0);
        final AtomicBoolean running = new AtomicBoolean(true);
        
        List<Future<?>> futures = new ArrayList<>();
        
        // 启动读线程
        for (int i = 0; i < readThreads; i++) {
            futures.add(executor.submit(() -> {
                try {
                    barrier.await(); // 等待所有线程就绪
                    
                    while (running.get()) {
                        userRankingService.getTopN(TEST_LEADERBOARD, 10);
                        readCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    log.error("读线程异常", e);
                }
            }));
        }
        
        // 启动写线程
        for (int i = 0; i < writeThreads; i++) {
            futures.add(executor.submit(() -> {
                try {
                    barrier.await(); // 等待所有线程就绪
                    
                    while (running.get()) {
                        int userIdx = ThreadLocalRandom.current().nextInt(USER_COUNT) + 1;
                        String userId = "user_" + userIdx;
                        double score = ThreadLocalRandom.current().nextDouble(0, 10000);
                        userRankingService.updateScore(TEST_LEADERBOARD, userId, score, null);
                        writeCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    log.error("写线程异常", e);
                }
            }));
        }
        
        try {
            // 等待所有线程就绪后开始计时
            barrier.await();
            log.info("所有线程已就绪，开始计时...");
            
            // 运行指定时间
            Thread.sleep(duration * 1000L);
            running.set(false);
            
            // 简单计算QPS
            double readQps = readCount.get() * 1.0 / duration;
            double writeQps = writeCount.get() * 1.0 / duration;
            
            log.info("混合性能测试完成：总读取: {}, 读QPS: {}, 总写入: {}, 写QPS: {}, 总QPS: {}", 
                    readCount.get(), String.format("%.2f", readQps), 
                    writeCount.get(), String.format("%.2f", writeQps),
                    String.format("%.2f", readQps + writeQps));
            
        } catch (Exception e) {
            log.error("混合测试异常", e);
        } finally {
            // 停止所有任务
            running.set(false);
            
            // 取消所有未完成的任务
            for (Future<?> future : futures) {
                future.cancel(true);
            }
        }
    }

    /**
     * 完整的性能测试套件，包括读写测试，为验证系统能否支持100万QPS
     */
    public void runFullBenchmarkSuite() {
        try {
            // 首先初始化测试数据
            initTestData();
            Thread.sleep(1000); // 等待1秒确保数据写入完成
            
            // 1. 测试读性能
            // 低并发测试
            testReadPerformance(10, 1000, 10);
            Thread.sleep(1000);
            
            // 中等并发测试
            testReadPerformance(50, 1000, 10);
            Thread.sleep(1000);
            
            // 高并发测试
            testReadPerformance(200, 1000, 10);
            Thread.sleep(1000);
            
            // 2. 测试写性能
            // 低并发测试
            testWritePerformance(5, 100, 10);
            Thread.sleep(1000);
            
            // 中等并发测试
            testWritePerformance(20, 100, 10);
            Thread.sleep(1000);
            
            // 3. 测试混合性能（读写比例10:1）
            testMixedPerformance(100, 10, 10);
            
            log.info("完整性能测试套件执行完毕");
            
        } catch (Exception e) {
            log.error("性能测试套件执行异常", e);
        } finally {
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
} 