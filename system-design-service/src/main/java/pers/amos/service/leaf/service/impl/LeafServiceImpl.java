package pers.amos.service.leaf.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pers.amos.service.leaf.LeafFactory;
import pers.amos.service.leaf.Result;
import pers.amos.service.leaf.service.LeafService;

/**
 * Leaf ID生成服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class LeafServiceImpl implements LeafService {
    
    @Autowired
    private LeafFactory leafFactory;
    
    @Override
    public long nextId(String bizTag) {
        if (bizTag == null || bizTag.isEmpty()) {
            throw new IllegalArgumentException("业务标签不能为空");
        }
        
        // 获取下一个ID
        Result result = leafFactory.getId(bizTag);
        if (result.getStatus() == Result.Status.EXCEPTION) {
            log.error("获取业务[{}]的ID失败", bizTag);
            throw new RuntimeException("获取ID失败");
        }
        
        return result.getId();
    }
    
    @Override
    public long[] nextIds(String bizTag, int size) {
        if (bizTag == null || bizTag.isEmpty()) {
            throw new IllegalArgumentException("业务标签不能为空");
        }
        
        if (size <= 0 || size > 10000) {
            throw new IllegalArgumentException("批量大小必须在1-10000之间");
        }
        
        // 批量获取ID
        long[] ids = new long[size];
        for (int i = 0; i < size; i++) {
            Result result = leafFactory.getId(bizTag);
            if (result.getStatus() == Result.Status.EXCEPTION) {
                log.error("批量获取业务[{}]的ID失败", bizTag);
                throw new RuntimeException("批量获取ID失败");
            }
            ids[i] = result.getId();
        }
        
        return ids;
    }
} 