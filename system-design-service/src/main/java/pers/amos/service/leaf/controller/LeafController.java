package pers.amos.service.leaf.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import pers.amos.service.common.Result;
import pers.amos.service.leaf.service.LeafService;

import java.util.*;

/**
 * Leaf ID生成控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/leaf")
public class LeafController {
    
    @Autowired
    private LeafService leafService;
    
    /**
     * 获取下一个ID
     * 
     * @param bizTag 业务标签
     * @return ID
     */
    @GetMapping("/id/{bizTag}")
    public Result<Long> getNextId(@PathVariable String bizTag) {
        try {
            long id = leafService.nextId(bizTag);
            return Result.success(id);
        } catch (Exception e) {
            log.error("获取业务[{}]的ID失败", bizTag, e);
            return Result.fail("LEAF_ID_ERROR", "获取ID失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量获取ID
     * 
     * @param bizTag 业务标签
     * @param size 批量大小
     * @return ID数组
     */
    @GetMapping("/ids/{bizTag}")
    public Result<long[]> getNextIds(@PathVariable String bizTag, @RequestParam(defaultValue = "10") int size) {
        try {
            long[] ids = leafService.nextIds(bizTag, size);
            return Result.success(ids);
        } catch (Exception e) {
            log.error("批量获取业务[{}]的ID失败, size={}", bizTag, size, e);
            return Result.fail("LEAF_IDS_ERROR", "批量获取ID失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取Leaf服务状态
     * 
     * @return 服务状态
     */
    @GetMapping("/status")
    public Result<Map<String, Object>> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("service", "leaf");
        status.put("status", "running");
        status.put("timestamp", System.currentTimeMillis());
        return Result.success(status);
    }
} 