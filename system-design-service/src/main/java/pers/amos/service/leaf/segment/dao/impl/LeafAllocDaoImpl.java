package pers.amos.service.leaf.segment.dao.impl;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import pers.amos.service.leaf.segment.dao.LeafAllocDao;
import pers.amos.service.leaf.segment.model.LeafAlloc;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 号段分配数据库访问实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Repository
public class LeafAllocDaoImpl implements LeafAllocDao {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Override
    public List<String> getAllTags() {
        String sql = "SELECT biz_tag FROM leaf_alloc";
        return jdbcTemplate.queryForList(sql, String.class);
    }
    
    @Override
    public LeafAlloc getLeafAlloc(String tag) {
        String sql = "SELECT biz_tag, max_id, step, description, update_time, is_seq_finished FROM leaf_alloc WHERE biz_tag = ?";
        List<LeafAlloc> list = jdbcTemplate.query(sql, new LeafAllocRowMapper(), tag);
        return !list.isEmpty() ? list.get(0) : null;
    }
    
    @Override
    public void updateMaxIdAndSetLeafFinished(String tag, Long maxId) {
        String sql = "UPDATE leaf_alloc SET max_id = ?, update_time = now(), is_seq_finished = ? WHERE biz_tag = ?";
        jdbcTemplate.update(sql, maxId, 1, tag);
    }
    
    @Override
    public boolean updateMaxId(String tag, Long newMaxId, Long oldMaxId) {
        String sql = "UPDATE leaf_alloc SET max_id = ?, update_time = now() WHERE biz_tag = ? AND max_id = ?";
        int updated = jdbcTemplate.update(sql, newMaxId, tag, oldMaxId);
        return updated > 0;
    }
    
    @Override
    public List<LeafAlloc> getAllLeafAllocs() {
        String sql = "SELECT biz_tag, max_id, step, description, update_time, is_seq_finished FROM leaf_alloc";
        return jdbcTemplate.query(sql, new LeafAllocRowMapper());
    }
    
    /**
     * LeafAlloc行映射器
     */
    private static class LeafAllocRowMapper implements RowMapper<LeafAlloc> {
        @Override
        public LeafAlloc mapRow(ResultSet rs, int rowNum) throws SQLException {
            LeafAlloc leafAlloc = new LeafAlloc();
            leafAlloc.setBizTag(rs.getString("biz_tag"));
            leafAlloc.setMaxId(rs.getLong("max_id"));
            leafAlloc.setStep(rs.getInt("step"));
            leafAlloc.setDescription(rs.getString("description"));
            leafAlloc.setUpdateTime(rs.getString("update_time"));
            leafAlloc.setIsSeqFinished(rs.getInt("is_seq_finished"));
            return leafAlloc;
        }
    }
} 