package pers.amos.service.leaf;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import pers.amos.service.leaf.segment.SegmentIDGenImpl;
import pers.amos.service.leaf.snowflake.SnowflakeIDGenImpl;

import jakarta.annotation.PostConstruct;

/**
 * Leaf ID生成器工厂类
 * 支持两种ID生成模式：
 * 1. Segment号段模式：基于数据库实现，适合分布式场景下保证ID连续性
 * 2. Snowflake雪花算法：适合分布式场景下保证ID唯一性，性能更高
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class LeafFactory {
    
    /**
     * ID生成模式
     */
    public enum Mode {
        /**
         * 号段模式
         */
        SEGMENT,
        
        /**
         * 雪花算法模式
         */
        SNOWFLAKE
    }
    
    @Value("${leaf.mode:SNOWFLAKE}")
    private String leafMode;
    
    @Autowired
    private SegmentIDGenImpl segmentIDGen;
    
    @Autowired
    private SnowflakeIDGenImpl snowflakeIDGen;
    
    private IDGen idGen;
    
    @PostConstruct
    public void init() {
        // 根据配置的模式选择ID生成器
        Mode mode = Mode.valueOf(leafMode.toUpperCase());
        switch (mode) {
            case SEGMENT:
                idGen = segmentIDGen;
                log.info("使用号段模式作为ID生成器");
                break;
            case SNOWFLAKE:
                idGen = snowflakeIDGen;
                log.info("使用雪花算法模式作为ID生成器");
                break;
            default:
                idGen = snowflakeIDGen;
                log.info("未指定模式或模式无效，默认使用雪花算法模式作为ID生成器");
        }
        
        // 初始化ID生成器
        boolean initSuccess = idGen.init();
        if (!initSuccess) {
            log.error("ID生成器初始化失败");
            throw new RuntimeException("ID生成器初始化失败");
        }
    }
    
    /**
     * 获取下一个ID
     * 
     * @param key 业务键
     * @return ID生成结果
     */
    public Result getId(String key) {
        return idGen.get(key);
    }
    
    /**
     * 获取下一个ID的值
     * 
     * @param key 业务键
     * @return ID值
     */
    public long getNextId(String key) {
        return idGen.get(key).getId();
    }
} 