package pers.amos.service.leaf.segment.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 号段分配表对应的实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("leaf_alloc")
public class LeafAlloc {
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 业务标签
     */
    @TableField("biz_tag")
    private String bizTag;
    
    /**
     * 最大ID
     */
    @TableField("max_id")
    private Long maxId;
    
    /**
     * 步长
     */
    @TableField("step")
    private Integer step;
    
    /**
     * 描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private String updateTime;
    
    /**
     * 当前号段是否用完
     */
    @TableField("is_seq_finished")
    private Integer isSeqFinished;
} 