package pers.amos.service.leaf.segment.model;

import lombok.Data;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 号段缓冲区，实现双Buffer机制
 * 
 * <AUTHOR>
 */
@Data
public class SegmentBuffer {
    
    /**
     * 业务标签
     */
    private String bizTag;
    
    /**
     * 双缓冲区
     */
    private Segment[] segments = new Segment[]{new Segment(), new Segment()};
    
    /**
     * 当前使用的缓冲区索引，0或1
     */
    private volatile int currentPos = 0;
    
    /**
     * 下一个缓冲区是否处于初始化状态
     */
    private volatile boolean nextReady = false;
    
    /**
     * 是否处于初始化中
     */
    private AtomicBoolean threadRunning = new AtomicBoolean(false);
    
    /**
     * 读写锁
     */
    private ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    
    /**
     * 获取当前号段
     */
    public Segment getCurrent() {
        return segments[currentPos];
    }
    
    /**
     * 获取下一个号段
     */
    public Segment getNext() {
        return segments[1 - currentPos];
    }
    
    /**
     * 切换到下一个缓冲区
     */
    public void switchPos() {
        currentPos = 1 - currentPos;
    }
    
    @Override
    public String toString() {
        return "SegmentBuffer{" +
                "bizTag='" + bizTag + '\'' +
                ", segments=" + Arrays.toString(segments) +
                ", currentPos=" + currentPos +
                ", nextReady=" + nextReady +
                '}';
    }
} 