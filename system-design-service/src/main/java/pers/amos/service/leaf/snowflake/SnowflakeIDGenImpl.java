package pers.amos.service.leaf.snowflake;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import pers.amos.service.leaf.IDGen;
import pers.amos.service.leaf.Result;

import jakarta.annotation.PostConstruct;
import java.util.Random;

/**
 * 基于雪花算法的ID生成器实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SnowflakeIDGenImpl implements IDGen {

    /**
     * 起始时间戳，从2023-01-01 00:00:00开始
     */
    private final long twepoch = 1672502400000L;

    /**
     * 机器ID所占位数
     */
    private final long workerIdBits = 5L;

    /**
     * 数据中心ID所占位数
     */
    private final long datacenterIdBits = 5L;

    /**
     * 支持的最大机器ID，31
     */
    private final long maxWorkerId = ~(-1L << workerIdBits);

    /**
     * 支持的最大数据中心ID，31
     */
    private final long maxDatacenterId = ~(-1L << datacenterIdBits);

    /**
     * 序列号所占位数
     */
    private final long sequenceBits = 12L;

    /**
     * 机器ID左移位数
     */
    private final long workerIdShift = sequenceBits;

    /**
     * 数据中心ID左移位数
     */
    private final long datacenterIdShift = sequenceBits + workerIdBits;

    /**
     * 时间戳左移位数
     */
    private final long timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;

    /**
     * 序列号掩码，4095
     */
    private final long sequenceMask = ~(-1L << sequenceBits);

    /**
     * 工作机器ID
     */
    @Value("${snowflake.worker-id:-1}")
    private long workerId;

    /**
     * 数据中心ID
     */
    @Value("${snowflake.datacenter-id:-1}")
    private long datacenterId;

    /**
     * 序列号，初始值0，从0开始
     */
    private long sequence = 0L;

    /**
     * 上次生成ID的时间戳
     */
    private long lastTimestamp = -1L;

    @PostConstruct
    public void initSnowflake() {
        if (workerId == -1) {
            workerId = generateRandomWorkerId();
        }
        if (datacenterId == -1) {
            datacenterId = generateRandomDatacenterId();
        }
        
        // 校验参数
        if (workerId > maxWorkerId || workerId < 0) {
            throw new IllegalArgumentException(String.format("Worker ID can't be greater than %d or less than 0", maxWorkerId));
        }
        if (datacenterId > maxDatacenterId || datacenterId < 0) {
            throw new IllegalArgumentException(String.format("Datacenter ID can't be greater than %d or less than 0", maxDatacenterId));
        }
        
        log.info("Snowflake ID生成器初始化完成，workerId: {}, datacenterId: {}", workerId, datacenterId);
    }

    /**
     * 生成随机的workerId
     */
    private long generateRandomWorkerId() {
        return new Random().nextInt((int) maxWorkerId + 1);
    }

    /**
     * 生成随机的datacenterId
     */
    private long generateRandomDatacenterId() {
        return new Random().nextInt((int) maxDatacenterId + 1);
    }

    @Override
    public Result get(String key) {
        // 获取雪花算法生成的ID
        long id = nextId();
        return Result.success(id);
    }

    @Override
    public boolean init() {
        // 已经在@PostConstruct注解的方法中完成初始化
        return true;
    }

    /**
     * 生成下一个ID
     */
    public synchronized long nextId() {
        long timestamp = timeGen();

        // 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
        if (timestamp < lastTimestamp) {
            long offset = lastTimestamp - timestamp;
            if (offset <= 5) {
                // 如果时钟回退在5ms内，等待时钟追上
                try {
                    Thread.sleep(offset << 1);
                    timestamp = timeGen();
                    if (timestamp < lastTimestamp) {
                        throw new RuntimeException(String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } else {
                throw new RuntimeException(String.format("Clock moved backwards. Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
            }
        }

        // 如果是同一时间生成的，则进行毫秒内序列
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & sequenceMask;
            // 毫秒内序列溢出
            if (sequence == 0) {
                // 阻塞到下一个毫秒，获得新的时间戳
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            // 时间戳改变，毫秒内序列重置
            sequence = 0L;
        }

        // 更新上次生成ID的时间戳
        lastTimestamp = timestamp;

        // 移位并通过或运算拼到一起组成64位的ID
        return ((timestamp - twepoch) << timestampLeftShift)
                | (datacenterId << datacenterIdShift)
                | (workerId << workerIdShift)
                | sequence;
    }

    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳
     */
    private long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * 返回以毫秒为单位的当前时间
     */
    private long timeGen() {
        return System.currentTimeMillis();
    }
} 