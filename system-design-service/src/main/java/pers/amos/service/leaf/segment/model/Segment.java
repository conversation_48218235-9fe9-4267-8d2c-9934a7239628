package pers.amos.service.leaf.segment.model;

import lombok.Data;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 号段模型
 * 
 * <AUTHOR>
 */
@Data
public class Segment {
    
    /**
     * 当前值
     */
    private AtomicLong value = new AtomicLong(0);
    
    /**
     * 最大值
     */
    private volatile long max;
    
    /**
     * 步长
     */
    private volatile int step;
    
    /**
     * 业务标签
     */
    private String bizTag;
    
    /**
     * 号段是否正在被初始化
     */
    private volatile boolean isInitializing = false;
    
    /**
     * 获取当前ID并递增
     */
    public long getAndIncrement() {
        return value.getAndIncrement();
    }
    
    /**
     * 是否用完
     */
    public boolean isLeafFinished() {
        return value.get() >= max;
    }
    
    /**
     * 是否需要加载新号段
     * 当前值超过最大值的80%时，需要加载新号段
     */
    public boolean needLoadNewSegment() {
        return value.get() > max - step / 5;
    }
} 