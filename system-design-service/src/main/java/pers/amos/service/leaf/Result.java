package pers.amos.service.leaf;

import lombok.Data;

/**
 * ID生成结果
 * 
 * <AUTHOR>
 */
@Data
public class Result {
    
    /**
     * 生成的ID值
     */
    private long id;
    
    /**
     * 生成状态
     */
    private Status status;
    
    public Result() {
    }
    
    public Result(long id, Status status) {
        this.id = id;
        this.status = status;
    }
    
    /**
     * 创建成功结果
     */
    public static Result success(long id) {
        return new Result(id, Status.SUCCESS);
    }
    
    /**
     * 创建失败结果
     */
    public static Result failed() {
        return new Result(0, Status.EXCEPTION);
    }
    
    /**
     * ID生成结果状态
     */
    public enum Status {
        /**
         * 成功
         */
        SUCCESS,
        
        /**
         * 异常
         */
        EXCEPTION
    }
} 