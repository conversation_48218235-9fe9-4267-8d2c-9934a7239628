package pers.amos.service.leaf.segment;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pers.amos.service.leaf.IDGen;
import pers.amos.service.leaf.Result;
import pers.amos.service.leaf.segment.dao.LeafAllocDao;
import pers.amos.service.leaf.segment.model.LeafAlloc;
import pers.amos.service.leaf.segment.model.Segment;
import pers.amos.service.leaf.segment.model.SegmentBuffer;

import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 基于数据库号段模式的ID生成器实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SegmentIDGenImpl implements IDGen {
    
    /**
     * 缓存所有业务的SegmentBuffer
     */
    private Map<String, SegmentBuffer> cache = new ConcurrentHashMap<>();
    
    /**
     * 线程池，用于异步加载下一个号段
     */
    private ExecutorService service = new ThreadPoolExecutor(
            5, 10, 60L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>(10),
            new ThreadFactory() {
                @Override
                public Thread newThread(Runnable r) {
                    Thread t = new Thread(r);
                    t.setName("leaf-segment-update-thread");
                    t.setDaemon(true);
                    return t;
                }
            },
            new ThreadPoolExecutor.DiscardOldestPolicy());
    
    /**
     * 最大重试次数
     */
    private static final int MAX_RETRIES = 3;
    
    @Autowired
    private LeafAllocDao leafAllocDao;
    
    @Override
    public Result get(String key) {
        if (key == null || key.isEmpty()) {
            return Result.failed();
        }
        
        // 获取或创建对应业务的SegmentBuffer
        SegmentBuffer buffer = getSegmentBuffer(key);
        if (buffer == null) {
            return Result.failed();
        }
        
        return getIdFromSegmentBuffer(buffer);
    }
    
    @Override
    public boolean init() {
        log.info("初始化SegmentIDGen...");
        // 初始化时加载所有业务标签的号段到缓存
        try {
            List<String> allTags = leafAllocDao.getAllTags();
            if (allTags == null || allTags.isEmpty()) {
                log.warn("数据库中没有业务标签");
                return false;
            }
            
            for (String tag : allTags) {
                SegmentBuffer buffer = new SegmentBuffer();
                buffer.setBizTag(tag);
                
                // 加载第一个号段到缓冲区
                loadSegment(tag, buffer, true);
                
                cache.put(tag, buffer);
            }
            
            log.info("初始化SegmentIDGen完成, 业务标签数量: {}", cache.size());
            return true;
        } catch (Exception e) {
            log.error("初始化SegmentIDGen异常", e);
            return false;
        }
    }
    
    /**
     * 获取业务对应的SegmentBuffer
     */
    private SegmentBuffer getSegmentBuffer(String key) {
        SegmentBuffer buffer = cache.get(key);
        if (buffer != null) {
            return buffer;
        }
        
        // 如果缓存中没有，则尝试加载
        synchronized (this) {
            buffer = cache.get(key);
            if (buffer != null) {
                return buffer;
            }
            
            // 查询数据库中是否存在该业务标签
            LeafAlloc leafAlloc = leafAllocDao.getLeafAlloc(key);
            if (leafAlloc == null) {
                log.warn("业务标签[{}]不存在", key);
                return null;
            }
            
            // 创建新的SegmentBuffer
            SegmentBuffer newBuffer = new SegmentBuffer();
            newBuffer.setBizTag(key);
            
            // 加载第一个号段到缓冲区
            boolean loadSuccess = loadSegment(key, newBuffer, true);
            if (!loadSuccess) {
                log.warn("加载业务标签[{}]的号段失败", key);
                return null;
            }
            
            cache.put(key, newBuffer);
            return newBuffer;
        }
    }
    
    /**
     * 从SegmentBuffer中获取ID
     */
    private Result getIdFromSegmentBuffer(SegmentBuffer buffer) {
        // 获取读锁
        buffer.getLock().readLock().lock();
        try {
            Segment segment = buffer.getCurrent();
            
            // 如果当前号段已经用完，需要切换到下一个号段
            if (segment.isLeafFinished()) {
                // 释放读锁，获取写锁
                buffer.getLock().readLock().unlock();
                buffer.getLock().writeLock().lock();
                try {
                    // 再次检查，防止在获取写锁的过程中被其他线程修改
                    if (buffer.getCurrent().isLeafFinished()) {
                        // 切换到下一个号段
                        if (buffer.isNextReady()) {
                            buffer.switchPos();
                            buffer.setNextReady(false);
                        } else {
                            // 如果下一个号段没有准备好，直接从数据库加载
                            boolean loadSuccess = loadSegment(buffer.getBizTag(), buffer, true);
                            if (!loadSuccess) {
                                return Result.failed();
                            }
                        }
                    }
                    
                    // 获取当前号段
                    segment = buffer.getCurrent();
                } finally {
                    // 释放写锁，重新获取读锁
                    buffer.getLock().writeLock().unlock();
                    buffer.getLock().readLock().lock();
                }
            }
            
            // 如果当前号段已经到达阈值，异步加载下一个号段
            if (segment.needLoadNewSegment()) {
                if (!buffer.isNextReady() && buffer.getThreadRunning().compareAndSet(false, true)) {
                    // 异步加载下一个号段
                    service.execute(() -> {
                        try {
                            loadSegment(buffer.getBizTag(), buffer, false);
                        } catch (Exception e) {
                            log.warn("异步加载业务[{}]的下一个号段异常", buffer.getBizTag(), e);
                        } finally {
                            buffer.getThreadRunning().set(false);
                        }
                    });
                }
            }
            
            // 获取ID
            long id = segment.getAndIncrement();
            return Result.success(id);
        } finally {
            buffer.getLock().readLock().unlock();
        }
    }
    
    /**
     * 加载号段
     * 
     * @param bizTag 业务标签
     * @param buffer 号段缓冲区
     * @param isCurrent 是否加载当前号段
     * @return 加载结果
     */
    private boolean loadSegment(String bizTag, SegmentBuffer buffer, boolean isCurrent) {
        try {
            // 获取写锁
            buffer.getLock().writeLock().lock();
            
            // 获取号段分配信息
            LeafAlloc leafAlloc = leafAllocDao.getLeafAlloc(bizTag);
            if (leafAlloc == null) {
                log.warn("业务标签[{}]不存在", bizTag);
                return false;
            }
            
            int retryCount = 0;
            while (retryCount < MAX_RETRIES) {
                // 当前最大ID
                long oldMaxId = leafAlloc.getMaxId();
                // 步长
                int step = leafAlloc.getStep();
                
                // 新的最大ID
                long newMaxId = oldMaxId + step;
                
                // 使用乐观锁更新数据库中的最大ID
                boolean updateSuccess = leafAllocDao.updateMaxId(bizTag, newMaxId, oldMaxId);
                
                if (updateSuccess) {
                    // 更新号段信息
                    Segment segment = isCurrent ? buffer.getCurrent() : buffer.getNext();
                    segment.setValue(new AtomicLong(oldMaxId));
                    segment.setMax(newMaxId);
                    segment.setStep(step);
                    segment.setBizTag(bizTag);
                    
                    if (!isCurrent) {
                        buffer.setNextReady(true);
                    }
                    
                    return true;
                }
                
                retryCount++;
                
                // 更新失败，说明有并发更新，重新获取最新值
                log.info("业务[{}]号段更新失败，重试第{}次", bizTag, retryCount);
                leafAlloc = leafAllocDao.getLeafAlloc(bizTag);
                if (leafAlloc == null) {
                    log.warn("业务标签[{}]不存在", bizTag);
                    return false;
                }
            }
            
            log.warn("业务[{}]号段更新重试{}次仍然失败", bizTag, MAX_RETRIES);
            return false;
        } catch (Exception e) {
            log.error("加载业务[{}]的号段异常", bizTag, e);
            return false;
        } finally {
            buffer.getLock().writeLock().unlock();
        }
    }
} 