package pers.amos.service.leaf.segment.dao;

import pers.amos.service.leaf.segment.model.LeafAlloc;

import java.util.List;

/**
 * 号段分配数据库访问接口
 * 
 * <AUTHOR>
 */
public interface LeafAllocDao {
    
    /**
     * 查询所有业务标签
     */
    List<String> getAllTags();
    
    /**
     * 根据业务标签获取号段
     */
    LeafAlloc getLeafAlloc(String tag);
    
    /**
     * 更新最大ID和标记号段已用完
     */
    void updateMaxIdAndSetLeafFinished(String tag, Long maxId);
    
    /**
     * 更新最大ID (带乐观锁)
     * @param tag 业务标签
     * @param newMaxId 新的最大ID
     * @param oldMaxId 当前的最大ID (乐观锁)
     * @return 是否更新成功
     */
    boolean updateMaxId(String tag, Long newMaxId, Long oldMaxId);
    
    /**
     * 全量获取所有业务标签的号段分配
     */
    List<LeafAlloc> getAllLeafAllocs();
} 