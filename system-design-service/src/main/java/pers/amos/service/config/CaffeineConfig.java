package pers.amos.service.config;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * Caffeine缓存配置
 * 
 * <AUTHOR>
 */
@Configuration
public class CaffeineConfig {
    
    @Value("${username.cache.max-size:10000000}")
    private long maxSize;
    
    @Value("${username.cache.expire-after-write:3600}")
    private long expireAfterWrite;
    
    /**
     * 用户名缓存
     * 
     * @return 缓存实例
     */
    @Bean
    public Cache<String, Boolean> usernameCache() {
        return Caffeine.newBuilder()
                .maximumSize(maxSize)
                .expireAfterWrite(expireAfterWrite, TimeUnit.SECONDS)
                .build();
    }
    
    /**
     * 短链接缓存
     * 
     * @return 缓存实例
     */
    @Bean
    public Cache<String, String> shortUrlCache() {
        return Caffeine.newBuilder()
                .maximumSize(100000)
                .expireAfterWrite(1800, TimeUnit.SECONDS)
                .build();
    }
} 