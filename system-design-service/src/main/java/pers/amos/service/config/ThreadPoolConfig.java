package pers.amos.service.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableAsync
public class ThreadPoolConfig {
    
    @Value("${thread-pool.core-size:50}")
    private int corePoolSize;
    
    @Value("${thread-pool.max-size:200}")
    private int maxPoolSize;
    
    @Value("${thread-pool.queue-capacity:1000}")
    private int queueCapacity;
    
    @Value("${thread-pool.keep-alive-seconds:60}")
    private int keepAliveSeconds;
    
    @Value("${thread-pool.thread-name-prefix:short-url-async-}")
    private String threadNamePrefix;
    
    /**
     * 通用异步任务执行器
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix(threadNamePrefix);
        
        // 拒绝策略: 使用调用者所在的线程来执行任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待终止的时间（秒）
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }
} 