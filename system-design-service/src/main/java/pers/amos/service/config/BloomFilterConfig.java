package pers.amos.service.config;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.nio.charset.Charset;

/**
 * 布隆过滤器配置
 * 
 * <AUTHOR>
 */
@Configuration
public class BloomFilterConfig {
    
    @Value("${username.bloomfilter.expected-insertions:10000000000}")
    private long expectedInsertions;
    
    @Value("${username.bloomfilter.fpp:0.001}")
    private double fpp;
    
    /**
     * 用户名布隆过滤器
     * 
     * @return 布隆过滤器实例
     */
    @Bean
    public BloomFilter<String> usernameBloomFilter() {
        return BloomFilter.create(
                Funnels.stringFunnel(Charset.defaultCharset()),
                expectedInsertions,
                fpp
        );
    }
    
    /**
     * 已删除用户名布隆过滤器
     * 用于标记那些已被删除或更新的用户名
     * 
     * @return 布隆过滤器实例
     */
    @Bean
    public BloomFilter<String> deletedUsernameBloomFilter() {
        return BloomFilter.create(
                Funnels.stringFunnel(Charset.defaultCharset()),
                expectedInsertions,
                fpp
        );
    }
} 