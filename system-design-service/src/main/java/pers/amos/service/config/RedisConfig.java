package pers.amos.service.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import pers.amos.service.bulletcomments.manager.BulletCommentManager;
import pers.amos.service.bulletcomments.model.BulletCommentMessage;

/**
 * Redis配置类
 * 
 * <AUTHOR>
 */
@Configuration
public class RedisConfig {
    
    /**
     * 配置RedisTemplate
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 使用Jackson2JsonRedisSerializer序列化和反序列化redis的value值
        Jackson2JsonRedisSerializer<Object> jsonSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        
        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(jsonSerializer);
        
        // Hash的key和value也采用String和Jackson序列化
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(jsonSerializer);
        
        template.afterPropertiesSet();
        return template;
    }
    
    /**
     * 配置StringRedisTemplate，使用String序列化
     */
    @Bean
    public RedisTemplate<String, String> stringRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        StringRedisSerializer stringSerializer = new StringRedisSerializer();
        
        // 使用StringRedisSerializer来序列化和反序列化redis的key和value值
        template.setKeySerializer(stringSerializer);
        template.setValueSerializer(stringSerializer);
        
        // Hash的key和value也采用String序列化
        template.setHashKeySerializer(stringSerializer);
        template.setHashValueSerializer(stringSerializer);
        
        template.afterPropertiesSet();
        return template;
    }
    
    /**
     * 配置无泛型的默认RedisTemplate
     */
    @Bean
    public RedisTemplate redisTemplateWithoutGeneric(RedisConnectionFactory connectionFactory) {
        RedisTemplate template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        
        // 使用Jackson2JsonRedisSerializer序列化和反序列化redis的value值
        Jackson2JsonRedisSerializer jsonSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        
        // 使用StringRedisSerializer来序列化和反序列化redis的key值
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(jsonSerializer);
        
        // Hash的key和value也采用String和Jackson序列化
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(jsonSerializer);
        
        template.afterPropertiesSet();
        return template;
    }


    /**
     * 配置Redis消息监听容器
     * 所有匹配的消息都会通过MessageListenerAdapter调用RedisBulletCommentManager的receiveMessage方法
     */
    //@Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(
            RedisConnectionFactory connectionFactory,
            MessageListenerAdapter roomChannelAdapter,
            MessageListenerAdapter privateMessageAdapter) {

        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);

        // 订阅所有房间频道消息 - 模式匹配bulletComment:channel:*
        container.addMessageListener(roomChannelAdapter, new PatternTopic("bulletComment:channel:*"));

        // 订阅所有私信频道消息 - 模式匹配bulletComment:private:*
        container.addMessageListener(privateMessageAdapter, new PatternTopic("bulletComment:private:*"));

        return container;
    }

    /**
     * 私信消息监听适配器
     * 当接收到私信频道的消息时，调用RedisBulletCommentManager的receivePrivateMessage方法
     */
    //@Bean
    public MessageListenerAdapter privateMessageAdapter(BulletCommentManager manager) {
        MessageListenerAdapter adapter = new MessageListenerAdapter(manager, "receivePrivateMessage");
        // 设置默认的序列化器为StringRedisSerializer
        adapter.setSerializer(new StringRedisSerializer());
        return adapter;
    }

    /**
     * 配置弹幕消息的RedisTemplate
     */
    //@Bean
    public RedisTemplate<String, BulletCommentMessage> bulletCommentRedisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, BulletCommentMessage> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        // 使用Jackson2JsonRedisSerializer来序列化和反序列化
        Jackson2JsonRedisSerializer<BulletCommentMessage> serializer =
                new Jackson2JsonRedisSerializer<>(BulletCommentMessage.class);

        // 值采用json序列化
        template.setValueSerializer(serializer);
        // 键采用字符串序列化
        template.setKeySerializer(new StringRedisSerializer());

        // Hash键值采用相应的序列化方式
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(serializer);
        template.afterPropertiesSet();

        return template;
    }
} 