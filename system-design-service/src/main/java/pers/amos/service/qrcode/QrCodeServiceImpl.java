package pers.amos.service.qrcode;

import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import pers.amos.dal.dataobject.Account;
import pers.amos.dal.repository.AccountRepository;


/**
 * <AUTHOR> wong
 * @create 2024/8/15 17:18
 * @Description
 */
@Service
public class QrCodeServiceImpl implements QrCodeService {

    @Resource
    private AccountRepository accountRepository;

    public Account getAccount() {
        return accountRepository.getById(1);
    }
}
