package pers.amos.service.user;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 布隆过滤器定期重建调度器
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class BloomFilterRebuildScheduler {

    @Resource
    private UserService userService;
    
    /**
     * 定期重建布隆过滤器
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void rebuildBloomFilter() {
        log.info("开始定期重建用户名布隆过滤器...");
        try {
            userService.initBloomFilter();
            log.info("用户名布隆过滤器重建完成");
        } catch (Exception e) {
            log.error("用户名布隆过滤器重建失败", e);
        }
    }
} 