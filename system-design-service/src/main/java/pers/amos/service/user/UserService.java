package pers.amos.service.user;

/**
 * 用户服务接口
 * 
 * <AUTHOR>
 */
public interface UserService {
    
    /**
     * 创建用户
     * 
     * @param username 用户名
     * @param phone 手机号
     * @param email 邮箱
     * @param password 密码
     * @return 创建的用户
     */
    UserDTO createUser(String username, String phone, String email, String password);
    
    /**
     * 检查用户名是否可用
     * 
     * @param username 用户名
     * @return 用户名是否可用
     */
    boolean isUsernameAvailable(String username);
    
    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户信息
     */
    UserDTO getUserByUsername(String username);
    
    /**
     * 更新用户名
     * 
     * @param userId 用户ID
     * @param newUsername 新用户名
     * @return 更新后的用户信息
     */
    UserDTO updateUsername(Long userId, String newUsername);
    
    /**
     * 初始化布隆过滤器
     * 加载所有用户名到布隆过滤器中
     */
    void initBloomFilter();
} 