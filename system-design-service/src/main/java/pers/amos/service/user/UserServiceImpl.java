package pers.amos.service.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.google.common.hash.BloomFilter;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pers.amos.dal.entity.UserDO;
import pers.amos.dal.mapper.UserMapper;
import pers.amos.service.shortkey.RateLimit;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private RedisTemplate<String, Boolean> redisTemplate;

    @Resource
    private Cache<String, Boolean> usernameCache;

    @Resource
    private BloomFilter<String> usernameBloomFilter;

    @Resource
    private BloomFilter<String> deletedUsernameBloomFilter;

    @Resource
    private UsernameShardUtil usernameShardUtil;

    @Value("${username.cache.redis.expire-days:7}")
    private int redisCacheExpireDays;

    private static final String REDIS_USERNAME_KEY_PREFIX = "username:exist:";
    private static final String REDIS_LOCK_KEY_PREFIX = "username:lock:";
    private static final String REDIS_DELETED_USERNAMES_KEY = "username:deleted";
    private static final int LOCK_EXPIRE_SECONDS = 10;

    //@PostConstruct
    @Override
    public void initBloomFilter() {
        log.info("开始初始化用户名布隆过滤器...");

        try {
            // 清空现有布隆过滤器，重新创建
            // 注意：Guava的BloomFilter不支持直接清空，这里只是逻辑上的重建

            // 分批查询所有用户名并加载到布隆过滤器中
            long lastId = 0;
            int batchSize = 10000;
            int totalCount = 0;

            while (true) {
                LambdaQueryWrapper<UserDO> queryWrapper = new LambdaQueryWrapper<UserDO>()
                        .gt(UserDO::getId, lastId)
                        .orderByAsc(UserDO::getId)
                        .last("LIMIT " + batchSize);

                List<UserDO> users = userMapper.selectList(queryWrapper);
                if (users.isEmpty()) {
                    break;
                }

                // 加载到布隆过滤器
                for (UserDO user : users) {
                    if (StringUtils.isNotBlank(user.getUsername())) {
                        usernameBloomFilter.put(user.getUsername());
                        totalCount++;
                    }
                }

                // 更新lastId，继续查询下一批
                lastId = users.get(users.size() - 1).getId();
                log.info("已加载{}个用户名到布隆过滤器", totalCount);
            }

            log.info("用户名布隆过滤器初始化完成，共加载{}个用户名", totalCount);
        } catch (Exception e) {
            log.error("布隆过滤器初始化失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RateLimit(limit = 10000, window = 1)
    public UserDTO createUser(String username, String phone, String email, String password) {
        // 1. 检查用户名是否可用
        if (!isUsernameAvailable(username)) {
            throw new RuntimeException("用户名已存在，请更换其他用户名");
        }

        // 2. 计算用户名分片
        int shardId = usernameShardUtil.getShardId(username);
        String usernameHash = usernameShardUtil.getMd5Hash(username);

        // 3. 创建用户实体
        UserDO userDO = new UserDO();
        userDO.setUsername(username);
        userDO.setUsernameHash(usernameHash);
        userDO.setShardId(shardId);
        userDO.setPhone(phone);
        userDO.setEmail(email);
        userDO.setPassword(password); // 实际应用中应进行加密
        userDO.setCreateTime(new Date());
        userDO.setUpdateTime(new Date());
        userDO.setIsDeleted(0);

        // 4. 保存用户信息
        userMapper.insert(userDO);

        // 5. 将用户名加入布隆过滤器
        usernameBloomFilter.put(username);

        // 6. 更新缓存
        updateUsernameCache(username, true);

        // 7. 转换为DTO并返回
        return convertToDto(userDO);
    }

    @Override
    public boolean isUsernameAvailable(String username) {
        if (StringUtils.isBlank(username)) {
            return false;
        }

        // 1. 检查本地缓存
        Boolean existsInLocalCache = usernameCache.getIfPresent(username);
        if (existsInLocalCache != null && existsInLocalCache) {
            log.info("本地缓存命中，用户名{}已存在", username);
            return false;
        }

        // 2. 检查布隆过滤器
        if (!usernameBloomFilter.mightContain(username)) {
            // 布隆过滤器确定不存在，用户名可用
            log.info("布隆过滤器判断用户名{}一定不存在", username);
            return true;
        }

        // 2.1 检查是否是已删除的用户名
        if (deletedUsernameBloomFilter.mightContain(username)) {
            // 可能是已删除/更新的用户名，检查Redis中的已删除集合
            Boolean isDeleted = redisTemplate.opsForSet().isMember(REDIS_DELETED_USERNAMES_KEY, username);
            if (Boolean.TRUE.equals(isDeleted)) {
                log.info("Redis已删除集合命中，用户名{}已被删除/更新，可能可用", username);
                // 需要查询数据库确认
            }
        }

        // 3. 检查Redis缓存
        String redisKey = REDIS_USERNAME_KEY_PREFIX + username;
        Boolean existsInRedis = (Boolean) redisTemplate.opsForValue().get(redisKey);
        if (existsInRedis != null && existsInRedis) {
            // 用户名已存在，回填本地缓存
            usernameCache.put(username, true);
            log.info("Redis缓存命中，用户名{}已存在", username);
            return false;
        }

        // 4. 计算分片ID并查询数据库
        int shardId = usernameShardUtil.getShardId(username);
        boolean acquiredLock = false;
        String lockKey = REDIS_LOCK_KEY_PREFIX + username;

        try {
            // 获取分布式锁
            acquiredLock = Boolean.TRUE.equals(
                    redisTemplate.opsForValue().setIfAbsent(lockKey, true, LOCK_EXPIRE_SECONDS, TimeUnit.SECONDS)
            );

            // 即使没有获取到锁，也继续查询数据库，只是不会更新缓存
            int count = userMapper.checkUsernameExists(username, shardId);
            boolean exists = count > 0;

            // 如果获取到锁，更新缓存
            if (acquiredLock) {
                updateUsernameCache(username, exists);
            }

            return !exists;
        } finally {
            if (acquiredLock) {
                redisTemplate.delete(lockKey);
            }
        }
    }

    @Override
    public UserDTO getUserByUsername(String username) {
        if (StringUtils.isBlank(username)) {
            return null;
        }

        // 计算分片ID
        int shardId = usernameShardUtil.getShardId(username);

        // 查询数据库
        UserDO userDO = userMapper.findByUsername(username, shardId);
        return convertToDto(userDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RateLimit(limit = 5000, window = 1)
    public UserDTO updateUsername(Long userId, String newUsername) {
        if (StringUtils.isBlank(newUsername)) {
            throw new RuntimeException("新用户名不能为空");
        }

        // 1. 获取旧用户数据
        UserDO userDO = userMapper.selectById(userId);
        if (userDO == null) {
            throw new RuntimeException("用户不存在");
        }

        String oldUsername = userDO.getUsername();

        // 如果新用户名与旧用户名相同，直接返回
        if (oldUsername.equals(newUsername)) {
            return convertToDto(userDO);
        }

        // 2. 验证新用户名可用性
        if (!isUsernameAvailable(newUsername)) {
            throw new RuntimeException("新用户名已存在，请更换其他用户名");
        }

        // 3. 计算新用户名分片
        int shardId = usernameShardUtil.getShardId(newUsername);
        String usernameHash = usernameShardUtil.getMd5Hash(newUsername);

        // 4. 更新数据库
        userDO.setUsername(newUsername);
        userDO.setUsernameHash(usernameHash);
        userDO.setShardId(shardId);
        userDO.setUpdateTime(new Date());
        userMapper.updateById(userDO);

        // 5. 更新缓存和布隆过滤器

        // 将旧用户名标记为已删除
        deletedUsernameBloomFilter.put(oldUsername);

        // 添加旧用户名到已删除集合，有效期7天
        redisTemplate.opsForSet().add(REDIS_DELETED_USERNAMES_KEY, Boolean.valueOf(oldUsername));
        redisTemplate.expire(REDIS_DELETED_USERNAMES_KEY, redisCacheExpireDays, TimeUnit.DAYS);

        // 从缓存中移除旧用户名
        redisTemplate.delete(REDIS_USERNAME_KEY_PREFIX + oldUsername);
        usernameCache.invalidate(oldUsername);

        // 添加新用户名到布隆过滤器和缓存
        usernameBloomFilter.put(newUsername);
        updateUsernameCache(newUsername, true);

        return convertToDto(userDO);
    }

    /**
     * 更新用户名缓存
     *
     * @param username 用户名
     * @param exists   是否存在
     */
    @Async
    protected void updateUsernameCache(String username, boolean exists) {
        // 更新Redis缓存
        String redisKey = REDIS_USERNAME_KEY_PREFIX + username;
        redisTemplate.opsForValue().set(redisKey, exists, redisCacheExpireDays, TimeUnit.DAYS);

        // 更新本地缓存
        if (exists) {
            usernameCache.put(username, true);
        } else {
            usernameCache.invalidate(username);
        }
    }

    /**
     * 转换为DTO
     *
     * @param userDO 用户实体
     * @return 用户DTO
     */
    private UserDTO convertToDto(UserDO userDO) {
        if (userDO == null) {
            return null;
        }

        UserDTO dto = new UserDTO();
        BeanUtils.copyProperties(userDO, dto);
        return dto;
    }
} 