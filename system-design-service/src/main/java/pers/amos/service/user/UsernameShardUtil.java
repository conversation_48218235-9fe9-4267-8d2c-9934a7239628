package pers.amos.service.user;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

/**
 * 用户名分片工具类
 * 
 * <AUTHOR>
 */
@Component
public class UsernameShardUtil {
    
    @Value("${username.shard.count:128}")
    private int shardCount;
    
    /**
     * 根据用户名计算分片ID
     * 
     * @param username 用户名
     * @return 分片ID
     */
    public int getShardId(String username) {
        if (StringUtils.isBlank(username)) {
            throw new IllegalArgumentException("用户名不能为空");
        }
        
        // 计算用户名的哈希值
        String md5 = getMd5Hash(username);
        
        // 取前8位十六进制转为整数，对分片数取模
        int hashCode = Integer.parseUnsignedInt(md5.substring(0, 8), 16);
        return Math.abs(hashCode % shardCount);
    }
    
    /**
     * 计算MD5哈希值
     * 
     * @param input 输入字符串
     * @return MD5哈希值
     */
    public String getMd5Hash(String input) {
        return DigestUtils.md5DigestAsHex(input.toLowerCase().getBytes());
    }
} 