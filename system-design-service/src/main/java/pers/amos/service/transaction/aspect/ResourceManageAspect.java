package pers.amos.service.transaction.aspect;


import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import pers.amos.service.transaction.annotation.ResourceManage;
import pers.amos.service.transaction.manager.GlobalTransactionManager;

import java.lang.reflect.Method;

/**
 * 资源管理切面
 */
@Slf4j
@Aspect
@Component
public class ResourceManageAspect {

    @Resource
    private GlobalTransactionManager globalTransactionManager;
    
    @Around("@annotation(pers.amos.service.transaction.annotation.ResourceManage)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        
        ResourceManage annotation = method.getAnnotation(ResourceManage.class);
        String nodeName = annotation.name();
        
        try {
            // 记录节点开始执行
            globalTransactionManager.recordNodeStart(nodeName);
            
            // 执行目标方法
            Object result = joinPoint.proceed();
            
            // 记录节点执行成功
            globalTransactionManager.recordNodeSuccess();
            
            return result;
        } catch (Exception e) {
            // 记录节点执行失败
            globalTransactionManager.recordNodeFailure(e);
            throw e;
        }
    }
} 