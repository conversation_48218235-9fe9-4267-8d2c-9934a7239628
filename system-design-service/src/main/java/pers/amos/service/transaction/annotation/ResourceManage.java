package pers.amos.service.transaction.annotation;

import java.lang.annotation.*;

/**
 * 资源管理注解，用于标记分布式事务中的节点
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ResourceManage {

    /**
     * 节点名称，需要保证唯一
     */
    String name() default "";

    /**
     * 回滚方法名，当事务失败需要回滚时调用的方法
     * 该方法必须与当前方法在同一个类中
     */
    String rollbackMethod() default "";

    /**
     * 是否需要回滚
     * 设置为true表示当该节点或后续节点失败时，需要调用回滚方法
     */
    boolean needRollback() default false;
} 