package pers.amos.service.transaction.context;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 事务上下文，用于保存整个业务流程中的参数
 */
@Data
public class TransactionContext {
    
    private static final ThreadLocal<TransactionContext> CONTEXT_THREAD_LOCAL = new ThreadLocal<>();
    
    /**
     * 事务链名称
     */
    private String chainName;
    
    /**
     * 事务ID
     */
    private String transactionId;
    
    /**
     * 当前执行的节点
     */
    private String currentNode;
    
    /**
     * 参数存储
     */
    private Map<String, Object> params = new HashMap<>();
    
    /**
     * 获取上下文
     */
    public static TransactionContext get() {
        TransactionContext context = CONTEXT_THREAD_LOCAL.get();
        if (context == null) {
            context = new TransactionContext();
            CONTEXT_THREAD_LOCAL.set(context);
        }
        return context;
    }
    
    /**
     * 设置上下文
     */
    public static void set(TransactionContext context) {
        CONTEXT_THREAD_LOCAL.set(context);
    }
    
    /**
     * 清除上下文
     */
    public static void clear() {
        CONTEXT_THREAD_LOCAL.remove();
    }
    
    /**
     * 存储参数
     */
    public void putParam(String key, Object value) {
        params.put(key, value);
    }
    
    /**
     * 获取参数
     */
    @SuppressWarnings("unchecked")
    public <T> T getParam(String key) {
        return (T) params.get(key);
    }
} 