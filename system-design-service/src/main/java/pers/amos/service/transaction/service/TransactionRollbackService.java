package pers.amos.service.transaction.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import pers.amos.service.transaction.context.TransactionContext;
import pers.amos.service.transaction.model.TransactionLog;
import pers.amos.service.transaction.repository.TransactionLogRepository;
import pers.amos.util.LogUtil;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

/**
 * 事务回滚服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransactionRollbackService {
    
    private final TransactionLogRepository transactionLogRepository;
    private final ObjectMapper objectMapper;
    private final ApplicationContext applicationContext;
    
    /**
     * 执行回滚操作
     * @param rollbackLog 回滚日志
     * @return 是否回滚成功
     */
    public boolean executeRollback(TransactionLog rollbackLog) {
        try {
            LogUtil.info("Executing rollback for node: {}, chain: {}, transaction: {}",
                    rollbackLog.getCurrentNode(), rollbackLog.getChainName(), rollbackLog.getId());
            
            // 更新回滚次数
            rollbackLog.setRollbackCount(rollbackLog.getRollbackCount() + 1);
            transactionLogRepository.update(rollbackLog);
            
            // 准备上下文
            TransactionContext context = new TransactionContext();
            context.setTransactionId(rollbackLog.getId());
            context.setChainName(rollbackLog.getChainName());
            context.setCurrentNode(rollbackLog.getCurrentNode());
            
            // 反序列化参数
            Map<String, Object> params = objectMapper.readValue(
                    rollbackLog.getParams(), 
                    new TypeReference<Map<String, Object>>() {}
            );
            context.getParams().putAll(params);
            
            // 设置上下文
            TransactionContext.set(context);
            
            // 执行回滚方法
            boolean success = invokeRollbackMethod(rollbackLog);
            
            if (success) {
                // 更新状态为回滚成功
                rollbackLog.setRollbackStatus(2); // 回滚成功
                transactionLogRepository.update(rollbackLog);
                
                LogUtil.info("Rollback succeeded for node: {}, chain: {}, transaction: {}", 
                        rollbackLog.getCurrentNode(), rollbackLog.getChainName(), rollbackLog.getId());
                return true;
            } else {
                // 更新状态为回滚失败
                rollbackLog.setRollbackStatus(3); // 回滚失败
                transactionLogRepository.update(rollbackLog);
                
                LogUtil.error("Rollback failed for node: {}, chain: {}, transaction: {}", 
                        rollbackLog.getCurrentNode(), rollbackLog.getChainName(), rollbackLog.getId());
                return false;
            }
        } catch (Exception e) {
            LogUtil.error("Failed to execute rollback", e);
            
            // 更新状态为回滚失败
            rollbackLog.setRollbackStatus(3); // 回滚失败
            rollbackLog.setFailReason(e.getMessage());
            transactionLogRepository.update(rollbackLog);
            
            return false;
        } finally {
            // 清理上下文
            TransactionContext.clear();
        }
    }
    
    /**
     * 调用回滚方法
     */
    private boolean invokeRollbackMethod(TransactionLog rollbackLog) {
        try {
            String className = rollbackLog.getClassName();
            String methodName = rollbackLog.getMethodName();
            
            if (className == null || methodName == null || methodName.isEmpty()) {
                LogUtil.error("Invalid rollback method information for node: {}", rollbackLog.getCurrentNode());
                return false;
            }
            
            // 获取Bean实例
            Class<?> clazz = Class.forName(className);
            Object bean = applicationContext.getBean(clazz);
            
            // 获取方法
            Class<?>[] parameterTypes = objectMapper.readValue(
                    rollbackLog.getParameterTypes(), 
                    new TypeReference<Class<?>[]>() {}
            );
            Method method = clazz.getDeclaredMethod(methodName, parameterTypes);
            
            // 准备方法参数
            TransactionContext context = TransactionContext.get();
            Object[] args = prepareMethodArguments(method, context.getParams());
            
            // 调用方法
            method.invoke(bean, args);
            
            return true;
        } catch (Exception e) {
            LogUtil.error("Failed to invoke rollback method for node: {}", rollbackLog.getCurrentNode(), e);
            return false;
        }
    }
    
    /**
     * 根据方法参数类型准备方法参数
     */
    private Object[] prepareMethodArguments(Method method, Map<String, Object> params) {
        Class<?>[] parameterTypes = method.getParameterTypes();
        Object[] args = new Object[parameterTypes.length];
        
        // 这里需要根据实际情况进行参数准备
        // 简单实现: 直接从context中获取同名参数
        for (int i = 0; i < parameterTypes.length; i++) {
            String paramName = method.getParameters()[i].getName();
            args[i] = params.get(paramName);
        }
        
        return args;
    }
    
    /**
     * 查询需要回滚的事务日志
     * @param limit 限制数量
     * @return 待回滚的事务日志列表
     */
    public List<TransactionLog> findRollbackLogs(int limit) {
        // 这里需要扩展 TransactionLogRepository 接口，添加查询待回滚日志的方法
        // 由于当前是示例实现，直接返回空列表
        return List.of();
    }
} 