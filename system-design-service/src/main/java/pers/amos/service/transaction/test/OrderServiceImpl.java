package pers.amos.service.transaction.test;


import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pers.amos.service.transaction.annotation.ResourceManage;
import pers.amos.service.transaction.context.TransactionContext;
import pers.amos.service.transaction.manager.GlobalTransactionManager;
import pers.amos.util.LogUtil;

/**
 * 订单服务实现，作为分布式事务示例
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderServiceImpl {
    
    private final GlobalTransactionManager globalTransactionManager;
    
    /**
     * 创建订单
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param productId 商品ID
     * @param quantity 数量
     */
    public void createOrder(String orderId, String userId, String productId, int quantity) {
        // 开始事务
        String transactionId = globalTransactionManager.beginTransaction("createOrder");
        
        try {
            // 设置上下文参数
            TransactionContext context = TransactionContext.get();
            context.putParam("orderId", orderId);
            context.putParam("userId", userId);
            context.putParam("productId", productId);
            context.putParam("quantity", quantity);
            
            // 执行创建订单操作
            doCreateOrder(orderId, userId, productId, quantity);
            
        } finally {
            // 结束事务
            globalTransactionManager.endTransaction();
        }
    }
    
    /**
     * 执行创建订单操作
     */
    @ResourceManage(name = "createOrderNode", needRollback = true, rollbackMethod = "rollbackCreateOrder")
    public void doCreateOrder(String orderId, String userId, String productId, int quantity) {
        LogUtil.info("Creating order: orderId={}, userId={}, productId={}, quantity={}",
                orderId, userId, productId, quantity);
        
        // 模拟订单创建操作
    }
    
    /**
     * 回滚创建订单操作
     */
    public void rollbackCreateOrder(String orderId, String userId, String productId, int quantity) {
        LogUtil.info("Rolling back order creation: orderId={}, userId={}, productId={}, quantity={}", 
                orderId, userId, productId, quantity);
        
        // 模拟订单回滚操作，例如将订单状态设置为已取消
    }
    
    /**
     * 扣减库存
     */
    @ResourceManage(name = "reduceInventoryNode", needRollback = true, rollbackMethod = "rollbackReduceInventory")
    public void reduceInventory(String productId, int quantity) {
        LogUtil.info("Reducing inventory: productId={}, quantity={}", productId, quantity);
        
        // 模拟扣减库存操作
        // 可以在这里抛出异常来模拟节点执行失败
        // throw new RuntimeException("Inventory not enough");
    }
    
    /**
     * 回滚扣减库存操作
     */
    public void rollbackReduceInventory(String productId, int quantity) {
        LogUtil.info("Rolling back inventory reduction: productId={}, quantity={}", productId, quantity);
        
        // 模拟库存回滚操作，例如将库存数量恢复
    }
    
    /**
     * 创建支付
     */
    @ResourceManage(name = "createPaymentNode", needRollback = true, rollbackMethod = "rollbackCreatePayment")
    public void createPayment(String orderId, String userId, double amount) {
        LogUtil.info("Creating payment: orderId={}, userId={}, amount={}", orderId, userId, amount);
        
        // 模拟创建支付操作
    }
    
    /**
     * 回滚创建支付操作
     */
    public void rollbackCreatePayment(String orderId, String userId, double amount) {
        LogUtil.info("Rolling back payment creation: orderId={}, userId={}, amount={}", orderId, userId, amount);
        
        // 模拟支付回滚操作，例如退款
    }
} 