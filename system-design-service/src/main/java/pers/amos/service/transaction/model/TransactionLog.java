package pers.amos.service.transaction.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 事务日志
 */
@Data
public class TransactionLog implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 唯一标识
     */
    private String id;
    
    /**
     * 事务链名称
     */
    private String chainName;
    
    /**
     * 当前节点名称
     */
    private String currentNode;
    
    /**
     * 请求参数（JSON字符串）
     */
    private String params;
    
    /**
     * 状态：0-执行中，1-成功，2-失败
     */
    private Integer status;
    
    /**
     * 失败原因
     */
    private String failReason;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 节点对应的类名
     */
    private String className;
    
    /**
     * 节点对应的方法名
     */
    private String methodName;
    
    /**
     * 方法参数类型（JSON格式）
     */
    private String parameterTypes;
    
    /**
     * 是否需要回滚
     */
    private Boolean needRollback;
    
    /**
     * 回滚方法名
     */
    private String rollbackMethod;
    
    /**
     * 回滚状态：0-未回滚，1-回滚中，2-回滚成功，3-回滚失败
     */
    private Integer rollbackStatus;
    
    /**
     * 回滚原因
     */
    private String rollbackReason;
    
    /**
     * 回滚次数
     */
    private Integer rollbackCount;
} 