package pers.amos.service.transaction.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import pers.amos.service.transaction.model.TransactionLog;
import pers.amos.service.transaction.service.TransactionRollbackService;
import pers.amos.util.LogUtil;

import java.util.List;

/**
 * 事务回滚定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransactionRollbackTask {
    
    private final TransactionRollbackService transactionRollbackService;
    
    private static final int MAX_ROLLBACK_COUNT = 3;
    private static final int BATCH_SIZE = 10;
    
    /**
     * 定时执行回滚操作
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000)
    public void executeRollback() {
        LogUtil.info("Start executing rollback tasks");
        
        try {
            // 查询需要回滚的事务日志
            List<TransactionLog> rollbackLogs = transactionRollbackService.findRollbackLogs(BATCH_SIZE);
            
            if (rollbackLogs.isEmpty()) {
                LogUtil.info("No rollback tasks found");
                return;
            }
            
            LogUtil.info("Found {} rollback tasks", rollbackLogs.size());
            
            // 依次执行回滚
            for (TransactionLog log : rollbackLogs) {
                // 检查回滚次数是否超过上限
                if (log.getRollbackCount() >= MAX_ROLLBACK_COUNT) {
                    LogUtil.info("Rollback count exceeded for node: {}, chain: {}, transaction: {}", 
                            log.getCurrentNode(), log.getChainName(), log.getId());
                    continue;
                }
                
                // 执行回滚
                boolean success = transactionRollbackService.executeRollback(log);
                LogUtil.info("Rollback result: {}, node: {}, chain: {}, transaction: {}", 
                        success, log.getCurrentNode(), log.getChainName(), log.getId());
            }
        } catch (Exception e) {
            LogUtil.error("Failed to execute rollback tasks", e);
        }
    }
} 