package pers.amos.service.transaction.repository.impl;

import org.springframework.stereotype.Component;
import pers.amos.service.transaction.model.TransactionLog;
import pers.amos.service.transaction.repository.TransactionLogRepository;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 内存实现的事务日志仓储
 * 注意：这只是一个示例实现，实际生产中应该使用数据库存储
 */
@Component
public class InMemoryTransactionLogRepository implements TransactionLogRepository {
    
    private final Map<String, TransactionLog> logMap = new ConcurrentHashMap<>();
    
    @Override
    public void save(TransactionLog log) {
        logMap.put(log.getId(), log);
    }
    
    @Override
    public void update(TransactionLog log) {
        logMap.put(log.getId(), log);
    }
    
    @Override
    public TransactionLog findById(String id) {
        return logMap.get(id);
    }
    
    @Override
    public List<TransactionLog> findFailedLogs(int limit) {
        return logMap.values().stream()
                .filter(log -> log.getStatus() == 2) // 状态为失败
                .limit(limit)
                .collect(Collectors.toList());
    }
} 