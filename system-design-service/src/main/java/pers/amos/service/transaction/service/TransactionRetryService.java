package pers.amos.service.transaction.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import pers.amos.service.transaction.config.TransactionConfigLoader;
import pers.amos.service.transaction.context.TransactionContext;
import pers.amos.service.transaction.model.TransactionLog;
import pers.amos.service.transaction.repository.TransactionLogRepository;
import pers.amos.util.LogUtil;

import java.lang.reflect.Method;
import java.util.Map;

/**
 * 事务重试服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TransactionRetryService {
    
    private final TransactionLogRepository transactionLogRepository;
    private final TransactionConfigLoader configLoader;
    private final ObjectMapper objectMapper;
    private final ApplicationContext applicationContext;
    
    /**
     * 执行节点重试
     * @param transactionLog 事务日志
     * @return 是否重试成功
     */
    public boolean retryNode(TransactionLog transactionLog) {
        try {
            LogUtil.info("Retrying node, chainName: {}, nodeName: {}, transactionId: {}",
                    transactionLog.getChainName(), transactionLog.getCurrentNode(), transactionLog.getId());
            
            // 更新重试次数
            transactionLog.setRetryCount(transactionLog.getRetryCount() + 1);
            transactionLog.setStatus(0); // 更新为执行中
            transactionLogRepository.update(transactionLog);
            
            // 准备上下文
            TransactionContext context = new TransactionContext();
            context.setTransactionId(transactionLog.getId());
            context.setChainName(transactionLog.getChainName());
            context.setCurrentNode(transactionLog.getCurrentNode());
            
            // 反序列化参数
            Map<String, Object> params = objectMapper.readValue(
                    transactionLog.getParams(), 
                    new TypeReference<Map<String, Object>>() {}
            );
            context.getParams().putAll(params);
            
            // 设置上下文
            TransactionContext.set(context);
            
            // 找到节点对应的服务Bean并执行方法
            boolean success = executeNode(transactionLog.getCurrentNode());
            
            if (success) {
                // 更新状态为成功
                transactionLog.setStatus(1);
                transactionLogRepository.update(transactionLog);
                
                // 继续执行后续节点
                executeNextNode(transactionLog.getChainName(), transactionLog.getCurrentNode());
                
                return true;
            } else {
                // 更新状态为失败
                transactionLog.setStatus(2);
                transactionLogRepository.update(transactionLog);
                return false;
            }
        } catch (Exception e) {
            LogUtil.error("Failed to retry node", e);
            
            // 更新状态为失败
            transactionLog.setStatus(2);
            transactionLog.setFailReason(e.getMessage());
            transactionLogRepository.update(transactionLog);
            
            return false;
        } finally {
            // 清理上下文
            TransactionContext.clear();
        }
    }
    
    /**
     * 执行节点
     * @param nodeName 节点名称
     * @return 是否执行成功
     */
    private boolean executeNode(String nodeName) {
        try {
            TransactionLog txLog = transactionLogRepository.findById(TransactionContext.get().getTransactionId());
            if (log == null) {
                LogUtil.error("Transaction log not found for node: {}", nodeName);
                return false;
            }
            
            // 获取节点对应的类和方法
            String className = txLog.getClassName();
            String methodName = txLog.getMethodName();
            
            if (className == null || methodName == null) {
                LogUtil.error("Class or method information not found for node: {}", nodeName);
                return false;
            }
            
            // 获取Bean实例
            Class<?> clazz = Class.forName(className);
            Object bean = applicationContext.getBean(clazz);
            
            // 获取方法
            Class<?>[] parameterTypes = objectMapper.readValue(
                    txLog.getParameterTypes(),
                    new TypeReference<Class<?>[]>() {}
            );
            Method method = clazz.getDeclaredMethod(methodName, parameterTypes);
            
            // 准备方法参数
            TransactionContext context = TransactionContext.get();
            Object[] args = prepareMethodArguments(method, context.getParams());
            
            // 调用方法
            method.invoke(bean, args);
            
            LogUtil.info("Successfully executed node: {}", nodeName);
            return true;
        } catch (Exception e) {
            LogUtil.error("Failed to execute node: {}", nodeName, e);
            return false;
        }
    }
    
    /**
     * 根据方法参数类型准备方法参数
     */
    private Object[] prepareMethodArguments(Method method, Map<String, Object> params) {
        Class<?>[] parameterTypes = method.getParameterTypes();
        Object[] args = new Object[parameterTypes.length];
        
        // 这里需要根据实际情况进行参数准备
        // 简单实现: 直接从context中获取同名参数
        for (int i = 0; i < parameterTypes.length; i++) {
            String paramName = method.getParameters()[i].getName();
            args[i] = params.get(paramName);
        }
        
        return args;
    }
    
    /**
     * 执行下一个节点
     * @param chainName 链名称
     * @param currentNode 当前节点
     */
    private void executeNextNode(String chainName, String currentNode) {
        String nextNode = configLoader.getNextNode(currentNode, chainName);
        if (nextNode != null) {
            LogUtil.info("Continuing to next node: {}", nextNode);
            executeNode(nextNode);
        } else {
            LogUtil.info("Transaction completed successfully, chainName: {}", chainName);
        }
    }
} 