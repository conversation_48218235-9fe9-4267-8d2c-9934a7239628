package pers.amos.service.transaction.config;


import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.type.filter.RegexPatternTypeFilter;
import org.springframework.stereotype.Component;
import pers.amos.service.transaction.annotation.ResourceManage;
import pers.amos.service.transaction.model.NodeInfo;
import pers.amos.service.transaction.model.TransactionChain;
import pers.amos.util.LogUtil;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 事务配置加载器
 */
@Slf4j
@Component
public class TransactionConfigLoader {

    private static final String CONFIG_FILE = "transaction.json";
    private static final String BASE_PACKAGE = "com.deepinnet.tptradeprod";

    private final ApplicationContext applicationContext;

    /**
     * 事务链配置，key: chainName, value: chain
     */
    private final Map<String, TransactionChain> chainMap = new HashMap<>();

    /**
     * 节点与链名称的映射，key: nodeName, value: chainName列表
     */
    private final Map<String, List<String>> nodeToChains = new HashMap<>();

    /**
     * 节点的下一个节点映射，key: nodeName-chainName, value: 下一个节点名称
     */
    private final Map<String, String> nextNodeMap = new HashMap<>();

    /**
     * 节点信息映射，key: nodeName, value: 节点信息
     */
    private final Map<String, NodeInfo> nodeInfoMap = new HashMap<>();

    public TransactionConfigLoader(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    @PostConstruct
    public void init() {
        try {
            loadConfig();
            scanResourceManageAnnotations();
            buildNodeMappings();
            LogUtil.info("Transaction config loaded successfully, chains: {}", chainMap.keySet());
            LogUtil.info("Node info loaded successfully, nodes: {}", nodeInfoMap.keySet());
        } catch (Exception e) {
            LogUtil.error("Failed to load transaction config", e);
        }
    }

    private void loadConfig() throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        ClassPathResource resource = new ClassPathResource(CONFIG_FILE);

        try (InputStream inputStream = resource.getInputStream()) {
            List<TransactionChain> chains = objectMapper.readValue(
                    inputStream,
                    new TypeReference<List<TransactionChain>>() {
                    }
            );

            for (TransactionChain chain : chains) {
                chainMap.put(chain.getChainName(), chain);
            }
        }
    }

    /**
     * 扫描带有 @ResourceManage 注解的方法
     */
    private void scanResourceManageAnnotations() {
        ClassPathScanningCandidateComponentProvider provider = new ClassPathScanningCandidateComponentProvider(false);
        provider.addIncludeFilter(new RegexPatternTypeFilter(Pattern.compile(".*")));

        Set<BeanDefinition> beanDefinitions = provider.findCandidateComponents(BASE_PACKAGE);

        for (BeanDefinition beanDefinition : beanDefinitions) {
            try {
                String className = beanDefinition.getBeanClassName();
                Class<?> clazz = Class.forName(className);

                // 遍历类的所有方法，查找带有 @ResourceManage 注解的方法
                Method[] methods = clazz.getDeclaredMethods();
                for (Method method : methods) {
                    ResourceManage annotation = method.getAnnotation(ResourceManage.class);
                    if (annotation != null) {
                        String nodeName = annotation.name();
                        if (nodeName.isEmpty()) {
                            continue;
                        }

                        // 创建节点信息对象
                        NodeInfo nodeInfo = new NodeInfo();
                        nodeInfo.setNodeName(nodeName);
                        nodeInfo.setClassName(className);
                        nodeInfo.setMethodName(method.getName());
                        nodeInfo.setParameterTypes(method.getParameterTypes());

                        // 添加回滚相关信息
                        nodeInfo.setNeedRollback(annotation.needRollback());
                        nodeInfo.setRollbackMethod(annotation.rollbackMethod());

                        nodeInfoMap.put(nodeName, nodeInfo);
                        LogUtil.info("Found node: {}, class: {}, method: {}, needRollback: {}, rollbackMethod: {}", 
                                nodeName, className, method.getName(), annotation.needRollback(), annotation.rollbackMethod());
                    }
                }
            } catch (ClassNotFoundException e) {
                LogUtil.error("Failed to load class", e);
            }
        }
    }

    private void buildNodeMappings() {
        chainMap.forEach((chainName, chain) -> {
            List<String> nodes = chain.getNodes();
            if (nodes != null && !nodes.isEmpty()) {
                for (int i = 0; i < nodes.size(); i++) {
                    String nodeName = nodes.get(i);

                    // 构建节点与链的映射
                    nodeToChains.computeIfAbsent(nodeName, k -> new ArrayList<>()).add(chainName);

                    // 构建节点与下一个节点的映射
                    if (i < nodes.size() - 1) {
                        String nextNode = nodes.get(i + 1);
                        nextNodeMap.put(nodeName + "-" + chainName, nextNode);
                    }

                    // 验证节点信息是否存在
                    if (!nodeInfoMap.containsKey(nodeName)) {
                        LogUtil.warn("Node info not found for node: {}", nodeName);
                    }
                }
            }
        });
    }

    /**
     * 根据链名称获取事务链
     */
    public TransactionChain getChain(String chainName) {
        return chainMap.get(chainName);
    }

    /**
     * 获取节点所属的链名称列表
     */
    public List<String> getChainsForNode(String nodeName) {
        return nodeToChains.getOrDefault(nodeName, new ArrayList<>());
    }

    /**
     * 获取节点在指定链中的下一个节点
     */
    public String getNextNode(String nodeName, String chainName) {
        return nextNodeMap.get(nodeName + "-" + chainName);
    }

    /**
     * 判断节点是否是链中的最后一个节点
     */
    public boolean isLastNode(String nodeName, String chainName) {
        return !nextNodeMap.containsKey(nodeName + "-" + chainName);
    }

    /**
     * 获取节点信息
     */
    public NodeInfo getNodeInfo(String nodeName) {
        return nodeInfoMap.get(nodeName);
    }

    /**
     * 获取前置节点
     * @param nodeName 当前节点名称
     * @param chainName 链名称
     * @return 前置节点名称
     */
    public String getPreviousNode(String nodeName, String chainName) {
        TransactionChain chain = chainMap.get(chainName);
        if (chain == null) {
            return null;
        }
        
        List<String> nodes = chain.getNodes();
        for (int i = 1; i < nodes.size(); i++) {
            if (nodes.get(i).equals(nodeName)) {
                return nodes.get(i - 1);
            }
        }
        
        return null;
    }

    /**
     * 获取节点在链中的索引
     * @param nodeName 节点名称
     * @param chainName 链名称
     * @return 索引，如果节点不在链中则返回-1
     */
    public int getNodeIndex(String nodeName, String chainName) {
        TransactionChain chain = chainMap.get(chainName);
        if (chain == null) {
            return -1;
        }
        
        List<String> nodes = chain.getNodes();
        for (int i = 0; i < nodes.size(); i++) {
            if (nodes.get(i).equals(nodeName)) {
                return i;
            }
        }
        
        return -1;
    }

    /**
     * 获取节点之前的所有节点
     * @param nodeName 节点名称
     * @param chainName 链名称
     * @return 前置节点列表，按顺序从后往前排列
     */
    public List<String> getPreviousNodes(String nodeName, String chainName) {
        List<String> result = new ArrayList<>();
        TransactionChain chain = chainMap.get(chainName);
        if (chain == null) {
            return result;
        }
        
        List<String> nodes = chain.getNodes();
        int index = getNodeIndex(nodeName, chainName);
        if (index <= 0) {
            return result;
        }
        
        // 从后往前添加前置节点，便于回滚时按照相反顺序执行
        for (int i = index - 1; i >= 0; i--) {
            result.add(nodes.get(i));
        }
        
        return result;
    }
} 