package pers.amos.service.transaction.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import pers.amos.service.transaction.model.TransactionLog;
import pers.amos.service.transaction.repository.TransactionLogRepository;
import pers.amos.service.transaction.service.TransactionRetryService;
import pers.amos.util.LogUtil;

import java.util.List;

/**
 * 事务重试定时任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TransactionRetryTask {
    
    private final TransactionLogRepository transactionLogRepository;
    private final TransactionRetryService transactionRetryService;
    
    private static final int MAX_RETRY_COUNT = 3;
    private static final int BATCH_SIZE = 10;
    
    /**
     * 定时扫描失败的事务日志并进行重试
     * 每10分钟执行一次
     */
    @Scheduled(fixedRate = 600000)
    public void retryFailedTransactions() {
        LogUtil.info("Start retrying failed transactions");
        
        try {
            // 查询失败的事务日志
            List<TransactionLog> failedLogs = transactionLogRepository.findFailedLogs(BATCH_SIZE);
            
            if (failedLogs.isEmpty()) {
                LogUtil.info("No failed transactions found");
                return;
            }
            
            LogUtil.info("Found {} failed transactions", failedLogs.size());
            
            // 依次进行重试
            for (TransactionLog txLog : failedLogs) {
                // 检查重试次数是否超过上限
                if (txLog.getRetryCount() >= MAX_RETRY_COUNT) {
                    LogUtil.info("Transaction retry count exceeded, chainName: {}, nodeName: {}, transactionId: {}",
                            txLog.getChainName(), txLog.getCurrentNode(), txLog.getId());
                    continue;
                }
                
                // 执行重试
                boolean success = transactionRetryService.retryNode(txLog);
                LogUtil.info("Transaction retry result: {}, chainName: {}, nodeName: {}, transactionId: {}",
                        success, txLog.getChainName(), txLog.getCurrentNode(), txLog.getId());
            }
        } catch (Exception e) {
            LogUtil.error("Failed to retry transactions", e);
        }
    }
} 