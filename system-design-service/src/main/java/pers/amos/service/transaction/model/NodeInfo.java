package pers.amos.service.transaction.model;

import lombok.Data;

/**
 * 节点信息，记录节点对应的类和方法
 */
@Data
public class NodeInfo {
    
    /**
     * 节点名称
     */
    private String nodeName;
    
    /**
     * 类名
     */
    private String className;
    
    /**
     * 方法名
     */
    private String methodName;
    
    /**
     * 方法参数类型
     */
    private Class<?>[] parameterTypes;
    
    /**
     * 是否需要回滚
     */
    private boolean needRollback;
    
    /**
     * 回滚方法名
     */
    private String rollbackMethod;
} 