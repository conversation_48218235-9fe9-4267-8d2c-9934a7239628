package pers.amos.service.transaction.manager;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import pers.amos.service.transaction.config.TransactionConfigLoader;
import pers.amos.service.transaction.context.TransactionContext;
import pers.amos.service.transaction.model.NodeInfo;
import pers.amos.service.transaction.model.TransactionLog;
import pers.amos.service.transaction.repository.TransactionLogRepository;
import pers.amos.util.LogUtil;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 事务管理器
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class GlobalTransactionManager {

    private final TransactionLogRepository transactionLogRepository;
    private final ObjectMapper objectMapper;
    private final TransactionConfigLoader configLoader;

    /**
     * 开始事务
     *
     * @param chainName 事务链名称
     * @return 事务ID
     */
    public String beginTransaction(String chainName) {
        TransactionContext context = TransactionContext.get();
        context.setChainName(chainName);
        String transactionId = UUID.randomUUID().toString().replace("-", "");
        context.setTransactionId(transactionId);

        LogUtil.info("Begin transaction, chainName: {}, transactionId: {}", chainName, transactionId);

        return transactionId;
    }

    /**
     * 记录节点开始执行
     *
     * @param nodeName 节点名称
     */
    // 这个必须是独立的事务，直接提交
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void recordNodeStart(String nodeName) {
        TransactionContext context = TransactionContext.get();
        context.setCurrentNode(nodeName);

        try {
            // 获取节点信息
            NodeInfo nodeInfo = configLoader.getNodeInfo(nodeName);
            if (nodeInfo == null) {
                log.warn("Node info not found for node: {}", nodeName);
                return;
            }

            // 创建事务日志
            TransactionLog log = new TransactionLog();
            log.setId(context.getTransactionId());
            log.setChainName(context.getChainName());
            log.setCurrentNode(nodeName);
            log.setClassName(nodeInfo.getClassName());
            log.setMethodName(nodeInfo.getMethodName());
            log.setParameterTypes(objectMapper.writeValueAsString(nodeInfo.getParameterTypes()));
            log.setParams(objectMapper.writeValueAsString(context.getParams()));
            log.setStatus(0); // 执行中
            log.setRetryCount(0);

            // 设置回滚相关信息
            log.setNeedRollback(nodeInfo.isNeedRollback());
            log.setRollbackMethod(nodeInfo.getRollbackMethod());
            log.setRollbackStatus(0); // 未回滚
            log.setRollbackCount(0);

            log.setCreateTime(new Date());
            log.setUpdateTime(new Date());

            transactionLogRepository.save(log);

            LogUtil.info("Node execution started, chain: {}, node: {}, transactionId: {}",
                    context.getChainName(), nodeName, context.getTransactionId());
        } catch (JsonProcessingException e) {
            LogUtil.error("Failed to serialize transaction context", e);
        }
    }

    /**
     * 记录节点执行成功
     */
    public void recordNodeSuccess() {
        TransactionContext context = TransactionContext.get();
        TransactionLog txLog = transactionLogRepository.findById(context.getTransactionId());

        if (txLog != null) {
            txLog.setStatus(1); // 成功
            txLog.setUpdateTime(new Date());
            transactionLogRepository.update(txLog);

            LogUtil.info("Node execution succeeded, chain: {}, node: {}, transactionId: {}",
                    context.getChainName(), context.getCurrentNode(), context.getTransactionId());
        }
    }

    /**
     * 记录节点执行失败并处理回滚
     *
     * @param e 异常
     */
    public void recordNodeFailure(Exception e) {
        TransactionContext context = TransactionContext.get();
        TransactionLog log = transactionLogRepository.findById(context.getTransactionId());

        if (log != null) {
            log.setStatus(2); // 失败
            log.setFailReason(e.getMessage());
            log.setUpdateTime(new Date());
            transactionLogRepository.update(log);

            LogUtil.error("Node execution failed, chain: {}, node: {}, transactionId: {}, reason: {}",
                    context.getChainName(), context.getCurrentNode(), context.getTransactionId(), e.getMessage());

            // 判断是否需要回滚
            if (log.getNeedRollback()) {
                log.setRollbackStatus(1); // 回滚中
                log.setRollbackReason("Node execution failed: " + e.getMessage());
                transactionLogRepository.update(log);

                // 初始化回滚链
                initRollbackChain(context.getChainName(), context.getCurrentNode(), context.getTransactionId(), e.getMessage());
            }
        }
    }

    /**
     * 初始化回滚链
     *
     * @param chainName     链名称
     * @param currentNode   当前节点
     * @param transactionId 事务ID
     * @param reason        回滚原因
     */
    private void initRollbackChain(String chainName, String currentNode, String transactionId, String reason) {
        // 获取前置节点列表（已经按照回滚顺序排序）
        List<String> previousNodes = configLoader.getPreviousNodes(currentNode, chainName);
        if (previousNodes.isEmpty()) {
            LogUtil.info("No previous nodes to rollback for chain: {}, node: {}", chainName, currentNode);
            return;
        }

        LogUtil.info("Initializing rollback chain for chain: {}, node: {}, previous nodes: {}",
                chainName, currentNode, previousNodes);

        // 创建当前节点的回滚日志
        createRollbackLog(chainName, currentNode, transactionId, reason);

        // 为每个前置节点创建回滚日志
        for (String nodeName : previousNodes) {
            NodeInfo nodeInfo = configLoader.getNodeInfo(nodeName);
            if (nodeInfo != null && nodeInfo.isNeedRollback() && !nodeInfo.getRollbackMethod().isEmpty()) {
                createRollbackLog(chainName, nodeName, transactionId, reason);
            }
        }
    }

    /**
     * 创建回滚日志
     *
     * @param chainName     链名称
     * @param nodeName      节点名称
     * @param transactionId 事务ID
     * @param reason        回滚原因
     */
    private void createRollbackLog(String chainName, String nodeName, String transactionId, String reason) {
        NodeInfo nodeInfo = configLoader.getNodeInfo(nodeName);
        if (nodeInfo == null || !nodeInfo.isNeedRollback() || nodeInfo.getRollbackMethod().isEmpty()) {
            return;
        }

        // 查找原始事务日志
        TransactionLog originalLog = findTransactionLogByNodeAndChain(nodeName, chainName, transactionId);
        if (originalLog == null) {
            log.warn("Original transaction log not found for node: {}, chain: {}, transaction: {}",
                    nodeName, chainName, transactionId);
            return;
        }

        // 创建回滚日志
        TransactionLog rollbackLog = new TransactionLog();
        String rollbackId = UUID.randomUUID().toString().replace("-", "");
        rollbackLog.setId(rollbackId);
        rollbackLog.setChainName(chainName + "_rollback");
        rollbackLog.setCurrentNode(nodeName);
        rollbackLog.setClassName(nodeInfo.getClassName());
        rollbackLog.setMethodName(nodeInfo.getRollbackMethod());
        rollbackLog.setParameterTypes(originalLog.getParameterTypes());
        rollbackLog.setParams(originalLog.getParams());
        rollbackLog.setStatus(0); // 执行中
        rollbackLog.setRetryCount(0);
        rollbackLog.setNeedRollback(false);
        rollbackLog.setRollbackStatus(1); // 回滚中
        rollbackLog.setRollbackReason(reason);
        rollbackLog.setRollbackCount(0);
        rollbackLog.setCreateTime(new Date());
        rollbackLog.setUpdateTime(new Date());

        transactionLogRepository.save(rollbackLog);

        LogUtil.info("Created rollback log for node: {}, chain: {}, original transaction: {}, rollback transaction: {}",
                nodeName, chainName, transactionId, rollbackId);
    }

    /**
     * 根据节点名称、链名称和事务ID查找事务日志
     */
    private TransactionLog findTransactionLogByNodeAndChain(String nodeName, String chainName, String transactionId) {
        // 这里需要扩展 TransactionLogRepository 接口，添加根据节点名称、链名称和事务ID查询的方法
        // 由于当前是示例实现，直接返回null
        return null;
    }

    /**
     * 结束事务
     */
    public void endTransaction() {
        TransactionContext.clear();
    }
} 