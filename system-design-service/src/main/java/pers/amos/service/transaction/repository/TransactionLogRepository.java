package pers.amos.service.transaction.repository;


import pers.amos.service.transaction.model.TransactionLog;

import java.util.List;

/**
 * 事务日志存储接口
 */
public interface TransactionLogRepository {
    
    /**
     * 保存事务日志
     */
    void save(TransactionLog log);
    
    /**
     * 更新事务日志
     */
    void update(TransactionLog log);
    
    /**
     * 根据ID查询事务日志
     */
    TransactionLog findById(String id);
    
    /**
     * 查询失败的事务日志
     */
    List<TransactionLog> findFailedLogs(int limit);
} 