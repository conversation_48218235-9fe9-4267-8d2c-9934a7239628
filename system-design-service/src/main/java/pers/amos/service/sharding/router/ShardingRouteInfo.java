package pers.amos.service.sharding.router;

import java.util.ArrayList;
import java.util.List;

/**
 * 分片路由信息
 * 
 * <AUTHOR>
 */
public class ShardingRouteInfo {
    
    /**
     * 逻辑表名
     */
    private String logicTable;
    
    /**
     * 分片键值
     */
    private Object shardingValue;
    
    /**
     * 实际路由的数据库索引集合
     */
    private List<Integer> databaseIndices;
    
    /**
     * 实际路由的表索引集合
     */
    private List<Integer> tableIndices;
    
    /**
     * 原始SQL
     */
    private String originalSql;
    
    /**
     * 实际执行的SQL列表
     */
    private List<String> actualSqlList;
    
    public ShardingRouteInfo(String logicTable, Object shardingValue) {
        this.logicTable = logicTable;
        this.shardingValue = shardingValue;
        this.databaseIndices = new ArrayList<>();
        this.tableIndices = new ArrayList<>();
        this.actualSqlList = new ArrayList<>();
    }
    
    // Getter and Setter
    
    public String getLogicTable() {
        return logicTable;
    }
    
    public void setLogicTable(String logicTable) {
        this.logicTable = logicTable;
    }
    
    public Object getShardingValue() {
        return shardingValue;
    }
    
    public void setShardingValue(Object shardingValue) {
        this.shardingValue = shardingValue;
    }
    
    public List<Integer> getDatabaseIndices() {
        return databaseIndices;
    }
    
    public void setDatabaseIndices(List<Integer> databaseIndices) {
        this.databaseIndices = databaseIndices;
    }
    
    public List<Integer> getTableIndices() {
        return tableIndices;
    }
    
    public void setTableIndices(List<Integer> tableIndices) {
        this.tableIndices = tableIndices;
    }
    
    public String getOriginalSql() {
        return originalSql;
    }
    
    public void setOriginalSql(String originalSql) {
        this.originalSql = originalSql;
    }
    
    public List<String> getActualSqlList() {
        return actualSqlList;
    }
    
    public void setActualSqlList(List<String> actualSqlList) {
        this.actualSqlList = actualSqlList;
    }
    
    /**
     * 添加路由的数据库和表索引
     * 
     * @param dbIndex 数据库索引
     * @param tableIndex 表索引
     */
    public void addRoute(int dbIndex, int tableIndex) {
        this.databaseIndices.add(dbIndex);
        this.tableIndices.add(tableIndex);
    }
    
    /**
     * 添加实际执行的SQL
     * 
     * @param actualSql 实际SQL
     */
    public void addActualSql(String actualSql) {
        this.actualSqlList.add(actualSql);
    }
} 