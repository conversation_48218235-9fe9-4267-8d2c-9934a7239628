package pers.amos.service.sharding.util;

/**
 * 哈希算法工具类
 * 
 * <AUTHOR>
 */
public class HashUtil {
    
    private static final long FNV_OFFSET_BASIS = 0xcbf29ce484222325L;
    private static final long FNV_PRIME = 0x100000001b3L;
    
    /**
     * FNV-1a哈希算法实现
     * 比默认的hashCode分布更均匀
     * 
     * @param data 输入字符串
     * @return 哈希值
     */
    public static long fnvHash(String data) {
        if (data == null) {
            return 0;
        }
        
        long hash = FNV_OFFSET_BASIS;
        for (int i = 0; i < data.length(); i++) {
            hash ^= data.charAt(i);
            hash *= FNV_PRIME;
        }
        return Math.abs(hash);
    }
    
    /**
     * MurmurHash算法实现
     * 
     * @param data 输入字符串
     * @return 哈希值
     */
    public static long murmurHash(String data) {
        if (data == null) {
            return 0;
        }
        
        byte[] bytes = data.getBytes();
        int length = bytes.length;
        int seed = 0x1234ABCD;
        
        final int m = 0x5bd1e995;
        final int r = 24;
        
        int h = seed ^ length;
        
        int len_4 = length >> 2;
        
        for (int i = 0; i < len_4; i++) {
            int i_4 = i << 2;
            int k = (bytes[i_4] & 0xff) + ((bytes[i_4 + 1] & 0xff) << 8)
                    + ((bytes[i_4 + 2] & 0xff) << 16) + ((bytes[i_4 + 3] & 0xff) << 24);
            
            k *= m;
            k ^= k >>> r;
            k *= m;
            
            h *= m;
            h ^= k;
        }
        
        // 处理剩余字节
        int len_m = len_4 << 2;
        int left = length - len_m;
        
        if (left != 0) {
            if (left >= 3) {
                h ^= (bytes[length - 3] & 0xff) << 16;
            }
            if (left >= 2) {
                h ^= (bytes[length - 2] & 0xff) << 8;
            }
            if (left >= 1) {
                h ^= (bytes[length - 1] & 0xff);
            }
            
            h *= m;
        }
        
        h ^= h >>> 13;
        h *= m;
        h ^= h >>> 15;
        
        return Math.abs((long) h);
    }
} 