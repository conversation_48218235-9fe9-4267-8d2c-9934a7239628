package pers.amos.service.sharding.strategy;

/**
 * 分片策略接口
 * 
 * <AUTHOR>
 */
public interface ShardingStrategy {
    
    /**
     * 计算分库索引
     * 
     * @param shardingValue 分片键值
     * @return 数据库索引
     */
    int calculateDatabaseIndex(Object shardingValue);
    
    /**
     * 计算分表索引
     * 
     * @param shardingValue 分片键值
     * @return 表索引
     */
    int calculateTableIndex(Object shardingValue);
} 