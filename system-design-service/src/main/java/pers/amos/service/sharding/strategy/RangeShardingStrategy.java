package pers.amos.service.sharding.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pers.amos.service.sharding.ShardingConfig;
import pers.amos.service.sharding.util.HashUtil;

/**
 * 范围分片策略
 * 适用于按照ID范围或时间范围分片
 * 
 * <AUTHOR>
 */
@Component
public class RangeShardingStrategy implements ShardingStrategy {
    
    @Autowired
    private ShardingConfig shardingConfig;
    
    @Override
    public int calculateDatabaseIndex(Object shardingValue) {
        if (shardingValue == null) {
            throw new IllegalArgumentException("分片键值不能为空");
        }
        
        // 如果是数字类型，可以直接按范围分片
        if (shardingValue instanceof Number) {
            long value = ((Number) shardingValue).longValue();
            return (int) (value % shardingConfig.getDatabaseCount());
        }
        
        // 否则使用哈希分片
        long hashCode = HashUtil.fnvHash(String.valueOf(shardingValue));
        return (int) (hashCode & (shardingConfig.getDatabaseCount() - 1));
    }
    
    @Override
    public int calculateTableIndex(Object shardingValue) {
        if (shardingValue == null) {
            throw new IllegalArgumentException("分片键值不能为空");
        }
        
        // 如果是数字类型，可以直接按范围分片
        if (shardingValue instanceof Number) {
            long value = ((Number) shardingValue).longValue();
            // 使用范围分片，例如每1000万一个分片
            return (int) (value / 10_000_000 % shardingConfig.getTableCount());
        }
        
        // 否则使用哈希分片
        long hashCode = HashUtil.fnvHash(String.valueOf(shardingValue));
        return (int) (hashCode & (shardingConfig.getTableCount() - 1));
    }
} 