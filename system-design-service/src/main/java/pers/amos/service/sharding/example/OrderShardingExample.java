package pers.amos.service.sharding.example;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Service;
import pers.amos.service.sharding.ShardingService;

import java.util.Arrays;
import java.util.List;

/**
 * 订单分片示例
 * 演示如何使用分片服务
 * 
 * <AUTHOR>
 */
@Service
public class OrderShardingExample {
    
    @Autowired
    private ShardingService shardingService;
    
    /**
     * 根据用户ID查询订单
     * 
     * @param userId 用户ID
     * @return 订单列表
     */
    public List<OrderDTO> findOrdersByUserId(Long userId) {
        String sql = "SELECT * FROM t_order WHERE user_id = " + userId;
        return shardingService.executeQuery(sql, userId, new BeanPropertyRowMapper<>(OrderDTO.class));
    }
    
    /**
     * 根据订单ID查询订单详情
     * 
     * @param orderId 订单ID
     * @return 订单详情
     */
    public OrderDTO findOrderById(Long orderId) {
        String sql = "SELECT * FROM t_order WHERE id = " + orderId;
        List<OrderDTO> orders = shardingService.executeQuery(sql, orderId, new BeanPropertyRowMapper<>(OrderDTO.class));
        return orders.isEmpty() ? null : orders.get(0);
    }
    
    /**
     * 查询商家的所有订单
     * 
     * @param sellerId 商家ID
     * @return 订单列表
     */
    public List<OrderDTO> findOrdersBySellerId(Long sellerId) {
        String sql = "SELECT * FROM t_order WHERE seller_id = " + sellerId;
        return shardingService.executeQuery(sql, sellerId, new BeanPropertyRowMapper<>(OrderDTO.class));
    }
    
    /**
     * 创建订单
     * 
     * @param order 订单信息
     * @return 影响行数
     */
    public int createOrder(OrderDTO order) {
        String sql = String.format(
                "INSERT INTO t_order (id, user_id, seller_id, amount, status, create_time) VALUES (%d, %d, %d, %.2f, '%s', now())",
                order.getId(), order.getUserId(), order.getSellerId(), order.getAmount(), order.getStatus()
        );
        return shardingService.executeUpdate(sql, order.getUserId());
    }
    
    /**
     * 查询多个用户的订单
     * 
     * @param userIds 用户ID列表
     * @return 订单列表
     */
    public List<OrderDTO> findOrdersByUserIds(List<Long> userIds) {
        String sql = "SELECT * FROM t_order WHERE user_id IN (" + String.join(",", 
                userIds.stream().map(Object::toString).toArray(String[]::new)) + ")";
        return shardingService.executeRangeQuery(sql, Arrays.asList(userIds.toArray()), new BeanPropertyRowMapper<>(OrderDTO.class));
    }
    
    /**
     * 订单DTO
     */
    public static class OrderDTO {
        private Long id;
        private Long userId;
        private Long sellerId;
        private Double amount;
        private String status;
        private String createTime;
        
        // Getter and Setter
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public Long getUserId() {
            return userId;
        }
        
        public void setUserId(Long userId) {
            this.userId = userId;
        }
        
        public Long getSellerId() {
            return sellerId;
        }
        
        public void setSellerId(Long sellerId) {
            this.sellerId = sellerId;
        }
        
        public Double getAmount() {
            return amount;
        }
        
        public void setAmount(Double amount) {
            this.amount = amount;
        }
        
        public String getStatus() {
            return status;
        }
        
        public void setStatus(String status) {
            this.status = status;
        }
        
        public String getCreateTime() {
            return createTime;
        }
        
        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }
    }
} 