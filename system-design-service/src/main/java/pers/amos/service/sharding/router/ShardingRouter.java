package pers.amos.service.sharding.router;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pers.amos.service.sharding.ShardingConfig;
import pers.amos.service.sharding.strategy.ShardingStrategy;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 分片路由器
 * 
 * <AUTHOR>
 */
@Component
public class ShardingRouter {
    
    private static final Pattern TABLE_PATTERN = Pattern.compile("FROM\\s+([a-zA-Z0-9_]+)", Pattern.CASE_INSENSITIVE);
    
    @Autowired
    private ShardingConfig shardingConfig;
    
    @Autowired
    private ShardingStrategy hashShardingStrategy;
    
    /**
     * 根据分片键值和SQL生成路由信息
     * 
     * @param sql 原始SQL
     * @param shardingValue 分片键值
     * @return 路由信息
     */
    public ShardingRouteInfo route(String sql, Object shardingValue) {
        // 解析SQL中的表名
        String logicTable = extractTableName(sql);
        if (logicTable == null) {
            throw new IllegalArgumentException("无法从SQL中解析出表名: " + sql);
        }
        
        ShardingRouteInfo routeInfo = new ShardingRouteInfo(logicTable, shardingValue);
        routeInfo.setOriginalSql(sql);
        
        // 计算分片路由
        int dbIndex = hashShardingStrategy.calculateDatabaseIndex(shardingValue);
        int tableIndex = hashShardingStrategy.calculateTableIndex(shardingValue);
        
        routeInfo.addRoute(dbIndex, tableIndex);
        
        // 构建实际执行的SQL
        String actualTable = shardingConfig.getActualTable(logicTable, tableIndex);
        String actualSql = sql.replaceAll("\\b" + logicTable + "\\b", actualTable);
        routeInfo.addActualSql(actualSql);
        
        return routeInfo;
    }
    
    /**
     * 解析SQL中的表名
     * 
     * @param sql SQL语句
     * @return 表名
     */
    private String extractTableName(String sql) {
        Matcher matcher = TABLE_PATTERN.matcher(sql);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
} 