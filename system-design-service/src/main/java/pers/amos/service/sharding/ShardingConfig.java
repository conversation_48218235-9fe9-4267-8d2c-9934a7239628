package pers.amos.service.sharding;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 分库分表配置
 * 
 * <AUTHOR>
 */
@Configuration
public class ShardingConfig {
    
    @Value("${sharding.database.count:32}")
    private int databaseCount;
    
    @Value("${sharding.table.count:128}")
    private int tableCount;
    
    @Value("${sharding.database.prefix:db_}")
    private String databasePrefix;
    
    @Value("${sharding.table.prefix:t_}")
    private String tablePrefix;
    
    public int getDatabaseCount() {
        return databaseCount;
    }
    
    public int getTableCount() {
        return tableCount;
    }
    
    public String getDatabasePrefix() {
        return databasePrefix;
    }
    
    public String getTablePrefix() {
        return tablePrefix;
    }
    
    /**
     * 获取逻辑表名对应的实际表名
     * 
     * @param logicTable 逻辑表名
     * @param tableIndex 表索引
     * @return 实际表名
     */
    public String getActualTable(String logicTable, int tableIndex) {
        return logicTable + "_" + tableIndex;
    }
    
    /**
     * 获取实际数据库名
     * 
     * @param dbIndex 数据库索引
     * @return 实际数据库名
     */
    public String getActualDatabase(int dbIndex) {
        return databasePrefix + dbIndex;
    }
} 