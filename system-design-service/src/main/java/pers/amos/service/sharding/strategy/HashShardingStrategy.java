package pers.amos.service.sharding.strategy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import pers.amos.service.sharding.ShardingConfig;
import pers.amos.service.sharding.util.HashUtil;

/**
 * 哈希分片策略
 * 
 * <AUTHOR>
 */
@Component
public class HashShardingStrategy implements ShardingStrategy {
    
    @Autowired
    private ShardingConfig shardingConfig;
    
    @Override
    public int calculateDatabaseIndex(Object shardingValue) {
        if (shardingValue == null) {
            throw new IllegalArgumentException("分片键值不能为空");
        }
        
        long hashCode = HashUtil.fnvHash(String.valueOf(shardingValue));
        return (int) (hashCode & (shardingConfig.getDatabaseCount() - 1));
    }
    
    @Override
    public int calculateTableIndex(Object shardingValue) {
        if (shardingValue == null) {
            throw new IllegalArgumentException("分片键值不能为空");
        }
        
        long hashCode = HashUtil.fnvHash(String.valueOf(shardingValue));
        return (int) (hashCode & (shardingConfig.getTableCount() - 1));
    }
} 