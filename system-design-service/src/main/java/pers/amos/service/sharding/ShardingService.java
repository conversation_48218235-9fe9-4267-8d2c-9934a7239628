package pers.amos.service.sharding;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;
import pers.amos.service.sharding.executor.ShardingExecutor;
import pers.amos.service.sharding.router.ShardingRouteInfo;
import pers.amos.service.sharding.router.ShardingRouter;

import java.util.List;

/**
 * 分片服务，整合路由和执行
 * 
 * <AUTHOR>
 */
@Service
public class ShardingService {
    
    @Autowired
    private ShardingRouter shardingRouter;
    
    @Autowired
    private ShardingExecutor shardingExecutor;
    
    /**
     * 执行分片查询
     * 
     * @param sql 逻辑SQL
     * @param shardingValue 分片键值
     * @param rowMapper 结果映射
     * @param <T> 返回类型
     * @return 查询结果
     */
    public <T> List<T> executeQuery(String sql, Object shardingValue, RowMapper<T> rowMapper) {
        // 解析SQL并路由
        ShardingRouteInfo routeInfo = shardingRouter.route(sql, shardingValue);
        
        // 执行查询
        return shardingExecutor.executeQuery(routeInfo, rowMapper);
    }
    
    /**
     * 执行分片更新
     * 
     * @param sql 逻辑SQL
     * @param shardingValue 分片键值
     * @return 影响行数
     */
    public int executeUpdate(String sql, Object shardingValue) {
        // 解析SQL并路由
        ShardingRouteInfo routeInfo = shardingRouter.route(sql, shardingValue);
        
        // 执行更新
        return shardingExecutor.executeUpdate(routeInfo);
    }
    
    /**
     * 执行范围查询（可能跨多个分片）
     * 
     * @param sql 逻辑SQL
     * @param shardingValues 多个分片键值
     * @param rowMapper 结果映射
     * @param <T> 返回类型
     * @return 合并后的查询结果
     */
    public <T> List<T> executeRangeQuery(String sql, List<Object> shardingValues, RowMapper<T> rowMapper) {
        if (shardingValues == null || shardingValues.isEmpty()) {
            throw new IllegalArgumentException("分片键值列表不能为空");
        }
        
        // 只有一个值的情况下，退化为普通查询
        if (shardingValues.size() == 1) {
            return executeQuery(sql, shardingValues.get(0), rowMapper);
        }
        
        // 构建多个分片的路由信息，并合并
        ShardingRouteInfo mergedRouteInfo = null;
        
        for (Object shardingValue : shardingValues) {
            ShardingRouteInfo routeInfo = shardingRouter.route(sql, shardingValue);
            
            if (mergedRouteInfo == null) {
                mergedRouteInfo = routeInfo;
            } else {
                // 合并路由信息
                mergedRouteInfo.getDatabaseIndices().addAll(routeInfo.getDatabaseIndices());
                mergedRouteInfo.getTableIndices().addAll(routeInfo.getTableIndices());
                mergedRouteInfo.getActualSqlList().addAll(routeInfo.getActualSqlList());
            }
        }
        
        // 执行并行查询
        return shardingExecutor.executeParallelQuery(mergedRouteInfo, rowMapper);
    }
} 