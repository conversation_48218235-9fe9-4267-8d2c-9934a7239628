package pers.amos.service.sharding.executor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Component;
import pers.amos.service.sharding.ShardingConfig;
import pers.amos.service.sharding.router.ShardingRouteInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 分片执行器
 * 
 * <AUTHOR>
 */
@Component
public class ShardingExecutor {
    
    @Autowired
    private ShardingConfig shardingConfig;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    // 线程池用于并行执行
    private final Executor executor = Executors.newFixedThreadPool(10);
    
    /**
     * 执行查询（单分片）
     * 
     * @param routeInfo 路由信息
     * @param rowMapper 结果映射
     * @param <T> 返回类型
     * @return 查询结果
     */
    public <T> List<T> executeQuery(ShardingRouteInfo routeInfo, RowMapper<T> rowMapper) {
        if (routeInfo.getActualSqlList().isEmpty()) {
            return new ArrayList<>();
        }
        
        String actualSql = routeInfo.getActualSqlList().get(0);
        int dbIndex = routeInfo.getDatabaseIndices().get(0);
        
        // 这里需要根据dbIndex获取对应的数据源，这里简化处理
        return jdbcTemplate.query(actualSql, rowMapper);
    }
    
    /**
     * 执行查询（多分片并行查询）
     * 
     * @param routeInfo 路由信息
     * @param rowMapper 结果映射
     * @param <T> 返回类型
     * @return 查询结果
     */
    public <T> List<T> executeParallelQuery(ShardingRouteInfo routeInfo, RowMapper<T> rowMapper) {
        List<String> sqls = routeInfo.getActualSqlList();
        List<Integer> dbIndices = routeInfo.getDatabaseIndices();
        
        if (sqls.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 创建并行任务
        List<CompletableFuture<List<T>>> futures = new ArrayList<>(sqls.size());
        
        for (int i = 0; i < sqls.size(); i++) {
            final String sql = sqls.get(i);
            final int dbIndex = dbIndices.get(i);
            
            CompletableFuture<List<T>> future = CompletableFuture.supplyAsync(() -> {
                // 这里需要根据dbIndex获取对应的数据源，这里简化处理
                return jdbcTemplate.query(sql, rowMapper);
            }, executor);
            
            futures.add(future);
        }
        
        // 等待所有查询完成并合并结果
        CompletableFuture<List<T>> allResults = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
        ).thenApply(v -> 
            futures.stream()
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .collect(Collectors.toList())
        );
        
        try {
            return allResults.get();
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("执行分片查询失败", e);
        }
    }
    
    /**
     * 执行更新（单分片）
     * 
     * @param routeInfo 路由信息
     * @return 影响行数
     */
    public int executeUpdate(ShardingRouteInfo routeInfo) {
        if (routeInfo.getActualSqlList().isEmpty()) {
            return 0;
        }
        
        String actualSql = routeInfo.getActualSqlList().get(0);
        int dbIndex = routeInfo.getDatabaseIndices().get(0);
        
        // 这里需要根据dbIndex获取对应的数据源，这里简化处理
        return jdbcTemplate.update(actualSql);
    }
} 