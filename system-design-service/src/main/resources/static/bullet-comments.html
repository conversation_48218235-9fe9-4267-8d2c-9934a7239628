<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>弹幕测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .config-panel {
            background: white;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .config-panel h2 {
            margin-top: 0;
            font-size: 18px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .form-row {
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        input, select {
            flex: 1;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            display: inline-block;
            margin-left: 10px;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.disconnected {
            background-color: #ffcccc;
            color: #cc0000;
        }
        .status.connecting {
            background-color: #ffffcc;
            color: #cc9900;
        }
        .status.connected {
            background-color: #ccffcc;
            color: #009900;
        }
        .debug-log {
            background: white;
            border: 1px solid #ddd;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            border-radius: 4px;
            margin-top: 20px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding-bottom: 5px;
            border-bottom: 1px solid #f0f0f0;
        }
        .log-entry.error {
            color: #cc0000;
        }
        .log-entry.info {
            color: #333;
        }
        .log-entry.success {
            color: #009900;
        }
        .log-entry.warn {
            color: #cc9900;
        }
        .video-area {
            width: 100%;
            height: 300px;
            background-color: black;
            position: relative;
            overflow: hidden;
            border-radius: 4px;
        }
        .message-input {
            display: flex;
            margin-top: 10px;
            gap: 10px;
        }
        .message-input input {
            flex: 1;
        }
        .bullet-comment {
            position: absolute;
            color: white;
            font-size: 20px;
            white-space: nowrap;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
            animation: moveLeft 8s linear forwards;
        }
        @keyframes moveLeft {
            from { transform: translateX(100%); }
            to { transform: translateX(-100%); }
        }
    </style>
    <!-- 添加SockJS和STOMP客户端库 -->
    <script src="https://cdn.jsdelivr.net/npm/sockjs-client@1/dist/sockjs.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/stompjs@2.3.3/lib/stomp.min.js"></script>
</head>
<body>
    <h1>WebSocket弹幕测试</h1>
    
    <div class="config-panel">
        <h2>连接设置</h2>
        <div class="form-row">
            <label for="serverUrl">服务器地址:</label>
            <input type="text" id="serverUrl" value="http://localhost:8080/ws-bullet-comments">
        </div>
        <div class="form-row">
            <label for="roomId">房间ID:</label>
            <input type="text" id="roomId" value="room1">
        </div>
        <div class="form-row">
            <label for="userId">用户ID:</label>
            <input type="text" id="userId" value="user123">
        </div>
        <div class="form-row">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="测试用户">
        </div>
        <div class="form-row">
            <button id="connectBtn">连接</button>
            <button id="disconnectBtn" disabled>断开</button>
            <div id="status" class="status disconnected">未连接</div>
        </div>
    </div>
    
    <div class="video-area" id="bulletScreen"></div>
    
    <div class="message-input">
        <input type="text" id="messageInput" placeholder="输入弹幕内容..." disabled>
        <button id="sendBtn" disabled>发送</button>
    </div>
    
    <div class="debug-log" id="logArea"></div>
    
    <script>
        // 获取DOM元素
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const status = document.getElementById('status');
        const logArea = document.getElementById('logArea');
        const bulletScreen = document.getElementById('bulletScreen');
        
        // STOMP客户端
        let stompClient = null;
        
        // 初始化
        function init() {
            // 生成随机用户ID和用户名
            const random = Math.floor(Math.random() * 10000);
            document.getElementById('userId').value = `user${random}`;
            document.getElementById('username').value = `用户${random}`;
            
            // 设置事件监听
            connectBtn.addEventListener('click', connect);
            disconnectBtn.addEventListener('click', disconnect);
            sendBtn.addEventListener('click', sendMessage);
            messageInput.addEventListener('keypress', e => {
                if (e.key === 'Enter') sendMessage();
            });
            
            log('系统准备就绪，请点击"连接"按钮开始测试', 'info');
        }
        
        // 连接WebSocket
        function connect() {
            // 获取参数
            const serverUrl = document.getElementById('serverUrl').value.trim();
            const roomId = document.getElementById('roomId').value.trim();
            const userId = document.getElementById('userId').value.trim();
            const username = document.getElementById('username').value.trim();
            
            if (!serverUrl || !roomId || !userId || !username) {
                log('所有字段都必须填写', 'error');
                return;
            }
            
            try {
                // 更新状态
                updateStatus('connecting');
                log(`正在连接到 ${serverUrl}`, 'info');
                
                // 创建SockJS连接
                const socket = new SockJS(serverUrl);
                
                // 创建STOMP客户端
                stompClient = Stomp.over(socket);
                
                // 配置STOMP客户端 - 禁用调试信息
                stompClient.debug = null;
                
                // 连接WebSocket
                stompClient.connect({}, function() {
                    // 连接成功
                    handleOpen();
                    
                    // 订阅房间消息
                    stompClient.subscribe(`/topic/room/${roomId}`, handleMessage);
                    
                    // 订阅私人消息
                    stompClient.subscribe(`/user/queue/private`, handlePrivateMessage);
                    
                    // 发送加入房间消息
                    sendJoinMessage(roomId, userId, username);
                    
                }, function(error) {
                    // 连接错误
                    handleError(error);
                });
                
            } catch (e) {
                updateStatus('disconnected');
                log(`创建WebSocket失败: ${e.message}`, 'error');
            }
        }
        
        // 发送加入房间消息
        function sendJoinMessage(roomId, userId, username) {
            const joinMessage = {
                userId: userId,
                username: username,
                roomId: roomId,
                timestamp: Date.now()
            };
            
            stompClient.send(`/app/room/${roomId}/join`, {}, JSON.stringify(joinMessage));
            log(`已发送加入房间消息`, 'success');
        }
        
        // 断开连接
        function disconnect() {
            if (stompClient && stompClient.connected) {
                stompClient.disconnect(function() {
                    log('已主动断开连接', 'info');
                    updateStatus('disconnected');
                    updateControls(false);
                });
            }
        }
        
        // 发送消息
        function sendMessage() {
            if (!stompClient || !stompClient.connected) {
                log('WebSocket未连接', 'error');
                return;
            }
            
            const content = messageInput.value.trim();
            if (!content) return;
            
            const roomId = document.getElementById('roomId').value;
            const userId = document.getElementById('userId').value;
            const username = document.getElementById('username').value;
            
            const message = {
                userId: userId,
                username: username,
                roomId: roomId,
                content: content,
                color: getRandomColor(),
                type: 'CHAT',
                timestamp: Date.now()
            };
            
            try {
                stompClient.send(`/app/room/${roomId}/send`, {}, JSON.stringify(message));
                log(`消息已发送: ${content}`, 'success');
                messageInput.value = '';
            } catch (e) {
                log(`发送消息失败: ${e.message}`, 'error');
            }
        }
        
        // WebSocket事件处理函数
        function handleOpen() {
            updateStatus('connected');
            updateControls(true);
            log('WebSocket连接已建立', 'success');
        }
        
        function handleMessage(message) {
            try {
                // 解析消息
                const data = JSON.parse(message.body);
                log(`收到房间消息: ${JSON.stringify(data).substring(0, 100)}...`, 'info');
                
                // 显示弹幕
                if (data.type !== 'SYSTEM') {
                    showBulletComment(data);
                }
            } catch (e) {
                log(`解析消息失败: ${e.message}`, 'error');
            }
        }
        
        function handlePrivateMessage(message) {
            try {
                // 解析消息
                const data = JSON.parse(message.body);
                log(`收到私信消息: ${JSON.stringify(data).substring(0, 100)}...`, 'info');
                
                // 显示私信弹幕（使用特殊样式）
                showBulletComment(data, true);
            } catch (e) {
                log(`解析私信消息失败: ${e.message}`, 'error');
            }
        }
        
        function handleError(error) {
            log(`WebSocket错误: ${error.headers ? error.headers.message : error}`, 'error');
            updateStatus('disconnected');
            updateControls(false);
        }
        
        // 辅助函数
        function log(message, type = 'info') {
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<b>[${time}]</b> ${message}`;
            
            logArea.appendChild(logEntry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function updateStatus(newStatus) {
            status.className = `status ${newStatus}`;
            
            switch (newStatus) {
                case 'connected':
                    status.textContent = '已连接';
                    break;
                case 'connecting':
                    status.textContent = '连接中...';
                    break;
                case 'disconnected':
                    status.textContent = '未连接';
                    break;
            }
        }
        
        function updateControls(connected) {
            connectBtn.disabled = connected;
            disconnectBtn.disabled = !connected;
            messageInput.disabled = !connected;
            sendBtn.disabled = !connected;
            
            document.getElementById('serverUrl').disabled = connected;
            document.getElementById('roomId').disabled = connected;
            document.getElementById('userId').disabled = connected;
            document.getElementById('username').disabled = connected;
        }
        
        function showBulletComment(message, isPrivate = false) {
            const comment = document.createElement('div');
            comment.className = 'bullet-comment';
            
            // 创建用户名和消息内容
            const userSpan = document.createElement('span');
            userSpan.style.color = '#ffcc00';
            userSpan.textContent = `${message.username}: `;
            
            const contentSpan = document.createElement('span');
            contentSpan.textContent = message.content;
            contentSpan.style.color = message.color || getRandomColor();
            
            // 如果是私信，添加特殊样式
            if (isPrivate) {
                comment.style.border = '1px solid #ffcc00';
                comment.style.padding = '2px 5px';
                comment.style.borderRadius = '3px';
                comment.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                
                const privateTag = document.createElement('span');
                privateTag.textContent = '[私信] ';
                privateTag.style.color = '#ff6666';
                comment.appendChild(privateTag);
            }
            
            comment.appendChild(userSpan);
            comment.appendChild(contentSpan);
            
            // 随机垂直位置
            const top = Math.floor(Math.random() * 80) + 10; // 10% 到 90% 的高度
            comment.style.top = `${top}%`;
            
            bulletScreen.appendChild(comment);
            
            // 动画结束后移除
            comment.addEventListener('animationend', function() {
                this.remove();
            });
        }
        
        function getRandomColor() {
            const colors = [
                '#ffffff', // 白色
                '#ff0000', // 红色
                '#00ff00', // 绿色
                '#0000ff', // 蓝色
                '#ffff00', // 黄色
                '#ff00ff', // 粉色
                '#00ffff'  // 青色
            ];
            return colors[Math.floor(Math.random() * colors.length)];
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html> 