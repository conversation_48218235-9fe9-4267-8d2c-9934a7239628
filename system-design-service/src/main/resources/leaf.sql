-- 创建Leaf号段表
CREATE TABLE IF NOT EXISTS `leaf_alloc` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `biz_tag` varchar(128) NOT NULL COMMENT '业务标签',
  `max_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '当前最大ID',
  `step` int(11) NOT NULL COMMENT '步长',
  `description` varchar(256) DEFAULT NULL COMMENT '描述',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_seq_finished` tinyint(1) NOT NULL DEFAULT '0' COMMENT '当前号段是否用完',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_biz_tag` (`biz_tag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Leaf号段表';

-- 插入测试数据
INSERT INTO `leaf_alloc` (`biz_tag`, `max_id`, `step`, `description`, `is_seq_finished`)
VALUES
('user', 1000, 1000, '用户ID', 0),
('order', 1000, 1000, '订单ID', 0),
('product', 1000, 1000, '商品ID', 0),
('shortUrl', 1000, 1000, '短链接ID', 0),
('message', 1000, 1000, '消息ID', 0),
('comment', 1000, 1000, '评论ID', 0),
('feedback', 1000, 1000, '反馈ID', 0),
('log', 1000, 1000, '日志ID', 0); 