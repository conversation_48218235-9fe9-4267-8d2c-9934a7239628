# Leaf分布式ID生成器配置
leaf:
  # ID生成模式，支持SEGMENT和SNOWFLAKE
  mode: SNOWFLAKE
  # 号段模式配置
  segment:
    # 号段缓存大小
    cache-size: 1000
    # 更新阈值，当前号段使用量超过该比例时，异步加载下一个号段
    update-threshold: 0.8
    # 初始步长
    step: 1000
    # 最大步长
    max-step: 10000
    # 步长增长因子
    step-factor: 2.0
    # 从数据库获取号段的重试次数
    retry-times: 3
  # 雪花算法配置
  snowflake:
    # 工作节点ID，-1表示自动分配
    worker-id: -1
    # 数据中心ID，-1表示自动分配
    datacenter-id: -1
    # 时钟回拨最大容忍毫秒数
    max-backward-ms: 5
    # 起始时间戳，默认为2023-01-01 00:00:00
    twepoch: 1672502400000 