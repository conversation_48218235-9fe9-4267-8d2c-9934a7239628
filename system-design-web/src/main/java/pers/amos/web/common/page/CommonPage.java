package pers.amos.web.common.page;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 通用分页数据封装
 * 
 * <AUTHOR>
 */
@Data
public class CommonPage<T> implements Serializable {
    private static final long serialVersionUID = -2207524948286807221L;
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 每页数量
     */
    private Integer pageSize;
    
    /**
     * 总页数
     */
    private Integer totalPage;
    
    /**
     * 总条数
     */
    private Long total;
    
    /**
     * 分页数据
     */
    private List<T> list;

    /**
     * 将PageHelper分页后的list转为分页信息
     */
    public static <T> CommonPage<T> buildPage(Integer pageNum, Integer pageSize, Integer totalPage, Long total, List<T> list) {
        CommonPage<T> result = new CommonPage<>();
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);
        result.setTotalPage(totalPage);
        result.setTotal(total);
        result.setList(list);
        return result;
    }

    /**
     * 返回空分页数据
     */
    public static CommonPage buildEmptyPage() {
        CommonPage result = new CommonPage<>();
        result.setPageNum(0);
        result.setPageSize(0);
        result.setTotalPage(0);
        result.setTotal(0L);
        result.setList(new ArrayList(0));
        return result;
    }

    /**
     * 页内数据转换
     */
    public static <T, R> CommonPage<R> copyMetaWithNewData(CommonPage<T> commonPage, List<R> list) {
        CommonPage<R> result = new CommonPage<>();
        result.setPageNum(commonPage.getPageNum());
        result.setPageSize(commonPage.getPageSize());
        result.setTotalPage(commonPage.getTotalPage());
        result.setTotal(commonPage.getTotal());
        result.setList(list);
        return result;
    }
} 