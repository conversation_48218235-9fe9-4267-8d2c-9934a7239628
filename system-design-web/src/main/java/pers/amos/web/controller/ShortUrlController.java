package pers.amos.web.controller;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import pers.amos.service.shortkey.RateLimit;
import pers.amos.service.shortkey.ShortUrlDTO;
import pers.amos.service.shortkey.ShortUrlService;
import pers.amos.web.common.response.Result;
import pers.amos.web.request.CreateShortUrlRequest;
import pers.amos.web.vo.ShortUrlVO;

import java.io.IOException;

/**
 * 短链接控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/url")
public class ShortUrlController {

    @Autowired
    private ShortUrlService shortUrlService;

    /**
     * 创建短链接
     * 接口限流：每秒5万次请求
     */
    @PostMapping("/shorten")
    @RateLimit(limit = 500, window = 1)
    public Result<ShortUrlVO> createShortUrl(@RequestBody @Validated CreateShortUrlRequest request) {
        try {
            // 创建短链接
            ShortUrlDTO dto = shortUrlService.createShortUrl(
                    request.getLongUrl(),
                    request.getCustomCode(),
                    request.getExpireDays(),
                    request.getDescription(),
                    0L // 这里应该从登录用户中获取，简化为0
            );

            // 转换为VO
            ShortUrlVO vo = convertToVo(dto);

            return Result.success(vo);
        } catch (Exception e) {
            log.error("创建短链接失败", e);
            return Result.fail("URL_CREATE_ERROR", "创建短链接失败: " + e.getMessage());
        }
    }

    /**
     * 短链接跳转
     * 核心接口，不做接口限流，依靠缓存支撑高并发
     */
    @GetMapping("/{shortCode}")
    public void redirect(@PathVariable String shortCode, HttpServletResponse response) {
        try {
            // 获取长链接 - 多级缓存保证高性能
            String longUrl = shortUrlService.getLongUrl(shortCode);

            if (longUrl != null) {
                // 重定向到长链接
                response.setStatus(HttpServletResponse.SC_MOVED_PERMANENTLY);
                response.setHeader("Location", longUrl);
            } else {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.getWriter().write("Short URL not found or expired");
            }
        } catch (Exception e) {
            log.error("短链接跳转失败", e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("Internal Server Error");
            } catch (IOException ex) {
                log.error("Error writing response", ex);
            }
        }
    }

    /**
     * 删除短链接
     * 接口限流：每秒1万次请求
     */
    @DeleteMapping("/{shortCode}")
    @RateLimit(limit = 10000, window = 1)
    public Result<Boolean> deleteShortUrl(@PathVariable String shortCode) {
        try {
            boolean success = shortUrlService.deleteByShortCode(shortCode);
            return Result.success(success);
        } catch (Exception e) {
            log.error("删除短链接失败", e);
            return Result.fail("DELETE_URL_ERROR", "删除短链接失败: " + e.getMessage());
        }
    }

    /**
     * 转换为VO
     */
    private ShortUrlVO convertToVo(ShortUrlDTO dto) {
        if (dto == null) {
            return null;
        }

        ShortUrlVO vo = new ShortUrlVO();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }
} 