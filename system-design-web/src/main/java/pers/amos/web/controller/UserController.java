package pers.amos.web.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import pers.amos.service.shortkey.RateLimit;
import pers.amos.service.user.UserDTO;
import pers.amos.service.user.UserService;
import pers.amos.web.common.response.Result;
import pers.amos.web.request.CreateUserRequest;
import pers.amos.web.request.UpdateUsernameRequest;
import pers.amos.web.vo.UserVO;

/**
 * 用户控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 创建用户
     * 接口限流：每秒5000次请求
     */
    @PostMapping("/register")
    @RateLimit(limit = 5000, window = 1)
    public Result<UserVO> createUser(@RequestBody @Validated CreateUserRequest request) {
        try {
            // 创建用户
            UserDTO userDTO = userService.createUser(
                    request.getUsername(),
                    request.getPhone(),
                    request.getEmail(),
                    request.getPassword()
            );

            // 转换为VO
            UserVO vo = convertToVo(userDTO);

            return Result.success(vo);
        } catch (Exception e) {
            log.error("创建用户失败", e);
            return Result.fail("USER_CREATE_ERROR", "创建用户失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户名是否可用
     * 接口限流：每秒10000次请求
     */
    @GetMapping("/check-username")
    @RateLimit(limit = 10000, window = 1)
    public Result<Boolean> checkUsername(@RequestParam String username) {
        try {
            boolean isAvailable = userService.isUsernameAvailable(username);
            return Result.success(isAvailable);
        } catch (Exception e) {
            log.error("检查用户名失败", e);
            return Result.fail("CHECK_USERNAME_ERROR", "检查用户名失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/{username}")
    public Result<UserVO> getUserByUsername(@PathVariable String username) {
        try {
            UserDTO userDTO = userService.getUserByUsername(username);
            if (userDTO == null) {
                return Result.fail("USER_NOT_FOUND", "用户不存在");
            }
            
            // 转换为VO
            UserVO vo = convertToVo(userDTO);
            
            return Result.success(vo);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.fail("GET_USER_ERROR", "获取用户信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新用户名
     * 接口限流：每秒5000次请求
     */
    @PutMapping("/update-username")
    @RateLimit(limit = 5000, window = 1)
    public Result<UserVO> updateUsername(@RequestBody @Validated UpdateUsernameRequest request) {
        try {
            // 更新用户名
            UserDTO userDTO = userService.updateUsername(
                    request.getUserId(),
                    request.getNewUsername()
            );

            // 转换为VO
            UserVO vo = convertToVo(userDTO);

            return Result.success(vo);
        } catch (Exception e) {
            log.error("更新用户名失败", e);
            return Result.fail("UPDATE_USERNAME_ERROR", "更新用户名失败: " + e.getMessage());
        }
    }

    /**
     * 转换为VO
     */
    private UserVO convertToVo(UserDTO dto) {
        if (dto == null) {
            return null;
        }

        UserVO vo = new UserVO();
        BeanUtils.copyProperties(dto, vo);
        
        // 进行数据脱敏
        if (vo.getPhone() != null && vo.getPhone().length() > 7) {
            vo.setPhone(vo.getPhone().substring(0, 3) + "****" + vo.getPhone().substring(7));
        }
        
        if (vo.getEmail() != null && vo.getEmail().contains("@")) {
            String[] parts = vo.getEmail().split("@");
            if (parts[0].length() > 2) {
                vo.setEmail(parts[0].substring(0, 2) + "***@" + parts[1]);
            }
        }
        
        return vo;
    }
} 