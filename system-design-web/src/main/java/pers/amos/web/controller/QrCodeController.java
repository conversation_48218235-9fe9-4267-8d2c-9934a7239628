package pers.amos.web.controller;

import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import pers.amos.dal.dataobject.Account;
import pers.amos.service.qrcode.QrCodeService;


/**
 * <AUTHOR> wong
 * @create 2024/8/15 17:01
 * @Description
 */
@RequestMapping("/qrcode")
@RestController
public class QrCodeController {

    @Resource
    private QrCodeService qrCodeService;

    @PostMapping("/account")
    public Account getAccount() {
        return qrCodeService.getAccount();
    }
}
