package pers.amos.web.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.URL;

import java.io.Serializable;

/**
 * 创建短链接请求
 * 
 * <AUTHOR>
 */
@Data
public class CreateShortUrlRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 原始长链接
     */
    @NotBlank(message = "长链接不能为空")
    @Size(max = 2048, message = "长链接长度不能超过2048个字符")
    @URL(message = "长链接格式不正确")
    private String longUrl;
    
    /**
     * 自定义短链接码
     */
    @Size(min = 4, max = 10, message = "自定义短链接码长度为4-10个字符")
    @Pattern(regexp = "^[a-zA-Z0-9]+$", message = "自定义短链接码只能包含字母和数字")
    private String customCode;
    
    /**
     * 过期天数
     */
    private Integer expireDays;
    
    /**
     * 描述
     */
    @Size(max = 200, message = "描述长度不能超过200个字符")
    private String description;
} 