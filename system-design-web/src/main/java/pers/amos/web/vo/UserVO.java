package pers.amos.web.vo;

import lombok.Data;

import java.util.Date;

/**
 * 用户视图对象
 * 
 * <AUTHOR>
 */
@Data
public class UserVO {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 手机号（脱敏）
     */
    private String phone;
    
    /**
     * 邮箱（脱敏）
     */
    private String email;
    
    /**
     * 创建时间
     */
    private Date createTime;
} 