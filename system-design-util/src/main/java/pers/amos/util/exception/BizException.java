package pers.amos.util.exception;

import pers.amos.util.ErrorType;

import java.util.HashMap;
import java.util.Map;

public class BizException extends RuntimeException {
    private static final long serialVersionUID = 289153342262221597L;
    protected String errorCode;
    protected String message;
    protected ErrorType errorType;
    protected Map<String, Object> parameters;

    public BizException(String espErrorCode) {
        this(espErrorCode, (String) null, (Map) null, (Throwable) null, (ErrorType) null);
    }

    public BizException(String espErrorCode, Throwable cause) {
        this(espErrorCode, (String) null, (Map) null, cause, (ErrorType) null);
    }

    public BizException(String espErrorCode, String message) {
        this(espErrorCode, message, (Map) null, (Throwable) null, (ErrorType) null);
    }

    public BizException(String espErrorCode, String message, ErrorType errorType) {
        this(espErrorCode, message, (Map) null, (Throwable) null, errorType);
    }

    public BizException(String espErrorCode, String message, Map<String, Object> parameters) {
        this(espErrorCode, message, parameters, (Throwable) null, (ErrorType) null);
    }

    public BizException(String espErrorCode, String message, Throwable cause) {
        this(espErrorCode, message, (Map) null, cause, (ErrorType) null);
    }

    public BizException(String espErrorCode, String message, Map<String, Object> parameters, Throwable cause, ErrorType errorType) {
        super(message, cause);
        this.errorCode = null;
        this.message = null;
        this.errorType = ErrorType.SYSTEM;
        this.errorCode = espErrorCode;
        this.message = message;
        this.parameters = parameters;
        this.errorType = errorType;
    }

    public String getMessage() {
        return this.message;
    }

    public Map<String, Object> getParameters() {
        return this.parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BizException addExtInfo(String key, String value) {
        if (this.parameters == null) {
            this.parameters = new HashMap();
        }

        this.parameters.put(key, value);
        return this;
    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public void setString(String espErrorCode) {
        this.errorCode = espErrorCode;
    }

    public ErrorType getErrorType() {
        return this.errorType;
    }

    public void setErrorType(ErrorType errorType) {
        this.errorType = errorType;
    }
}