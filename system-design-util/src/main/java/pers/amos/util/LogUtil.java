package pers.amos.util;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;

@Slf4j
public class LogUtil {
    public static void info(Logger logger, String format, Object... params) {
        logger.info(format, params);
    }

    /**
     * 打印info级别日志
     *
     * @param format
     * @param args
     */
    public static void info(String format, Object... args) {
        log.info(format, args);
    }

    /**
     * 打印warn级别日志
     *
     * @param format
     * @param args
     */
    public static void warn(Logger logger, String format, Object... args) {
        logger.warn(format, args);
    }

    /**
     * 打印warn级别日志
     *
     * @param format
     * @param args
     */
    public static void warn(String format, Object... args) {
        log.warn(format, args);
    }

    /**
     * 打印error级别日志
     *
     * @param format
     * @param args
     */
    public static void error(Logger logger, String format, Object... args) {
        logger.error(format, args);
    }

    /**
     * 打印error级别日志
     *
     * @param format
     * @param args
     */
    public static void error(String format, Object... args) {
        log.error(format, args);
    }

    /**
     * 打印error级别日志
     *
     * @param message
     * @param throwable
     */
    public static void error(Logger logger, String message, Throwable throwable) {
        logger.error(message, throwable);
    }

    /**
     * 打印error级别日志
     *
     * @param message
     * @param throwable
     */
    public static void error(String message, Throwable throwable) {
        log.error(message, throwable);
    }

}
