# Sentinel熔断与降级原理解析
_Exported on 2025/6/22 at GMT+8 15:01:21 from Cursor (1.1.3)_

---

**User**

DegradeSlot熔断的原理

---

DegradeSlot 熔断的原理如下：

### Sentinel DegradeSlot 熔断原理

DegradeSlot 是 Sentinel 熔断降级机制的核心实现组件，它负责根据配置的熔断规则对资源进行熔断保护。熔断机制通过统计请求的异常情况来决定是否暂时切断对某个资源的访问，以防止系统被异常请求持续消耗导致崩溃。

#### 1. 熔断触发条件

Sentinel 支持三种熔断策略：

1. **异常比例 (DEGRADE_GRADE_EXCEPTION_RATIO)**：
   - 当资源的异常比例超过阈值（如 50%）时触发熔断
   - 需要在一个统计周期内至少有 minRequestAmount（如 5 个）请求

2. **异常数 (DEGRADE_GRADE_EXCEPTION_COUNT)**：
   - 当资源的异常数超过阈值（如 5 个）时触发熔断
   - 异常数是按分钟统计的

3. **慢调用比例 (DEGRADE_GRADE_RT)**：
   - 当资源的慢调用比例超过阈值（如 50%）时触发熔断
   - 慢调用指的是响应时间超过设定的 RT 阈值（如 100ms）的调用
   - 需要在一个统计周期内至少有 minRequestAmount 请求

#### 2. DegradeSlot 工作流程

1. **数据统计**：
   - DegradeSlot 依赖于 StatisticSlot 提供的统计数据
   - StatisticSlot 会统计资源的成功次数、异常次数、响应时间等指标
   - 这些统计数据保存在滑动窗口中，用于计算各种比例

2. **熔断检查**：
   - 当请求到达 DegradeSlot 时，首先会检查资源是否处于熔断状态
   - 如果资源已经处于熔断状态且未到恢复时间，则直接抛出 DegradeException
   - 如果资源未熔断，则继续向下传递请求

3. **熔断开启**：
   - 如果请求异常或慢调用，会被统计到相应指标中
   - 当统计指标达到熔断标准时，DegradeSlot 会将资源置为熔断状态
   - 熔断状态信息保存在 DegradeRuleManager 管理的熔断器中

4. **半开启状态**：
   - 熔断时长（timeWindow）结束后，熔断器会进入"半开启"状态
   - 在半开启状态下，允许一个请求通过以探测服务是否恢复
   - 如果该请求成功，则熔断器关闭；如果失败，则重新进入熔断状态

#### 3. 具体实现案例

以您项目中配置的熔断规则为例：

```java
DegradeRule rule1 = new DegradeRule();
rule1.setResource("createOrder");
rule1.setGrade(RuleConstant.DEGRADE_GRADE_EXCEPTION_RATIO); // 异常比例
rule1.setCount(0.5); // 异常比例阈值，超过50%触发熔断
rule1.setTimeWindow(10); // 熔断时长，单位为秒
rule1.setMinRequestAmount(5); // 熔断触发的最小请求数
```

当"createOrder"资源在统计周期内处理至少5个请求，且异常比例超过50%时，熔断器会触发熔断，该资源将在10秒内拒绝所有请求。

#### 4. 实际案例分析

从您的代码中可以看到，`SentinelOrderService.createOrder()` 方法模拟了高比例的异常情况：

```java
@SentinelResource(value = "createOrder", blockHandler = "createOrderBlockHandler", fallback = "createOrderFallback")
public Map<String, Object> createOrder(String productId, Integer quantity) {
    // 模拟异常情况，70%的概率发生异常
    throw new RuntimeException("模拟订单创建异常");
}
```

假设使用上述熔断规则，工作流程如下：

1. 用户连续发送10个创建订单请求
2. 约7个请求出现异常，异常比例为70%，超过熔断阈值50%
3. DegradeSlot 检测到异常比例超过阈值，触发熔断
4. 接下来10秒内，所有对 createOrder 的请求均被熔断器直接拦截，不会真正执行业务逻辑
5. 熔断期间，请求会被转发到 blockHandler 方法处理：
   ```java
   public Map<String, Object> createOrderBlockHandler(String productId, Integer quantity, BlockException e) {
       Map<String, Object> result = new HashMap<>();
       result.put("code", 429);
       result.put("message", "订单创建被熔断，请稍后重试");
       return result;
   }
   ```
6. 10秒熔断期结束后，熔断器进入半开状态，允许下一个请求尝试执行
7. 如果该请求仍然失败，熔断器重新进入熔断状态；如果成功，则关闭熔断器

这种机制有效防止了系统因持续异常而崩溃，提供了"自我保护"和"自我恢复"的能力。

```java
DegradeRule rule1 = new DegradeRule();
rule1.setResource("createOrder");
rule1.setGrade(RuleConstant.DEGRADE_GRADE_EXCEPTION_RATIO); // 异常比例
rule1.setCount(0.5); // 异常比例阈值，超过50%触发熔断
rule1.setTimeWindow(10); // 熔断时长，单位为秒
rule1.setMinRequestAmount(5); // 熔断触发的最小请求数
```

```java
@SentinelResource(value = "createOrder", blockHandler = "createOrderBlockHandler", fallback = "createOrderFallback")
public Map<String, Object> createOrder(String productId, Integer quantity) {
    // 模拟异常情况，70%的概率发生异常
    throw new RuntimeException("模拟订单创建异常");
}
```

```java
   public Map<String, Object> createOrderBlockHandler(String productId, Integer quantity, BlockException e) {
       Map<String, Object> result = new HashMap<>();
       result.put("code", 429);
       result.put("message", "订单创建被熔断，请稍后重试");
       return result;
   }
```

---

**User**

OK，基本原理了解，具体的实现源码讲述一下

---

**Cursor**

# Sentinel DegradeSlot 熔断源码实现分析

## 1. DegradeSlot 实现源码分析

DegradeSlot 是 Sentinel 熔断降级机制的核心组件，让我们看一下它的源码实现：

```java
public class DegradeSlot extends AbstractLinkedProcessorSlot<DefaultNode> {
    @Override
    public void entry(Context context, ResourceWrapper resourceWrapper, DefaultNode node, int count, boolean prioritized, Object... args)
        throws Throwable {
        // 检查是否应该被熔断
        if (shouldDegradeByRule(resourceWrapper)) {
            // 如果确认需要熔断，则抛出 DegradeException
            throw new DegradeException(resourceWrapper.getName());
        }
        
        // 如果不需要熔断，则继续执行责任链中的下一个 Slot
        fireEntry(context, resourceWrapper, node, count, prioritized, args);
    }

    @Override
    public void exit(Context context, ResourceWrapper resourceWrapper, int count, Object... args) {
        fireExit(context, resourceWrapper, count, args);
    }

    // 检查资源是否应该被熔断
    private boolean shouldDegradeByRule(ResourceWrapper resource) {
        // 获取资源对应的所有熔断规则
        List<DegradeRule> rules = DegradeRuleManager.getRulesOfResource(resource.getName());
        if (rules == null || rules.isEmpty()) {
            return false;
        }
        
        // 遍历所有熔断规则，任一规则触发熔断则返回 true
        for (DegradeRule rule : rules) {
            // 通过 CircuitBreaker 检查是否应该熔断
            if (!rule.passCheck(context, node)) {
                return true;
            }
        }
        return false;
    }
}
```

## 2. DegradeRule 和熔断检查

DegradeRule 类包含熔断规则的参数和检查逻辑：

```java
public class DegradeRule extends AbstractRule {
    // 熔断策略：异常比例、异常数、慢调用比例
    private int grade = RuleConstant.DEGRADE_GRADE_RT;
    
    // 熔断阈值
    private double count;
    
    // 熔断时长，单位为秒
    private int timeWindow;
    
    // 最小请求数，请求数小于该值时不会触发熔断
    private int minRequestAmount = 5;
    
    // 慢调用比例阈值，仅在慢调用比例模式下有效
    private double slowRatioThreshold = 1.0d;
    
    // 熔断器，由 DegradeRuleManager 初始化
    private transient CircuitBreaker circuitBreaker;
    
    public boolean passCheck(Context context, DefaultNode node) {
        // 委托给对应的熔断器进行检查
        return circuitBreaker != null && circuitBreaker.tryPass(context);
    }
}
```

## 3. DegradeRuleManager 和熔断器管理

DegradeRuleManager 负责管理所有熔断规则和熔断器：

```java
public class DegradeRuleManager {
    // 规则映射表：资源名称 -> 熔断规则列表
    private static volatile Map<String, List<DegradeRule>> ruleMap = new ConcurrentHashMap<>();
    
    // 熔断器映射表：规则 -> 熔断器
    private static final Map<DegradeRule, CircuitBreaker> circuitBreakerMap = new HashMap<>();
    
    // 加载规则时创建对应的熔断器
    public static void loadRules(List<DegradeRule> rules) {
        Map<String, List<DegradeRule>> newRuleMap = new ConcurrentHashMap<>();
        
        // 按资源名分组规则
        for (DegradeRule rule : rules) {
            String resourceName = rule.getResource();
            // ... 数据校验逻辑 ...
            
            // 根据熔断策略创建对应的熔断器
            CircuitBreaker circuitBreaker = null;
            switch (rule.getGrade()) {
                case RuleConstant.DEGRADE_GRADE_RT:
                    circuitBreaker = new SlowRatioCircuitBreaker(rule);
                    break;
                case RuleConstant.DEGRADE_GRADE_EXCEPTION_RATIO:
                    circuitBreaker = new ExceptionRatioCircuitBreaker(rule);
                    break;
                case RuleConstant.DEGRADE_GRADE_EXCEPTION_COUNT:
                    circuitBreaker = new ExceptionCountCircuitBreaker(rule);
                    break;
                default:
                    // 默认为慢调用比例熔断器
                    circuitBreaker = new SlowRatioCircuitBreaker(rule);
                    break;
            }
            
            // 关联规则和熔断器
            rule.setCircuitBreaker(circuitBreaker);
            circuitBreakerMap.put(rule, circuitBreaker);
            
            // 将规则添加到对应资源的列表中
            List<DegradeRule> ruleList = newRuleMap.getOrDefault(resourceName, new ArrayList<>());
            ruleList.add(rule);
            newRuleMap.put(resourceName, ruleList);
        }
        
        // 更新规则映射表
        ruleMap = newRuleMap;
    }
}
```

## 4. CircuitBreaker 接口及实现

CircuitBreaker 接口定义了熔断器的基本行为：

```java
public interface CircuitBreaker {
    // 尝试通过熔断器
    boolean tryPass(Context context);
    
    // 记录一次成功的调用
    void onRequestComplete(Context context, int rt, Throwable error);
}
```

### 4.1 熔断器的基础实现 - AbstractCircuitBreaker

```java
public abstract class AbstractCircuitBreaker implements CircuitBreaker {
    protected final DegradeRule rule;
    protected final int recoveryTimeoutMs;
    
    // 熔断器状态：0-关闭，1-开启，2-半开启
    protected final AtomicInteger state = new AtomicInteger(0);
    
    // 熔断开始时间
    protected volatile long nextRetryTimestamp;
    
    public AbstractCircuitBreaker(DegradeRule rule) {
        this.rule = rule;
        this.recoveryTimeoutMs = rule.getTimeWindow() * 1000;
    }
    
    @Override
    public boolean tryPass(Context context) {
        // 如果熔断器当前状态为关闭，则通过
        if (state.get() == 0) {
            return true;
        }
        
        // 如果熔断时间已过，尝试进入半开启状态
        if (state.get() == 1 && System.currentTimeMillis() >= nextRetryTimestamp) {
            // CAS 设置为半开启状态
            if (state.compareAndSet(1, 2)) {
                return true;
            }
        }
        
        // 如果处于半开启状态且已有请求通过，则拒绝
        if (state.get() == 2) {
            return false;
        }
        
        return false;
    }
    
    // 转换为熔断状态
    protected void transformToOpen() {
        // 如果当前状态不是开启，则尝试转换为开启状态
        if (state.get() != 1 && state.compareAndSet(state.get(), 1)) {
            // 设置下一次重试时间
            nextRetryTimestamp = System.currentTimeMillis() + recoveryTimeoutMs;
        }
    }
    
    // 转换为关闭状态
    protected void transformToClose() {
        state.set(0);
    }
}
```

### 4.2 异常比例熔断器 - ExceptionRatioCircuitBreaker

```java
public class ExceptionRatioCircuitBreaker extends AbstractCircuitBreaker {
    // 滑动时间窗口，用于统计异常比例
    private final LeapArray<MetricBucket> slidingWindow;
    
    @Override
    public void onRequestComplete(Context context, int rt, Throwable error) {
        // 如果处于半开启状态
        if (state.get() == 2) {
            // 如果有异常，转为开启状态；否则转为关闭状态
            if (error != null) {
                transformToOpen();
            } else {
                transformToClose();
            }
            return;
        }
        
        // 如果处于关闭状态且有异常，则记录异常并检查是否需要熔断
        if (state.get() == 0 && error != null) {
            // 获取当前时间窗口的统计数据
            MetricBucket bucket = slidingWindow.currentWindow().value();
            // 统计异常次数和总次数
            long totalCount = bucket.totalCount();
            long exceptionCount = bucket.exception();
            
            // 如果请求数达到下限且异常比例超过阈值，则触发熔断
            if (totalCount >= rule.getMinRequestAmount() && 
                (double)exceptionCount / totalCount >= rule.getCount()) {
                transformToOpen();
            }
        }
    }
}
```

### 4.3 慢调用比例熔断器 - SlowRatioCircuitBreaker

```java
public class SlowRatioCircuitBreaker extends AbstractCircuitBreaker {
    // 滑动时间窗口，用于统计慢调用比例
    private final LeapArray<MetricBucket> slidingWindow;
    
    @Override
    public void onRequestComplete(Context context, int rt, Throwable error) {
        // 如果处于半开启状态
        if (state.get() == 2) {
            // 如果响应时间超出阈值，转为开启状态；否则转为关闭状态
            if (rt > rule.getCount()) {
                transformToOpen();
            } else {
                transformToClose();
            }
            return;
        }
        
        // 如果处于关闭状态，统计慢调用并检查是否需要熔断
        if (state.get() == 0) {
            // 获取当前时间窗口的统计数据
            MetricBucket bucket = slidingWindow.currentWindow().value();
            // 统计慢调用次数和总次数
            long totalCount = bucket.totalCount();
            long slowCount = bucket.slowCount();
            
            // 如果请求数达到下限且慢调用比例超过阈值，则触发熔断
            if (totalCount >= rule.getMinRequestAmount() && 
                (double)slowCount / totalCount >= rule.getSlowRatioThreshold()) {
                transformToOpen();
            }
        }
    }
}
```

### 4.4 异常数熔断器 - ExceptionCountCircuitBreaker

```java
public class ExceptionCountCircuitBreaker extends AbstractCircuitBreaker {
    // 滑动时间窗口，用于统计异常数
    private final LeapArray<MetricBucket> slidingWindow;
    
    @Override
    public void onRequestComplete(Context context, int rt, Throwable error) {
        // 如果处于半开启状态
        if (state.get() == 2) {
            // 如果有异常，转为开启状态；否则转为关闭状态
            if (error != null) {
                transformToOpen();
            } else {
                transformToClose();
            }
            return;
        }
        
        // 如果处于关闭状态且有异常，则记录异常并检查是否需要熔断
        if (state.get() == 0 && error != null) {
            // 统计当前分钟内的异常数
            long exceptionCount = getExceptionCount(TimeUtil.currentTimeMillis());
            
            // 如果异常数超过阈值，则触发熔断
            if (exceptionCount >= rule.getCount()) {
                transformToOpen();
            }
        }
    }
}
```

## 5. 完整熔断流程

现在让我们看一下完整的熔断流程：

1. **入口阶段**：请求通过 Entry API 或 @SentinelResource 注解进入 Sentinel
2. **Slot 链处理**：请求依次经过各个 Slot，包括 DegradeSlot
3. **熔断检查**：DegradeSlot 调用 DegradeRuleManager 获取资源对应的熔断规则
4. **规则判断**：对每个规则，检查对应的 CircuitBreaker 是否允许请求通过
5. **熔断拦截**：如果判断需要熔断，则抛出 DegradeException，请求被拦截
6. **回调处理**：被拦截的请求转向 @SentinelResource 注解指定的 blockHandler 方法
7. **统计反馈**：请求完成后，通过 StatisticSlot 记录统计数据，反馈给 CircuitBreaker
8. **状态转换**：CircuitBreaker 根据统计数据决定是否转换状态（关闭/开启/半开启）

例如，以您项目中的熔断规则为例：

```java
DegradeRule rule1 = new DegradeRule();
rule1.setResource("createOrder");
rule1.setGrade(RuleConstant.DEGRADE_GRADE_EXCEPTION_RATIO); // 异常比例
rule1.setCount(0.5); // 异常比例阈值，超过50%触发熔断
rule1.setTimeWindow(10); // 熔断时长，单位为秒
rule1.setMinRequestAmount(5); // 熔断触发的最小请求数
```

当加载这个规则时，DegradeRuleManager 会创建一个 ExceptionRatioCircuitBreaker，并与该规则关联。当请求调用 "createOrder" 资源时，CircuitBreaker 会统计异常比例，如果在一个统计周期内至少有 5 个请求且异常比例超过 50%，则触发熔断，接下来 10 秒内所有请求都会被直接拦截。

这种设计使得 Sentinel 的熔断机制非常灵活，可以根据不同的业务场景选择不同的熔断策略，有效保护系统稳定性。

```java
public class DegradeSlot extends AbstractLinkedProcessorSlot<DefaultNode> {
    @Override
    public void entry(Context context, ResourceWrapper resourceWrapper, DefaultNode node, int count, boolean prioritized, Object... args)
        throws Throwable {
        // 检查是否应该被熔断
        if (shouldDegradeByRule(resourceWrapper)) {
            // 如果确认需要熔断，则抛出 DegradeException
            throw new DegradeException(resourceWrapper.getName());
        }
        
        // 如果不需要熔断，则继续执行责任链中的下一个 Slot
        fireEntry(context, resourceWrapper, node, count, prioritized, args);
    }

    @Override
    public void exit(Context context, ResourceWrapper resourceWrapper, int count, Object... args) {
        fireExit(context, resourceWrapper, count, args);
    }

    // 检查资源是否应该被熔断
    private boolean shouldDegradeByRule(ResourceWrapper resource) {
        // 获取资源对应的所有熔断规则
        List<DegradeRule> rules = DegradeRuleManager.getRulesOfResource(resource.getName());
        if (rules == null || rules.isEmpty()) {
            return false;
        }
        
        // 遍历所有熔断规则，任一规则触发熔断则返回 true
        for (DegradeRule rule : rules) {
            // 通过 CircuitBreaker 检查是否应该熔断
            if (!rule.passCheck(context, node)) {
                return true;
            }
        }
        return false;
    }
}
```

```java
public class DegradeRule extends AbstractRule {
    // 熔断策略：异常比例、异常数、慢调用比例
    private int grade = RuleConstant.DEGRADE_GRADE_RT;
    
    // 熔断阈值
    private double count;
    
    // 熔断时长，单位为秒
    private int timeWindow;
    
    // 最小请求数，请求数小于该值时不会触发熔断
    private int minRequestAmount = 5;
    
    // 慢调用比例阈值，仅在慢调用比例模式下有效
    private double slowRatioThreshold = 1.0d;
    
    // 熔断器，由 DegradeRuleManager 初始化
    private transient CircuitBreaker circuitBreaker;
    
    public boolean passCheck(Context context, DefaultNode node) {
        // 委托给对应的熔断器进行检查
        return circuitBreaker != null && circuitBreaker.tryPass(context);
    }
}
```

```java
public class DegradeRuleManager {
    // 规则映射表：资源名称 -> 熔断规则列表
    private static volatile Map<String, List<DegradeRule>> ruleMap = new ConcurrentHashMap<>();
    
    // 熔断器映射表：规则 -> 熔断器
    private static final Map<DegradeRule, CircuitBreaker> circuitBreakerMap = new HashMap<>();
    
    // 加载规则时创建对应的熔断器
    public static void loadRules(List<DegradeRule> rules) {
        Map<String, List<DegradeRule>> newRuleMap = new ConcurrentHashMap<>();
        
        // 按资源名分组规则
        for (DegradeRule rule : rules) {
            String resourceName = rule.getResource();
            // ... 数据校验逻辑 ...
            
            // 根据熔断策略创建对应的熔断器
            CircuitBreaker circuitBreaker = null;
            switch (rule.getGrade()) {
                case RuleConstant.DEGRADE_GRADE_RT:
                    circuitBreaker = new SlowRatioCircuitBreaker(rule);
                    break;
                case RuleConstant.DEGRADE_GRADE_EXCEPTION_RATIO:
                    circuitBreaker = new ExceptionRatioCircuitBreaker(rule);
                    break;
                case RuleConstant.DEGRADE_GRADE_EXCEPTION_COUNT:
                    circuitBreaker = new ExceptionCountCircuitBreaker(rule);
                    break;
                default:
                    // 默认为慢调用比例熔断器
                    circuitBreaker = new SlowRatioCircuitBreaker(rule);
                    break;
            }
            
            // 关联规则和熔断器
            rule.setCircuitBreaker(circuitBreaker);
            circuitBreakerMap.put(rule, circuitBreaker);
            
            // 将规则添加到对应资源的列表中
            List<DegradeRule> ruleList = newRuleMap.getOrDefault(resourceName, new ArrayList<>());
            ruleList.add(rule);
            newRuleMap.put(resourceName, ruleList);
        }
        
        // 更新规则映射表
        ruleMap = newRuleMap;
    }
}
```

```java
public interface CircuitBreaker {
    // 尝试通过熔断器
    boolean tryPass(Context context);
    
    // 记录一次成功的调用
    void onRequestComplete(Context context, int rt, Throwable error);
}
```

```java
public abstract class AbstractCircuitBreaker implements CircuitBreaker {
    protected final DegradeRule rule;
    protected final int recoveryTimeoutMs;
    
    // 熔断器状态：0-关闭，1-开启，2-半开启
    protected final AtomicInteger state = new AtomicInteger(0);
    
    // 熔断开始时间
    protected volatile long nextRetryTimestamp;
    
    public AbstractCircuitBreaker(DegradeRule rule) {
        this.rule = rule;
        this.recoveryTimeoutMs = rule.getTimeWindow() * 1000;
    }
    
    @Override
    public boolean tryPass(Context context) {
        // 如果熔断器当前状态为关闭，则通过
        if (state.get() == 0) {
            return true;
        }
        
        // 如果熔断时间已过，尝试进入半开启状态
        if (state.get() == 1 && System.currentTimeMillis() >= nextRetryTimestamp) {
            // CAS 设置为半开启状态
            if (state.compareAndSet(1, 2)) {
                return true;
            }
        }
        
        // 如果处于半开启状态且已有请求通过，则拒绝
        if (state.get() == 2) {
            return false;
        }
        
        return false;
    }
    
    // 转换为熔断状态
    protected void transformToOpen() {
        // 如果当前状态不是开启，则尝试转换为开启状态
        if (state.get() != 1 && state.compareAndSet(state.get(), 1)) {
            // 设置下一次重试时间
            nextRetryTimestamp = System.currentTimeMillis() + recoveryTimeoutMs;
        }
    }
    
    // 转换为关闭状态
    protected void transformToClose() {
        state.set(0);
    }
}
```

```java
public class ExceptionRatioCircuitBreaker extends AbstractCircuitBreaker {
    // 滑动时间窗口，用于统计异常比例
    private final LeapArray<MetricBucket> slidingWindow;
    
    @Override
    public void onRequestComplete(Context context, int rt, Throwable error) {
        // 如果处于半开启状态
        if (state.get() == 2) {
            // 如果有异常，转为开启状态；否则转为关闭状态
            if (error != null) {
                transformToOpen();
            } else {
                transformToClose();
            }
            return;
        }
        
        // 如果处于关闭状态且有异常，则记录异常并检查是否需要熔断
        if (state.get() == 0 && error != null) {
            // 获取当前时间窗口的统计数据
            MetricBucket bucket = slidingWindow.currentWindow().value();
            // 统计异常次数和总次数
            long totalCount = bucket.totalCount();
            long exceptionCount = bucket.exception();
            
            // 如果请求数达到下限且异常比例超过阈值，则触发熔断
            if (totalCount >= rule.getMinRequestAmount() && 
                (double)exceptionCount / totalCount >= rule.getCount()) {
                transformToOpen();
            }
        }
    }
}
```

```java
public class SlowRatioCircuitBreaker extends AbstractCircuitBreaker {
    // 滑动时间窗口，用于统计慢调用比例
    private final LeapArray<MetricBucket> slidingWindow;
    
    @Override
    public void onRequestComplete(Context context, int rt, Throwable error) {
        // 如果处于半开启状态
        if (state.get() == 2) {
            // 如果响应时间超出阈值，转为开启状态；否则转为关闭状态
            if (rt > rule.getCount()) {
                transformToOpen();
            } else {
                transformToClose();
            }
            return;
        }
        
        // 如果处于关闭状态，统计慢调用并检查是否需要熔断
        if (state.get() == 0) {
            // 获取当前时间窗口的统计数据
            MetricBucket bucket = slidingWindow.currentWindow().value();
            // 统计慢调用次数和总次数
            long totalCount = bucket.totalCount();
            long slowCount = bucket.slowCount();
            
            // 如果请求数达到下限且慢调用比例超过阈值，则触发熔断
            if (totalCount >= rule.getMinRequestAmount() && 
                (double)slowCount / totalCount >= rule.getSlowRatioThreshold()) {
                transformToOpen();
            }
        }
    }
}
```

```java
public class ExceptionCountCircuitBreaker extends AbstractCircuitBreaker {
    // 滑动时间窗口，用于统计异常数
    private final LeapArray<MetricBucket> slidingWindow;
    
    @Override
    public void onRequestComplete(Context context, int rt, Throwable error) {
        // 如果处于半开启状态
        if (state.get() == 2) {
            // 如果有异常，转为开启状态；否则转为关闭状态
            if (error != null) {
                transformToOpen();
            } else {
                transformToClose();
            }
            return;
        }
        
        // 如果处于关闭状态且有异常，则记录异常并检查是否需要熔断
        if (state.get() == 0 && error != null) {
            // 统计当前分钟内的异常数
            long exceptionCount = getExceptionCount(TimeUtil.currentTimeMillis());
            
            // 如果异常数超过阈值，则触发熔断
            if (exceptionCount >= rule.getCount()) {
                transformToOpen();
            }
        }
    }
}
```

```java
DegradeRule rule1 = new DegradeRule();
rule1.setResource("createOrder");
rule1.setGrade(RuleConstant.DEGRADE_GRADE_EXCEPTION_RATIO); // 异常比例
rule1.setCount(0.5); // 异常比例阈值，超过50%触发熔断
rule1.setTimeWindow(10); // 熔断时长，单位为秒
rule1.setMinRequestAmount(5); // 熔断触发的最小请求数
```

当加载这个规则时，DegradeRuleManager 会创建一个 ExceptionRatioCircuitBreaker，并与该规则关联。当请求调用 "createOrder" 资源时，CircuitBreaker 会统计异常比例，如果在一个统计周期内至少有 5 个请求且异常比例超过 50%，则触发熔断，接下来 10 秒内所有请求都会被直接拦截。

这种设计使得 Sentinel 的熔断机制非常灵活，可以根据不同的业务场景选择不同的熔断策略，有效保护系统稳定性。
